- [KeyHandle](#keyhandle)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# KeyHandle

## 描述

`KeyHandle`作为按键输入处理的应用层模块。该模块基于HalKey硬件抽象层，负责按键事件的处理和管理，包括按键防抖、短按/长按检测、组合键检测，并通过Event事件系统发布相应的按键事件。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0075    |   左软键功能   |
| MNT0076 | 右软键功能 |
| MNT0077 | 方向键功能 |
| MNT0078 | 启动键功能 |
| MNT0079 | 停止键功能 |
| MNT0080 | 帮助键功能 |
| MNT0081 | 控制权切换功能 |
| MNT0082 | 左方向键+右方向键 |
| MNT0083 | 左方向键+上方向键 |
| MNT0084 | 左方向键+下方向键 |
| MNT0085 | 右方向键+上方向键 |
| MNT0086 | 右方向键+下方向键 |
| MNT0087 | 左方向键+帮助键   |

### 软件需求

1) 应能够检测按键的短按、长按、组合操作
2) 应能够根据按键的操作事件进行相应的响应
3) 应能够实现对按键按下的处理，包括消抖功能、单按键短按、单按键长按、组合键短按的检测功能和相应事件发布功能

### 假设

1) 硬件抽象层HalKey已正确初始化并可正常工作。
2) Event事件系统已初始化并可正常工作。
3) FreeRTOS任务调度系统已正常运行，可提供时间戳功能。
4) 按键矩阵映射已在初始化时正确配置。
5) 系统时钟稳定，可提供准确的毫秒级时间戳。

***

## 平台资源

接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HalKey_eInit()` | 初始化HalKey硬件抽象层模块。 |
| `HalKey_eScanMatrix()` | 扫描按键矩阵，获取当前按键状态。 |
| `Event_ePublish()` | 发布事件到事件系统。 |
| `xTaskGetTickCount()` | 获取FreeRTOS系统时钟计数。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用状态机设计方法，将按键处理分为四个主要阶段：

1. 扫描阶段：调用HalKey模块扫描按键矩阵，获取当前原始按键状态，同时保存前一次的状态用于后续处理。

2. 防抖阶段：对原始按键状态进行防抖处理。当按键状态发生变化时，启动防抖计时；当防抖时间到达后，更新稳定的按键状态。

3. 单键检测阶段：基于稳定的按键状态，检测单键的短按和长按事件。长按支持重复触发功能。同时检查单键是否为当前激活组合键的一部分，避免重复触发。

4. 组合键检测阶段：检测组合键按下事件。当检测到新的组合键时，发布相应的组合键事件，并标记当前激活的组合键状态。

通过这种分层处理方式，可以确保按键事件的准确性和稳定性，同时支持复杂的按键交互逻辑。

### 静态设计

模块的静态设计如下所示：

![类图](Image/KeyHandle_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/KeyHandle_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：

   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `KeyHandle_eInit()` | 2      |
   | `KeyHandle_vRun()` | 1 |
   | `KeyHandle_vProcessStateScan()` | 2 |
   | `KeyHandle_vProcessStateDebounce()` | 6 |
   | `KeyHandle_vProcessStateDetectSingle()` | 12 |
   | `KeyHandle_vProcessStateDetectCombo()` | 7 |
   | `KeyHandle_vTriggerSingleKeyShortPressEvent()` | 12 |
   | `KeyHandle_vTriggerSingleKeyLongPressEvent()` | 12 |
   | `KeyHandle_vTriggerComboKeyShortPressEvent()` | 8 |
   | `KeyHandle_vResetState()` | 1 |
   | `KeyHandle_bIsComboMatch()` | 6 |
   | `KeyHandle_bIsKeyPartOfActiveCombo()` | 6 |
   | `KeyHandle_u32GetTickMs()` | 1 |
   | `KeyHandle_u32GetElapsedTime()` | 3 |

2. 其他测试项：目前无免费工具，暂不列出。

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用STM32CubeIDE搭建测试工程，硬件板卡为安富莱STM32-V5开发板。

   1.2 使用FreeRTOS新建StartKeyHandleTask任务，在其中实现KeyHandle模块的按键处理测试方案。任务调度时间为10ms。

2. 函数测试详细结果

   2.1 KeyHandle_eInit()

      2.1.1 分支1：HalKey初始化失败

      - 测试用例：模拟HalKey_eInit失败
      - 预期结果：返回 `KEYHANDLE_ERROR_E`
      - 测试结果：通过，函数正确处理初始化失败

      2.1.2 分支2：正常初始化

      - 测试用例：HalKey初始化成功
      - 预期结果：返回 `KEYHANDLE_OK_E`，单例实例状态正确设置
      - 测试结果：通过，初始化成功，配置参数正确设置

   2.2 KeyHandle_vRun()

      2.2.1 分支1：正常运行

      - 测试用例：模块已初始化
      - 预期结果：依次执行扫描、防抖、单键检测、组合键检测
      - 测试结果：通过，所有处理阶段正常执行

   2.3 KeyHandle_vProcessStateScan()

      2.3.1 分支1：HalKey扫描成功

      - 测试用例：HalKey_eScanMatrix返回HALKEY_OK_E
      - 预期结果：正确更新当前和前一次按键状态
      - 测试结果：通过，状态更新正确

      2.3.2 分支2：HalKey扫描失败

      - 测试用例：HalKey_eScanMatrix返回错误
      - 预期结果：调用KeyHandle_vResetState重置状态
      - 测试结果：通过，状态正确重置

   2.4 KeyHandle_vProcessStateDebounce()

      2.4.1 分支1：无按键状态变化

      - 测试用例：当前和前一次原始按键状态相同
      - 预期结果：检查防抖时间，更新稳定状态
      - 测试结果：通过，防抖处理正确

      2.4.2 分支2：按键状态发生变化

      - 测试用例：当前和前一次原始按键状态不同
      - 预期结果：重启防抖计时
      - 测试结果：通过，防抖计时正确重启

      2.4.3 分支3：防抖时间未到

      - 测试用例：按键状态变化后防抖时间未达到阈值
      - 预期结果：不更新稳定按键状态
      - 测试结果：通过，稳定状态保持不变

      2.4.4 分支4：防抖时间到达

      - 测试用例：防抖时间达到配置阈值
      - 预期结果：根据原始状态更新稳定按键状态
      - 测试结果：通过，稳定状态正确更新

   2.5 KeyHandle_vProcessStateDetectSingle()

      2.5.1 分支1：新按键按下处理

      - 测试用例：单个按键从未按下变为按下
      - 预期结果：记录按下时间，清除长按标志
      - 测试结果：通过，按键状态正确记录

      2.5.2 分支2：单键长按检测（首次触发）

      - 测试用例：按键持续按下超过长按时间阈值
      - 预期结果：触发长按事件，设置长按处理标志
      - 测试结果：通过，长按事件正确触发

      2.5.3 分支3：单键长按重复触发

      - 测试用例：长按状态下超过重复触发间隔
      - 预期结果：重复触发长按事件，更新触发时间
      - 测试结果：通过，重复触发正确

      2.5.4 分支4：单键短按检测

      - 测试用例：按键在长按时间内释放
      - 预期结果：触发短按事件
      - 测试结果：通过，短按事件正确触发

      2.5.5 分支5：组合键状态下单键处理

      - 测试用例：单键为当前激活组合键的一部分
      - 预期结果：不触发单键事件
      - 测试结果：通过，正确避免冲突

      2.5.6 分支6：按键释放处理

      - 测试用例：按键从按下变为释放
      - 预期结果：清除相关状态标志
      - 测试结果：通过，状态正确清除

   2.6 KeyHandle_vProcessStateDetectCombo()

      2.6.1 分支1：无按键按下

      - 测试用例：当前无任何按键按下
      - 预期结果：清除激活的组合键状态
      - 测试结果：通过，组合键状态正确清除

      2.6.2 分支2：组合键匹配检测

      - 测试用例：当前按键组合匹配配置的组合键
      - 预期结果：触发组合键事件，更新激活状态
      - 测试结果：通过，组合键事件正确触发

      2.6.3 分支3：重复组合键检测

      - 测试用例：相同组合键持续按下
      - 预期结果：不重复触发组合键事件
      - 测试结果：通过，避免重复触发

      2.6.4 分支4：不匹配的按键组合

      - 测试用例：当前按键组合不匹配任何配置
      - 预期结果：不触发任何组合键事件
      - 测试结果：通过，无误触发

   2.7 KeyHandle_vTriggerSingleKeyShortPressEvent()

      2.7.1 分支1-10：各单键短按事件触发

      - 测试用例：依次测试所有10个单键类型
      - 预期结果：正确映射并发布对应的短按事件
      - 测试结果：通过，所有单键短按事件正确发布

      2.7.11 分支11：无效单键类型

      - 测试用例：传入无效的单键类型
      - 预期结果：函数直接返回，不发布事件
      - 测试结果：通过，正确处理无效输入

   2.8 KeyHandle_vTriggerSingleKeyLongPressEvent()

      2.8.1 分支1-10：各单键长按事件触发

      - 测试用例：依次测试所有10个单键类型
      - 预期结果：正确映射并发布对应的长按事件
      - 测试结果：通过，所有单键长按事件正确发布

      2.8.11 分支11：无效单键类型

      - 测试用例：传入无效的单键类型
      - 预期结果：函数直接返回，不发布事件
      - 测试结果：通过，正确处理无效输入

   2.9 KeyHandle_vTriggerComboKeyShortPressEvent()

      2.9.1 分支1-6：各组合键事件触发

      - 测试用例：依次测试所有6个组合键类型
      - 预期结果：正确映射并发布对应的组合键事件
      - 测试结果：通过，所有组合键事件正确发布

      2.9.7 分支7：无效组合键类型

      - 测试用例：传入无效的组合键类型
      - 预期结果：函数直接返回，不发布事件
      - 测试结果：通过，正确处理无效输入

   2.10 KeyHandle_vResetState()

      2.10.1 分支1：正常状态重置

      - 测试用例：调用状态重置函数
      - 预期结果：所有状态变量重置为初始值
      - 测试结果：通过，状态正确重置

   2.11 KeyHandle_bIsComboMatch()

      2.11.1 分支1：空指针检测（组合键配置）

      - 测试用例：传入 `psCombo = NULL_D`
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，函数正确返回错误状态

      2.11.2 分支2：空指针检测（激活按键列表）

      - 测试用例：传入 `aeActiveKeys = NULL_D`
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，函数正确返回错误状态

      2.11.3 分支3：按键数量不匹配

      - 测试用例：当前按键数量与组合键配置不符
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，正确检测数量不匹配

      2.11.4 分支4：按键组合完全匹配

      - 测试用例：当前按键完全匹配组合键配置
      - 预期结果：返回 `TRUE_D`
      - 测试结果：通过，正确检测匹配

      2.11.5 分支5：按键组合部分匹配

      - 测试用例：当前按键部分匹配组合键配置
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，正确检测不完全匹配

   2.12 KeyHandle_bIsKeyPartOfActiveCombo()

      2.12.1 分支1：无激活组合键

      - 测试用例：当前无激活的组合键
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，正确处理无组合键状态

      2.12.2 分支2：单键为激活组合键的一部分

      - 测试用例：检查的单键为当前激活组合键的组成部分
      - 预期结果：返回 `TRUE_D`
      - 测试结果：通过，正确检测组合键关系

      2.12.3 分支3：单键不是激活组合键的一部分

      - 测试用例：检查的单键不是当前激活组合键的组成部分
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，正确检测非组合键关系

   2.13 KeyHandle_u32GetTickMs()

      2.13.1 分支1：正常时间戳获取

      - 测试用例：调用函数获取系统时间戳
      - 预期结果：返回正确的毫秒时间戳
      - 测试结果：通过，时间戳获取正确

   2.14 KeyHandle_u32GetElapsedTime()

      2.14.1 分支1：无溢出情况

      - 测试用例：当前时间大于等于开始时间
      - 预期结果：返回正确的时间差
      - 测试结果：通过，时间差计算正确

      2.14.2 分支2：溢出情况

      - 测试用例：当前时间小于开始时间（32位溢出）
      - 预期结果：返回考虑溢出的正确时间差
      - 测试结果：通过，溢出处理正确

3. 集成测试详细结果

   3.1 单个按键短按触发测试

      3.1.1 测试目标
   
      验证单个按键的短按功能是否正常工作，确保能够正确检测按键按下和释放，并触发相应的短按事件。

      3.1.2 测试步骤
   
      测试程序如下：
      ```c
      void StartKeyHandleTask(void const * argument)
      {
        /* USER CODE BEGIN StartKeyHandleTask */
        KEYHANDLE_STATUS_E eStatus;
        
        /* 初始化KeyHandle模块 */
        eStatus = KeyHandle_eInit();
        
        if (eStatus != KEYHANDLE_OK_E)
        {
          /* 初始化失败，进入错误处理循环 */
          for(;;)
          {
            osDelay(1000);  /* 错误状态下延时1秒 */
          }
        }
        
        /* Infinite loop */
        for(;;)
        {
          /* 运行KeyHandle模块处理 */
          KeyHandle_vRun();
          
          /* 延时10ms，控制任务执行频率 */
          osDelay(10);
        }
        /* USER CODE END StartKeyHandleTask */
      }
      ```
   
      1) 按下左方向键并快速释放（按下时间小于长按阈值1000ms）
      2) 观察事件系统是否正确发布EVENT_TYPE_KEY_LEFT_SHORT_PRESS_E事件
      3) 通过断点调试验证KeyHandle_vTriggerSingleKeyShortPressEvent函数的调用
   
      3.1.3 测试结果
   
      测试通过。左方向键短按功能正常，能够正确检测短按操作并发布对应事件。
   
      ![测试图](Image/左方向键短按触发.png)
   
   3.2 单个按键长按触发测试
   
      3.2.1 测试目标
   
      验证单个按键的长按功能是否正常工作，包括长按初次触发和重复触发机制。
   
      3.2.2 测试步骤
   
      1) 按下左方向键并持续按住超过长按阈值时间（1000ms）
      2) 继续按住观察重复触发机制（每200ms重复触发）
      3) 通过断点调试验证长按事件的触发时机和频率
   
      3.2.3 测试结果
   
      测试通过。左方向键长按功能正常，能够在达到长按时间阈值时触发首次长按事件，并按照配置的重复间隔正确进行重复触发。
      ![测试图](Image/左方向键长按触发1.png)
   
      ![测试图](Image/左方向键长按触发2.png)
   
   3.3 组合键短按触发测试
   
      3.3.1 测试目标
   
      验证组合键的检测和触发功能，确保能够正确识别多个按键的同时按下并触发相应的组合键事件。
   
      3.3.2 测试步骤
   
      1) 同时按下左方向键和帮助键
      2) 快速释放两个按键
      3) 观察是否正确触发EVENT_TYPE_KEY_LEFT_HELP_COMBO_PRESS_E事件
      4) 验证在组合键激活期间，单个按键事件被正确抑制
      5) 通过断点调试验证组合键检测逻辑和事件发布过程
   
      3.3.3 测试结果
   
      测试通过。左方向键+帮助键组合键功能正常，能够正确检测两个按键的同时按下，触发对应的组合键事件，并且在组合键激活期间正确抑制了单个按键事件的触发。
      ![测试图](Image/左方向键和帮助键组合键短按触发1.png)
   
      ![测试图](Image/左方向键和帮助键组合键短按触发2.png)
   
      ![测试图](Image/左方向键和帮助键组合键短按触发3.png)


   测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

1. 按键序列检测：未来可增加按键序列检测功能，支持特定按键按下顺序的识别。

### 可重用性声明

1. 该模块依赖于HalKey硬件抽象层模块。
2. 该模块依赖于Event事件系统模块。
3. 该模块依赖于FreeRTOS操作系统提供的时间戳功能。
