//----------------------------------------------------------------------------
/**
* @file KeyHandle.h
* @remark KeyHandle public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef KEYHANDLE_H_
#define KEYHANDLE_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------

/**
* @brief Key handling module status enumeration
*/
typedef enum
{
    KEYHANDLE_OK_E = 0U,                            /* Operation successful */
    KEYHANDLE_ERROR_E                               /* Operation failed/error */
} KEYHANDLE_STATUS_E;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
KEYHANDLE_STATUS_E KeyHandle_eInit(void);
void KeyHandle_vRun(void);

#endif /* KEYHANDLE_H_ */

//===========================================================================
// End of file.
//=========================================================================== 








