//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2023 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file Utility.c
*
* @brief Utility functions in service layer.
*
*/
//----------------------------------------------------------------------------

//-----------------------------------------------------------------------
// Includes:
//-----------------------------------------------------------------------
#include "GlobalTypes.h"
#include "Utility.h"
#include "stm32f4xx.h"
#include <stdlib.h>
//------------------------------------------------------------------------
// Private Definitions:
//------------------------------------------------------------------------


//-----------------------------------------------------------------------
// Private Function Prototypes:
//-----------------------------------------------------------------------

//-----------------------------------------------------------------------
// Private Data:
//-----------------------------------------------------------------------

//-----------------------------------------------------------------------
// Public Function Implementation:
//-----------------------------------------------------------------------

/**
* @brief  precise us delay
* @remark
* @param u16Delay [in] delay count.
* @return none
*@version
* |     Date    |         Version      |    Author   |   Modified Content   |
* |  2024.07.15 |        v1.00.00      |   Li Teng   |    First Creation    |
*/
void Utility_vDelayUs(U16 u16Delay)
{
  U32 u32WaitLoopIndex = 0;

  u32WaitLoopIndex = (u16Delay * (SystemCoreClock / (1000000U * 5U))) + 150U;
  while (u32WaitLoopIndex != 0U)
  {
	  u32WaitLoopIndex--;
  }
}

/**
* @brief  Round the data
* @remark
* @param s16Data [in] data needed to round.
* @return round data.
* @version
* |     Date    |         Version      |    Author   |   Modified Content   |
* |  2024.07.18 |        v1.00.00      |   Li Teng   |    First Creation    |
*/
S16 Utility_s16Round(F32 f32Data)
{
  S32 s32ConvertData;

  /* Use S32 prevent data overflow */
  s32ConvertData = (S32)(f32Data*10);
  if((abs(s32ConvertData)%10) > 4)	
  {
    if(s32ConvertData >= 0)
    {
      s32ConvertData += 10;
    }
    else
    {
      s32ConvertData -= 10;
    }
  }	
  s32ConvertData /= 10;

  return (S16)s32ConvertData;
}

/**
* @brief Get current system tick in milliseconds.
* @remark Used for timing operations.
*
* @return Current system tick in milliseconds.
*/
U32 Utility_u32GetTickMs(void)
{
    /* Use HAL_GetTick() to get milliseconds since system start */
    return HAL_GetTick();
}

//-----------------------------------------------------------------------
// Private Function Implementation:
//-----------------------------------------------------------------------





//===========================================================================
// End of file.
//===========================================================================
