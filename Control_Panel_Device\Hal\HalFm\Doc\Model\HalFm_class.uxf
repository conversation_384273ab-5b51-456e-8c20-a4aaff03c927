<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>8</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>360</x>
      <y>240</y>
      <w>712</w>
      <h>112</h>
    </coordinates>
    <panel_attributes>HalFm
--
+HalFm_eInit():void
+HalFm_eReadData(u16Address:const U16, pu8Buffer:U8* const, u16Length:const U16):HALFM_STATUS_E
+HalFm_eWriteData(u16Address:const U16, pu8Buffer:const U8* const, u16Length:const U16):HALFM_STATUS_E
-HalFm_eWriteEnable():HALFM_STATUS_E
-HalFm_eIsAddressValid(u16Address:const U16, u16Length:const U16):HALFM_STATUS_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>416</x>
      <y>112</y>
      <w>592</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>HAL_SPI
--
+HAL_SPI_Transmit(hspi:SPI_HandleTypeDef*, pData:uint8_t*, Size:uint16_t, Timeout:uint32_t):HAL_StatusTypeDef
+HAL_SPI_Receive(hspi:SPI_HandleTypeDef*, pData:uint8_t*, Size:uint16_t, Timeout:uint32_t):HAL_StatusTypeDef</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>696</x>
      <y>184</y>
      <w>56</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>456</x>
      <y>400</y>
      <w>496</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>HAL_GPIO
--
+HAL_GPIO_WritePin(GPIOx:GPIO_TypeDef*, GPIO_Pin:U16, PinState:GPIO_PinState):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>696</x>
      <y>344</y>
      <w>56</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1112</x>
      <y>264</y>
      <w>216</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALFM_STATUS_E
--
HALFM_OK_E = 0U
HALFM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1064</x>
      <y>272</y>
      <w>64</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
</diagram>
