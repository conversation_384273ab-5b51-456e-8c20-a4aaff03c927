/*******************************************************************************
 * Size: 17 px
 * Bpp: 4
 * Opts: --bpp 4 --size 17 --font F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/LVGL_UI/STM32CubeIDE_FreeRTOS_LVGL_UI4/assets/Dengb.ttf -o F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/LVGL_UI/STM32CubeIDE_FreeRTOS_LVGL_UI4/assets\ui_font_FontDengXianBold.c --format lvgl -r 0x20-0x7f --symbols 设备本地远程菜单名称状态自定义参数设定频率电机实际输出电流→返回确认编辑主页视图选项点击“”添加新完成对比度设置背光亮度时间系统语言蓝牙用户登录中文已连接 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_FONTDENGXIANBOLD
#define UI_FONT_FONTDENGXIANBOLD 1
#endif

#if UI_FONT_FONTDENGXIANBOLD

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x6f, 0x96, 0xf9, 0x5f, 0x85, 0xf8, 0x4f, 0x74,
    0xf7, 0x4f, 0x63, 0xf6, 0x4, 0x10, 0x0, 0x6f,
    0x87, 0xf9,

    /* U+0022 "\"" */
    0xdf, 0x8, 0xf4, 0xce, 0x8, 0xf3, 0xbd, 0x7,
    0xf2, 0x67, 0x3, 0x91,

    /* U+0023 "#" */
    0x0, 0x7, 0xa0, 0x9, 0x80, 0x0, 0xb, 0x60,
    0xd, 0x40, 0x0, 0xe, 0x20, 0x1f, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xfa, 0x1, 0x6c, 0x11, 0x8a,
    0x10, 0x0, 0x79, 0x0, 0xa7, 0x0, 0x0, 0xa7,
    0x0, 0xc5, 0x0, 0x0, 0xd4, 0x0, 0xe2, 0x0,
    0xbe, 0xfe, 0xee, 0xfe, 0xe3, 0x15, 0xd1, 0x16,
    0xc1, 0x10, 0x7, 0xa0, 0x9, 0x80, 0x0, 0xb,
    0x60, 0xd, 0x40, 0x0,

    /* U+0024 "$" */
    0x0, 0x9e, 0xff, 0xd5, 0x0, 0x9f, 0x8d, 0x9d,
    0xf3, 0xf, 0xc0, 0xb5, 0x4f, 0x70, 0xfd, 0xb,
    0x50, 0x0, 0xc, 0xf8, 0xc5, 0x0, 0x0, 0x2d,
    0xff, 0xe9, 0x20, 0x0, 0x4, 0xde, 0xff, 0x30,
    0x0, 0xb, 0x55, 0xfb, 0x14, 0x20, 0xb5, 0xe,
    0xe5, 0xf8, 0xb, 0x50, 0xec, 0xd, 0xf8, 0xd9,
    0xbf, 0x60, 0x1a, 0xef, 0xfd, 0x60, 0x0, 0x0,
    0xb5, 0x0, 0x0, 0x0, 0x8, 0x40, 0x0,

    /* U+0025 "%" */
    0x1d, 0xc0, 0x4, 0xb0, 0x7, 0x9b, 0x50, 0xa5,
    0x0, 0xa7, 0x98, 0xe, 0x0, 0xb, 0x68, 0x95,
    0x90, 0x0, 0xa7, 0x98, 0xb4, 0x0, 0x8, 0x9b,
    0x7d, 0x5d, 0x60, 0x2f, 0xe7, 0x8d, 0x5f, 0x0,
    0x0, 0xc3, 0xf1, 0xf2, 0x0, 0x2d, 0x1f, 0xe,
    0x30, 0x7, 0x70, 0xf1, 0xf2, 0x0, 0xd1, 0xd,
    0x5f, 0x0, 0x3b, 0x0, 0x5e, 0x70,

    /* U+0026 "&" */
    0x0, 0x8, 0xdf, 0xd6, 0x0, 0x0, 0x0, 0x7,
    0xf8, 0x3b, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0x0,
    0x5f, 0x70, 0x0, 0x0, 0x9, 0xf2, 0xb, 0xf3,
    0x0, 0x0, 0x0, 0x3f, 0xbd, 0xf6, 0x0, 0x0,
    0x0, 0x18, 0xff, 0xc2, 0x0, 0x59, 0x10, 0x2d,
    0xf8, 0xee, 0x20, 0xc, 0xf0, 0xa, 0xf4, 0x3,
    0xfd, 0x12, 0xfa, 0x0, 0xef, 0x0, 0x4, 0xfd,
    0xcf, 0x20, 0xd, 0xf3, 0x0, 0x7, 0xff, 0x90,
    0x0, 0x6f, 0xf9, 0x8b, 0xff, 0xff, 0xb8, 0x10,
    0x5c, 0xff, 0xd9, 0x22, 0xaf, 0xf1,

    /* U+0027 "'" */
    0xbf, 0x1a, 0xf0, 0x9f, 0x5, 0x80,

    /* U+0028 "(" */
    0x0, 0xc, 0xe1, 0x0, 0x8f, 0x50, 0x1, 0xfc,
    0x0, 0x8, 0xf4, 0x0, 0xd, 0xf0, 0x0, 0x1f,
    0xc0, 0x0, 0x2f, 0xa0, 0x0, 0x3f, 0x90, 0x0,
    0x2f, 0xa0, 0x0, 0x1f, 0xb0, 0x0, 0xd, 0xf0,
    0x0, 0x8, 0xf4, 0x0, 0x1, 0xfc, 0x0, 0x0,
    0x8f, 0x50, 0x0, 0xc, 0xe1,

    /* U+0029 ")" */
    0x5f, 0x70, 0x0, 0xaf, 0x20, 0x2, 0xfb, 0x0,
    0xa, 0xf2, 0x0, 0x5f, 0x70, 0x1, 0xfb, 0x0,
    0xf, 0xc0, 0x0, 0xfd, 0x0, 0xf, 0xc0, 0x1,
    0xfb, 0x0, 0x5f, 0x70, 0xa, 0xf2, 0x2, 0xfb,
    0x0, 0xaf, 0x20, 0x5f, 0x70, 0x0,

    /* U+002A "*" */
    0x0, 0xf, 0x60, 0x0, 0x11, 0xe, 0x40, 0x20,
    0x8f, 0xbf, 0xbe, 0xe0, 0x3, 0xaf, 0xe5, 0x10,
    0x1, 0xe8, 0xf5, 0x0, 0xa, 0xd0, 0x7e, 0x0,
    0x0, 0x10, 0x1, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x86, 0x0, 0x0, 0x0, 0x0, 0xea,
    0x0, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0x0, 0xea, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xf2, 0x38, 0x88, 0xfd, 0x88, 0x81, 0x0, 0x0,
    0xea, 0x0, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0,
    0x0, 0x0, 0xea, 0x0, 0x0,

    /* U+002C "," */
    0x22, 0xf, 0xf1, 0xff, 0x2, 0xf0, 0x7b, 0x6,
    0x30,

    /* U+002D "-" */
    0x1a, 0xaa, 0xa7, 0x2f, 0xff, 0xfa,

    /* U+002E "." */
    0x22, 0xf, 0xf0, 0xff, 0x0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x2,
    0xfa, 0x0, 0x0, 0x0, 0x9f, 0x30, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x0, 0x6, 0xf5, 0x0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0,
    0x0, 0xa, 0xf1, 0x0, 0x0, 0x1, 0xfa, 0x0,
    0x0, 0x0, 0x8f, 0x40, 0x0, 0x0, 0xe, 0xd0,
    0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x5c, 0xfe, 0x90, 0x0, 0x4f, 0xe9, 0xbf,
    0xb0, 0xc, 0xf2, 0x0, 0xbf, 0x30, 0xfd, 0x0,
    0x6, 0xf8, 0x3f, 0xb0, 0x0, 0x4f, 0xa4, 0xfa,
    0x0, 0x3, 0xfb, 0x4f, 0xa0, 0x0, 0x3f, 0xb3,
    0xfb, 0x0, 0x4, 0xfa, 0x1f, 0xd0, 0x0, 0x6f,
    0x70, 0xcf, 0x20, 0xc, 0xf2, 0x4, 0xfe, 0x8b,
    0xfa, 0x0, 0x4, 0xcf, 0xe8, 0x0,

    /* U+0031 "1" */
    0x0, 0x6f, 0xf2, 0x0, 0x1b, 0xff, 0xf2, 0x0,
    0x8e, 0x4c, 0xf2, 0x0, 0x30, 0xc, 0xf2, 0x0,
    0x0, 0xc, 0xf2, 0x0, 0x0, 0xc, 0xf2, 0x0,
    0x0, 0xc, 0xf2, 0x0, 0x0, 0xc, 0xf2, 0x0,
    0x0, 0xc, 0xf2, 0x0, 0x0, 0xc, 0xf2, 0x0,
    0x68, 0x8e, 0xf9, 0x85, 0xcf, 0xff, 0xff, 0xfb,

    /* U+0032 "2" */
    0x0, 0x6d, 0xfe, 0xa1, 0x0, 0x6f, 0xe9, 0xbf,
    0xd0, 0xe, 0xf1, 0x0, 0xbf, 0x40, 0x44, 0x0,
    0x8, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0x30, 0x0,
    0x0, 0xaf, 0xa0, 0x0, 0x0, 0xbf, 0xc0, 0x0,
    0x1, 0xcf, 0xa0, 0x0, 0x0, 0xcf, 0x80, 0x0,
    0x0, 0x9f, 0x90, 0x0, 0x0, 0x2f, 0xf9, 0x99,
    0x99, 0x63, 0xff, 0xff, 0xff, 0xfa,

    /* U+0033 "3" */
    0x0, 0x7d, 0xfe, 0xa2, 0x0, 0x8f, 0xd9, 0xcf,
    0xd0, 0xf, 0xe0, 0x0, 0xcf, 0x30, 0x22, 0x0,
    0xa, 0xf4, 0x0, 0x0, 0x4, 0xfd, 0x0, 0x0,
    0xbf, 0xfa, 0x10, 0x0, 0x6, 0xad, 0xfa, 0x0,
    0x0, 0x0, 0xa, 0xf6, 0x26, 0x40, 0x0, 0x5f,
    0x93, 0xfd, 0x0, 0x9, 0xf8, 0xb, 0xfd, 0x9b,
    0xff, 0x20, 0x8, 0xdf, 0xea, 0x20,

    /* U+0034 "4" */
    0x0, 0x0, 0x2f, 0xf5, 0x0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x5, 0xfb, 0xf5, 0x0, 0x0,
    0x1e, 0xa7, 0xf5, 0x0, 0x0, 0x9f, 0x17, 0xf5,
    0x0, 0x3, 0xf7, 0x7, 0xf5, 0x0, 0xc, 0xd0,
    0x7, 0xf5, 0x0, 0x6f, 0x40, 0x7, 0xf5, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xf1, 0x58, 0x88, 0x8c,
    0xfb, 0x80, 0x0, 0x0, 0x7, 0xf5, 0x0, 0x0,
    0x0, 0x7, 0xf5, 0x0,

    /* U+0035 "5" */
    0x9, 0xff, 0xff, 0xff, 0x0, 0x9f, 0x99, 0x99,
    0x90, 0xa, 0xf1, 0x0, 0x0, 0x0, 0xbf, 0x0,
    0x0, 0x0, 0xc, 0xf2, 0x77, 0x40, 0x0, 0xdf,
    0xff, 0xff, 0xb0, 0xa, 0xb5, 0x2, 0xdf, 0x60,
    0x0, 0x0, 0x5, 0xf9, 0x0, 0x0, 0x0, 0x4f,
    0xa1, 0xfd, 0x0, 0x9, 0xf7, 0xb, 0xfd, 0x9b,
    0xfe, 0x10, 0x8, 0xdf, 0xea, 0x10,

    /* U+0036 "6" */
    0x0, 0x2b, 0xff, 0xc3, 0x0, 0x1e, 0xfa, 0x9f,
    0xf1, 0x9, 0xf5, 0x0, 0x47, 0x20, 0xee, 0x0,
    0x10, 0x0, 0x1f, 0xc8, 0xff, 0xe5, 0x2, 0xff,
    0xc6, 0x7f, 0xf2, 0x2f, 0xf1, 0x0, 0x7f, 0x81,
    0xfe, 0x0, 0x3, 0xfb, 0xf, 0xf0, 0x0, 0x3f,
    0xb0, 0xaf, 0x50, 0x9, 0xf7, 0x2, 0xff, 0x9a,
    0xfe, 0x10, 0x3, 0xbf, 0xfb, 0x20,

    /* U+0037 "7" */
    0x2f, 0xff, 0xff, 0xff, 0x91, 0x99, 0x99, 0x9b,
    0xf8, 0x0, 0x0, 0x0, 0xcf, 0x10, 0x0, 0x0,
    0x6f, 0x70, 0x0, 0x0, 0xe, 0xe0, 0x0, 0x0,
    0x8, 0xf6, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0x0, 0x0, 0xef, 0x0, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x0, 0x2, 0xfc, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x7d, 0xfe, 0xa2, 0x0, 0x9f, 0xb6, 0x8f,
    0xe1, 0xe, 0xf0, 0x0, 0xaf, 0x50, 0xef, 0x0,
    0x9, 0xf5, 0x7, 0xf8, 0x24, 0xed, 0x0, 0x8,
    0xff, 0xfd, 0x20, 0x7, 0xfa, 0x57, 0xfd, 0x11,
    0xfd, 0x0, 0x6, 0xf8, 0x4f, 0xb0, 0x0, 0x4f,
    0xb2, 0xfe, 0x0, 0x7, 0xf9, 0xb, 0xfb, 0x68,
    0xff, 0x30, 0x7, 0xdf, 0xeb, 0x30,

    /* U+0039 "9" */
    0x0, 0x7d, 0xfe, 0x80, 0x0, 0xbf, 0xd9, 0xcf,
    0x90, 0x2f, 0xd0, 0x0, 0xcf, 0x24, 0xfa, 0x0,
    0x7, 0xf6, 0x3f, 0xc0, 0x0, 0x8f, 0x90, 0xdf,
    0x72, 0x5f, 0xf9, 0x3, 0xef, 0xff, 0x9f, 0x90,
    0x0, 0x34, 0x5, 0xf8, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0xcc, 0x0, 0x1e, 0xf1, 0xa, 0xfc, 0x8d,
    0xf7, 0x0, 0x9, 0xef, 0xd6, 0x0,

    /* U+003A ":" */
    0xff, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0xff, 0x0,

    /* U+003B ";" */
    0xff, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0xff, 0x2, 0xf0, 0x7b,
    0x6, 0x30,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x5b, 0xf2, 0x0, 0x2, 0x8e, 0xfd, 0x70, 0x5,
    0xcf, 0xfa, 0x40, 0x0, 0x6f, 0xe7, 0x10, 0x0,
    0x0, 0x6f, 0xb5, 0x0, 0x0, 0x0, 0x18, 0xef,
    0xe8, 0x20, 0x0, 0x0, 0x4, 0xaf, 0xfb, 0x50,
    0x0, 0x0, 0x1, 0x7d, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x31,

    /* U+003D "=" */
    0x6f, 0xff, 0xff, 0xff, 0xf2, 0x38, 0x88, 0x88,
    0x88, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0x88, 0x88, 0x88,
    0x81, 0x6f, 0xff, 0xff, 0xff, 0xf2,

    /* U+003E ">" */
    0x11, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa3, 0x0,
    0x0, 0x0, 0x18, 0xef, 0xd7, 0x10, 0x0, 0x0,
    0x5, 0xcf, 0xfa, 0x40, 0x0, 0x0, 0x2, 0x9e,
    0xf2, 0x0, 0x0, 0x0, 0x6d, 0xf2, 0x0, 0x3,
    0x9f, 0xfc, 0x60, 0x6, 0xcf, 0xf9, 0x30, 0x0,
    0x6f, 0xc6, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x3, 0xcf, 0xe9, 0x10, 0x2f, 0xf9, 0xcf, 0xb0,
    0x9f, 0x40, 0xd, 0xf1, 0x68, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x1, 0xdf, 0x50,
    0x0, 0xd, 0xf6, 0x0, 0x0, 0x4f, 0x80, 0x0,
    0x0, 0x49, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0xaf, 0x60, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x5, 0xae, 0xff, 0xd9, 0x20, 0x0,
    0x0, 0x1, 0xce, 0x95, 0x33, 0x5b, 0xf7, 0x0,
    0x0, 0x2e, 0xa0, 0x0, 0x0, 0x0, 0x4f, 0x60,
    0x0, 0xca, 0x0, 0x7d, 0xfb, 0x4f, 0x47, 0xe0,
    0x6, 0xf1, 0xa, 0xe4, 0x18, 0xef, 0x1, 0xf4,
    0xc, 0x90, 0x4f, 0x50, 0x0, 0xed, 0x0, 0xe6,
    0xf, 0x50, 0xae, 0x0, 0x0, 0xf9, 0x0, 0xe6,
    0x2f, 0x30, 0xdb, 0x0, 0x3, 0xf6, 0x1, 0xf3,
    0x1f, 0x30, 0xdb, 0x0, 0xa, 0xf2, 0x6, 0xe0,
    0xf, 0x60, 0x9f, 0x20, 0x7b, 0xf2, 0x3f, 0x60,
    0xa, 0xc0, 0x1b, 0xff, 0xa0, 0xcf, 0xe6, 0x0,
    0x2, 0xf8, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x4f, 0xb4, 0x10, 0x3, 0x7c, 0xb0, 0x0,
    0x0, 0x2, 0x9e, 0xff, 0xff, 0xb6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x6, 0xfb, 0xfc,
    0x0, 0x0, 0x0, 0xc, 0xf3, 0xdf, 0x20, 0x0,
    0x0, 0x2f, 0xd0, 0x7f, 0x80, 0x0, 0x0, 0x7f,
    0x80, 0x1f, 0xd0, 0x0, 0x0, 0xdf, 0x20, 0xb,
    0xf3, 0x0, 0x3, 0xff, 0x88, 0x8c, 0xf9, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xe, 0xf2,
    0x0, 0x0, 0xcf, 0x50, 0x4f, 0xb0, 0x0, 0x0,
    0x5f, 0xa0, 0xaf, 0x50, 0x0, 0x0, 0xf, 0xf1,

    /* U+0042 "B" */
    0x8f, 0xff, 0xfd, 0x70, 0x8, 0xfb, 0x79, 0xef,
    0x90, 0x8f, 0x70, 0x3, 0xfe, 0x8, 0xf7, 0x0,
    0x2f, 0xd0, 0x8f, 0x81, 0x3b, 0xf6, 0x8, 0xff,
    0xff, 0xf7, 0x0, 0x8f, 0x94, 0x5a, 0xfc, 0x8,
    0xf7, 0x0, 0x9, 0xf7, 0x8f, 0x70, 0x0, 0x6f,
    0x98, 0xf7, 0x0, 0xb, 0xf8, 0x8f, 0xb7, 0x8c,
    0xfe, 0x18, 0xff, 0xff, 0xd9, 0x10,

    /* U+0043 "C" */
    0x0, 0x7, 0xdf, 0xeb, 0x30, 0x0, 0xb, 0xfe,
    0xab, 0xff, 0x40, 0x6, 0xfd, 0x10, 0x4, 0xfd,
    0x0, 0xcf, 0x50, 0x0, 0x6, 0x20, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf5, 0x0, 0x0,
    0x85, 0x0, 0x6f, 0xd1, 0x0, 0x5f, 0xd0, 0x0,
    0xbf, 0xea, 0xbf, 0xf3, 0x0, 0x0, 0x7d, 0xfe,
    0xb3, 0x0,

    /* U+0044 "D" */
    0x8f, 0xff, 0xfe, 0xb5, 0x0, 0x8, 0xfc, 0x9a,
    0xbf, 0xfb, 0x0, 0x8f, 0x70, 0x0, 0x1d, 0xf8,
    0x8, 0xf7, 0x0, 0x0, 0x2f, 0xf0, 0x8f, 0x70,
    0x0, 0x0, 0xdf, 0x38, 0xf7, 0x0, 0x0, 0xb,
    0xf5, 0x8f, 0x70, 0x0, 0x0, 0xcf, 0x48, 0xf7,
    0x0, 0x0, 0xe, 0xf2, 0x8f, 0x70, 0x0, 0x4,
    0xfe, 0x8, 0xf7, 0x0, 0x2, 0xdf, 0x60, 0x8f,
    0xc9, 0x9b, 0xff, 0xa0, 0x8, 0xff, 0xff, 0xeb,
    0x50, 0x0,

    /* U+0045 "E" */
    0x8f, 0xff, 0xff, 0xd0, 0x8f, 0xc9, 0x99, 0x80,
    0x8f, 0x70, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0,
    0x8f, 0x70, 0x0, 0x0, 0x8f, 0xc9, 0x99, 0x20,
    0x8f, 0xff, 0xff, 0x50, 0x8f, 0x70, 0x0, 0x0,
    0x8f, 0x70, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0,
    0x8f, 0xc9, 0x99, 0x91, 0x8f, 0xff, 0xff, 0xf2,

    /* U+0046 "F" */
    0x8f, 0xff, 0xff, 0xf8, 0xfc, 0xaa, 0xa9, 0x8f,
    0x70, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0x8, 0xfc, 0x99, 0x97, 0x8f, 0xff, 0xff,
    0xc8, 0xf7, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x8,
    0xf7, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x8, 0xf7,
    0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x6, 0xcf, 0xfd, 0x80, 0x0, 0xa, 0xff,
    0xaa, 0xef, 0xc0, 0x6, 0xfd, 0x10, 0x0, 0xbf,
    0x60, 0xcf, 0x50, 0x0, 0x1, 0x20, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x1, 0x22,
    0x21, 0x1f, 0xf0, 0x0, 0xdf, 0xff, 0xc0, 0xff,
    0x10, 0x4, 0x56, 0xfc, 0xc, 0xf6, 0x0, 0x0,
    0x1f, 0xc0, 0x5f, 0xe2, 0x0, 0x5, 0xfc, 0x0,
    0x9f, 0xfa, 0x9c, 0xff, 0x50, 0x0, 0x5c, 0xff,
    0xd9, 0x20,

    /* U+0048 "H" */
    0x8f, 0x70, 0x0, 0x0, 0xef, 0x8, 0xf7, 0x0,
    0x0, 0xe, 0xf0, 0x8f, 0x70, 0x0, 0x0, 0xef,
    0x8, 0xf7, 0x0, 0x0, 0xe, 0xf0, 0x8f, 0x70,
    0x0, 0x0, 0xef, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xda, 0xaa, 0xaa, 0xff, 0x8, 0xf7,
    0x0, 0x0, 0xe, 0xf0, 0x8f, 0x70, 0x0, 0x0,
    0xef, 0x8, 0xf7, 0x0, 0x0, 0xe, 0xf0, 0x8f,
    0x70, 0x0, 0x0, 0xef, 0x8, 0xf7, 0x0, 0x0,
    0xe, 0xf0,

    /* U+0049 "I" */
    0x8f, 0x78, 0xf7, 0x8f, 0x78, 0xf7, 0x8f, 0x78,
    0xf7, 0x8f, 0x78, 0xf7, 0x8f, 0x78, 0xf7, 0x8f,
    0x78, 0xf7,

    /* U+004A "J" */
    0x7, 0xff, 0xf8, 0x4, 0x9c, 0xf8, 0x0, 0x7,
    0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0,
    0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8,
    0x0, 0x8, 0xf7, 0x0, 0xc, 0xf4, 0x9a, 0xcf,
    0xd0, 0xaf, 0xfb, 0x20,

    /* U+004B "K" */
    0x8f, 0x70, 0x0, 0xbf, 0xa0, 0x8f, 0x70, 0x7,
    0xfd, 0x0, 0x8f, 0x70, 0x3f, 0xf2, 0x0, 0x8f,
    0x71, 0xef, 0x50, 0x0, 0x8f, 0x7b, 0xf9, 0x0,
    0x0, 0x8f, 0xef, 0xf4, 0x0, 0x0, 0x8f, 0xfc,
    0xfd, 0x0, 0x0, 0x8f, 0xa0, 0xcf, 0x80, 0x0,
    0x8f, 0x70, 0x3f, 0xf3, 0x0, 0x8f, 0x70, 0x8,
    0xfd, 0x0, 0x8f, 0x70, 0x0, 0xef, 0x70, 0x8f,
    0x70, 0x0, 0x4f, 0xf2,

    /* U+004C "L" */
    0x8f, 0x70, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x8f,
    0x70, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0x8, 0xf7, 0x0, 0x0, 0x8f, 0x70, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x8,
    0xf7, 0x0, 0x0, 0x8f, 0xc9, 0x99, 0x98, 0xff,
    0xff, 0xff,

    /* U+004D "M" */
    0x8f, 0xf6, 0x0, 0x0, 0x0, 0xaf, 0xf4, 0x8f,
    0xfc, 0x0, 0x0, 0x1, 0xff, 0xf4, 0x8f, 0xdf,
    0x30, 0x0, 0x6, 0xfd, 0xf4, 0x8f, 0x7f, 0x90,
    0x0, 0xd, 0xe9, 0xf4, 0x8f, 0x4d, 0xf0, 0x0,
    0x3f, 0x89, 0xf4, 0x8f, 0x46, 0xf6, 0x0, 0x9f,
    0x29, 0xf4, 0x8f, 0x41, 0xfc, 0x0, 0xfc, 0x9,
    0xf4, 0x8f, 0x40, 0xaf, 0x25, 0xf5, 0x9, 0xf4,
    0x8f, 0x40, 0x4f, 0x8b, 0xf0, 0x9, 0xf4, 0x8f,
    0x40, 0xd, 0xef, 0x90, 0x9, 0xf4, 0x8f, 0x40,
    0x7, 0xff, 0x30, 0x9, 0xf4, 0x8f, 0x40, 0x1,
    0xfd, 0x0, 0x9, 0xf4,

    /* U+004E "N" */
    0x8f, 0xf2, 0x0, 0x0, 0x4f, 0x98, 0xff, 0xc0,
    0x0, 0x4, 0xf9, 0x8f, 0xdf, 0x60, 0x0, 0x4f,
    0x98, 0xf5, 0xfe, 0x10, 0x4, 0xf9, 0x8f, 0x46,
    0xf9, 0x0, 0x4f, 0x98, 0xf4, 0xd, 0xf3, 0x4,
    0xf9, 0x8f, 0x40, 0x3f, 0xd0, 0x4f, 0x98, 0xf4,
    0x0, 0x9f, 0x73, 0xf9, 0x8f, 0x40, 0x1, 0xef,
    0x5f, 0x98, 0xf4, 0x0, 0x6, 0xfd, 0xf9, 0x8f,
    0x40, 0x0, 0xc, 0xff, 0x98, 0xf4, 0x0, 0x0,
    0x3f, 0xf9,

    /* U+004F "O" */
    0x0, 0x5, 0xbe, 0xfe, 0xa3, 0x0, 0x0, 0x9,
    0xff, 0xba, 0xcf, 0xf7, 0x0, 0x5, 0xfe, 0x20,
    0x0, 0x4f, 0xf3, 0x0, 0xdf, 0x50, 0x0, 0x0,
    0x8f, 0xa0, 0xf, 0xf0, 0x0, 0x0, 0x3, 0xfe,
    0x2, 0xfe, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x2f, 0xe0, 0xc, 0xf6, 0x0, 0x0,
    0x7, 0xf9, 0x0, 0x5f, 0xe2, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x9f, 0xfb, 0x9b, 0xff, 0x60, 0x0,
    0x0, 0x5b, 0xef, 0xea, 0x30, 0x0,

    /* U+0050 "P" */
    0x8f, 0xff, 0xfd, 0x70, 0x8, 0xfb, 0x78, 0xdf,
    0xb0, 0x8f, 0x70, 0x0, 0xef, 0x38, 0xf7, 0x0,
    0xa, 0xf5, 0x8f, 0x70, 0x0, 0xdf, 0x48, 0xf8,
    0x23, 0x9f, 0xd0, 0x8f, 0xff, 0xff, 0xc2, 0x8,
    0xfa, 0x54, 0x20, 0x0, 0x8f, 0x70, 0x0, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x8f, 0x70, 0x0,
    0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x5, 0xbe, 0xfe, 0xa3, 0x0, 0x0, 0x9,
    0xff, 0xba, 0xcf, 0xf7, 0x0, 0x6, 0xfe, 0x20,
    0x0, 0x3f, 0xf3, 0x0, 0xdf, 0x50, 0x0, 0x0,
    0x8f, 0xa0, 0x1f, 0xf0, 0x0, 0x0, 0x3, 0xfe,
    0x2, 0xfe, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0xff, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x2f, 0xe0, 0xc, 0xf7, 0x0, 0x0,
    0x8, 0xf9, 0x0, 0x4f, 0xe3, 0x0, 0x4, 0xff,
    0x20, 0x0, 0x8f, 0xfc, 0xbd, 0xff, 0x50, 0x0,
    0x0, 0x3a, 0xdf, 0xe8, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x87, 0x0, 0x0, 0x0, 0x0, 0x8, 0xef,
    0xb0, 0x0,

    /* U+0052 "R" */
    0x8f, 0xff, 0xfe, 0xa2, 0x0, 0x8f, 0xb7, 0x7b,
    0xff, 0x20, 0x8f, 0x70, 0x0, 0x8f, 0xa0, 0x8f,
    0x70, 0x0, 0x4f, 0xc0, 0x8f, 0x70, 0x0, 0x6f,
    0xa0, 0x8f, 0x82, 0x25, 0xef, 0x40, 0x8f, 0xff,
    0xff, 0xe6, 0x0, 0x8f, 0xa5, 0xbf, 0x60, 0x0,
    0x8f, 0x70, 0x2f, 0xe1, 0x0, 0x8f, 0x70, 0x7,
    0xfa, 0x0, 0x8f, 0x70, 0x0, 0xdf, 0x50, 0x8f,
    0x70, 0x0, 0x4f, 0xe1,

    /* U+0053 "S" */
    0x0, 0x8d, 0xfe, 0x91, 0x0, 0xaf, 0xc8, 0xbf,
    0xd0, 0xf, 0xe0, 0x0, 0xbe, 0x31, 0xfe, 0x0,
    0x0, 0x0, 0xe, 0xfb, 0x30, 0x0, 0x0, 0x3e,
    0xff, 0xd8, 0x10, 0x0, 0x5, 0xbf, 0xfd, 0x10,
    0x0, 0x0, 0x1b, 0xf8, 0x14, 0x20, 0x0, 0x4f,
    0xa5, 0xfb, 0x0, 0x7, 0xf9, 0xc, 0xfc, 0x9a,
    0xff, 0x30, 0x19, 0xdf, 0xea, 0x30,

    /* U+0054 "T" */
    0xcf, 0xff, 0xff, 0xff, 0xf2, 0x7a, 0xac, 0xfe,
    0xaa, 0xa1, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0,
    0x5, 0xfa, 0x0, 0x0, 0x0, 0x5, 0xfa, 0x0,
    0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x5,
    0xfa, 0x0, 0x0, 0x0, 0x5, 0xfa, 0x0, 0x0,
    0x0, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x5, 0xfa,
    0x0, 0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0,
    0x5, 0xfa, 0x0, 0x0,

    /* U+0055 "U" */
    0xcf, 0x40, 0x0, 0x5, 0xfb, 0xcf, 0x40, 0x0,
    0x5, 0xfb, 0xcf, 0x40, 0x0, 0x5, 0xfb, 0xcf,
    0x40, 0x0, 0x5, 0xfb, 0xcf, 0x40, 0x0, 0x5,
    0xfb, 0xcf, 0x40, 0x0, 0x5, 0xfb, 0xcf, 0x40,
    0x0, 0x5, 0xfb, 0xbf, 0x40, 0x0, 0x5, 0xfa,
    0xaf, 0x70, 0x0, 0x8, 0xf9, 0x6f, 0xe1, 0x0,
    0x2e, 0xf4, 0xc, 0xfe, 0xab, 0xff, 0xa0, 0x0,
    0x8d, 0xff, 0xc7, 0x0,

    /* U+0056 "V" */
    0xbf, 0x50, 0x0, 0x0, 0x7f, 0xa5, 0xfa, 0x0,
    0x0, 0xc, 0xf4, 0xf, 0xf0, 0x0, 0x1, 0xfe,
    0x0, 0xaf, 0x50, 0x0, 0x6f, 0x80, 0x4, 0xfa,
    0x0, 0xb, 0xf3, 0x0, 0xe, 0xf0, 0x1, 0xfd,
    0x0, 0x0, 0x9f, 0x40, 0x6f, 0x70, 0x0, 0x3,
    0xfa, 0xb, 0xf2, 0x0, 0x0, 0xd, 0xe0, 0xfc,
    0x0, 0x0, 0x0, 0x8f, 0x8f, 0x60, 0x0, 0x0,
    0x2, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xc, 0xfb,
    0x0, 0x0,

    /* U+0057 "W" */
    0xcf, 0x40, 0x0, 0xe, 0xf4, 0x0, 0x0, 0xef,
    0x27, 0xf8, 0x0, 0x2, 0xff, 0x90, 0x0, 0x1f,
    0xe0, 0x3f, 0xc0, 0x0, 0x7f, 0xcd, 0x0, 0x5,
    0xfa, 0x0, 0xff, 0x0, 0xb, 0xe7, 0xf2, 0x0,
    0x9f, 0x50, 0xb, 0xf4, 0x0, 0xfa, 0x4f, 0x60,
    0xd, 0xf1, 0x0, 0x7f, 0x70, 0x4f, 0x60, 0xfa,
    0x1, 0xfd, 0x0, 0x2, 0xfb, 0x8, 0xf1, 0xb,
    0xe0, 0x5f, 0x80, 0x0, 0xe, 0xf0, 0xcd, 0x0,
    0x7f, 0x39, 0xf4, 0x0, 0x0, 0xaf, 0x4f, 0x90,
    0x2, 0xf7, 0xdf, 0x0, 0x0, 0x6, 0xfb, 0xf4,
    0x0, 0xe, 0xbf, 0xb0, 0x0, 0x0, 0x1f, 0xff,
    0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0xdf,
    0xb0, 0x0, 0x5, 0xff, 0x30, 0x0,

    /* U+0058 "X" */
    0x5, 0xfc, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0xbf,
    0x60, 0x0, 0xdf, 0x40, 0x0, 0x1e, 0xe1, 0x7,
    0xf9, 0x0, 0x0, 0x6, 0xfa, 0x2f, 0xe1, 0x0,
    0x0, 0x0, 0xbf, 0xdf, 0x50, 0x0, 0x0, 0x0,
    0x2f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x2, 0xfe, 0x7f, 0xb0, 0x0,
    0x0, 0xb, 0xf4, 0xc, 0xf5, 0x0, 0x0, 0x6f,
    0xa0, 0x2, 0xfe, 0x10, 0x1, 0xff, 0x10, 0x0,
    0x8f, 0x90, 0xb, 0xf6, 0x0, 0x0, 0xd, 0xf3,

    /* U+0059 "Y" */
    0xbf, 0x70, 0x0, 0x9, 0xf8, 0x2f, 0xe0, 0x0,
    0x2f, 0xe0, 0x9, 0xf7, 0x0, 0xaf, 0x60, 0x1,
    0xff, 0x12, 0xfd, 0x0, 0x0, 0x8f, 0x8a, 0xf5,
    0x0, 0x0, 0xe, 0xff, 0xc0, 0x0, 0x0, 0x6,
    0xff, 0x30, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x1, 0xfe, 0x0, 0x0, 0x0, 0x1, 0xfe,
    0x0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0,

    /* U+005A "Z" */
    0xe, 0xff, 0xff, 0xff, 0xf2, 0x9, 0xaa, 0xaa,
    0xbf, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0x60, 0x0,
    0x0, 0x7, 0xfa, 0x0, 0x0, 0x0, 0x3f, 0xe1,
    0x0, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x0, 0xa,
    0xf8, 0x0, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0x0,
    0x2, 0xff, 0x20, 0x0, 0x0, 0xc, 0xf5, 0x0,
    0x0, 0x0, 0x7f, 0xf9, 0x99, 0x99, 0x95, 0x9f,
    0xff, 0xff, 0xff, 0xf9,

    /* U+005B "[" */
    0xbf, 0xff, 0x7b, 0xf6, 0x52, 0xbf, 0x10, 0xb,
    0xf1, 0x0, 0xbf, 0x10, 0xb, 0xf1, 0x0, 0xbf,
    0x10, 0xb, 0xf1, 0x0, 0xbf, 0x10, 0xb, 0xf1,
    0x0, 0xbf, 0x10, 0xb, 0xf1, 0x0, 0xbf, 0x10,
    0xb, 0xf5, 0x52, 0xbf, 0xff, 0x70,

    /* U+005C "\\" */
    0xcf, 0x10, 0x0, 0x6, 0xf7, 0x0, 0x0, 0xe,
    0xe0, 0x0, 0x0, 0x8f, 0x50, 0x0, 0x2, 0xfc,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x5f, 0x90,
    0x0, 0x0, 0xef, 0x0, 0x0, 0x7, 0xf6, 0x0,
    0x0, 0x1f, 0xd0, 0x0, 0x0, 0xaf, 0x40, 0x0,
    0x3, 0xfa,

    /* U+005D "]" */
    0xbf, 0xff, 0x83, 0x58, 0xf8, 0x0, 0x5f, 0x80,
    0x5, 0xf8, 0x0, 0x5f, 0x80, 0x5, 0xf8, 0x0,
    0x5f, 0x80, 0x5, 0xf8, 0x0, 0x5f, 0x80, 0x5,
    0xf8, 0x0, 0x5f, 0x80, 0x5, 0xf8, 0x0, 0x5f,
    0x83, 0x58, 0xf8, 0xbf, 0xff, 0x80,

    /* U+005E "^" */
    0x0, 0x2, 0x87, 0x0, 0x0, 0x0, 0xa, 0xef,
    0x60, 0x0, 0x0, 0x1f, 0x7b, 0xd0, 0x0, 0x0,
    0x8f, 0x3, 0xf5, 0x0, 0x0, 0xf8, 0x0, 0xcc,
    0x0, 0x7, 0xf2, 0x0, 0x5f, 0x30, 0xd, 0xa0,
    0x0, 0xe, 0xa0, 0x5f, 0x30, 0x0, 0x7, 0xf2,

    /* U+005F "_" */
    0x2c, 0xcc, 0xcc, 0xcc, 0x20,

    /* U+0060 "`" */
    0x58, 0x20, 0x1, 0xce, 0x10, 0x0, 0xac, 0x0,

    /* U+0061 "a" */
    0x0, 0x9e, 0xfd, 0x60, 0x9, 0xfa, 0x5c, 0xf4,
    0x4, 0x70, 0x5, 0xf9, 0x0, 0x0, 0x3, 0xfb,
    0x3, 0xad, 0xef, 0xfb, 0x1f, 0xf5, 0x24, 0xfb,
    0x5f, 0xa0, 0x5, 0xfb, 0x2f, 0xe4, 0x4d, 0xfb,
    0x6, 0xef, 0xc5, 0xfb,

    /* U+0062 "b" */
    0xbf, 0x20, 0x0, 0x0, 0xb, 0xf2, 0x0, 0x0,
    0x0, 0xbf, 0x20, 0x0, 0x0, 0xb, 0xf4, 0xaf,
    0xfa, 0x10, 0xbf, 0xda, 0x7c, 0xfb, 0xb, 0xf9,
    0x0, 0xd, 0xf3, 0xbf, 0x40, 0x0, 0x9f, 0x6b,
    0xf2, 0x0, 0x8, 0xf7, 0xbf, 0x30, 0x0, 0x9f,
    0x6b, 0xf9, 0x0, 0xe, 0xf2, 0xbf, 0xd9, 0x7c,
    0xfb, 0xc, 0xf2, 0xbf, 0xe9, 0x0,

    /* U+0063 "c" */
    0x0, 0x7e, 0xfd, 0x60, 0x7, 0xfb, 0x7d, 0xf5,
    0xe, 0xe0, 0x3, 0xc7, 0x2f, 0xb0, 0x0, 0x0,
    0x4f, 0xa0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0,
    0xf, 0xf0, 0x4, 0xe8, 0x8, 0xfc, 0x7d, 0xf4,
    0x0, 0x8e, 0xfd, 0x50,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x7e, 0xfd, 0x4e, 0xf0, 0x7, 0xfd, 0x78, 0xef,
    0xf0, 0xe, 0xf2, 0x0, 0x6f, 0xf0, 0x2f, 0xc0,
    0x0, 0xf, 0xf0, 0x4f, 0xb0, 0x0, 0xe, 0xf0,
    0x3f, 0xc0, 0x0, 0xf, 0xf0, 0xf, 0xf1, 0x0,
    0x4f, 0xf0, 0x9, 0xfc, 0x46, 0xdf, 0xf0, 0x0,
    0x8e, 0xfc, 0x3d, 0xf0,

    /* U+0065 "e" */
    0x0, 0x6d, 0xfe, 0x80, 0x0, 0x7f, 0xc5, 0xaf,
    0x80, 0xf, 0xf1, 0x0, 0xdf, 0x13, 0xfc, 0x0,
    0xa, 0xf4, 0x4f, 0xff, 0xff, 0xff, 0x63, 0xfd,
    0x33, 0x33, 0x31, 0xe, 0xf1, 0x0, 0x34, 0x0,
    0x7f, 0xc6, 0x7f, 0xe1, 0x0, 0x6d, 0xfe, 0xa1,
    0x0,

    /* U+0066 "f" */
    0x0, 0x5d, 0xfe, 0x0, 0xff, 0x65, 0x1, 0xfd,
    0x0, 0x9f, 0xff, 0xfa, 0x36, 0xfe, 0x63, 0x1,
    0xfd, 0x0, 0x1, 0xfd, 0x0, 0x1, 0xfd, 0x0,
    0x1, 0xfd, 0x0, 0x1, 0xfd, 0x0, 0x1, 0xfd,
    0x0, 0x1, 0xfd, 0x0,

    /* U+0067 "g" */
    0x0, 0x8e, 0xfc, 0x2d, 0xf0, 0x7, 0xfe, 0x78,
    0xde, 0xf0, 0xf, 0xf2, 0x0, 0x5f, 0xf0, 0x2f,
    0xd0, 0x0, 0xf, 0xf0, 0x4f, 0xb0, 0x0, 0xe,
    0xf0, 0x2f, 0xd0, 0x0, 0xf, 0xf0, 0xf, 0xf2,
    0x0, 0x5f, 0xf0, 0x8, 0xfe, 0x78, 0xde, 0xf0,
    0x0, 0x9e, 0xfb, 0x2e, 0xf0, 0x3, 0x50, 0x0,
    0x2f, 0xc0, 0x9, 0xfc, 0x78, 0xef, 0x40, 0x0,
    0x8d, 0xff, 0xb4, 0x0,

    /* U+0068 "h" */
    0xcf, 0x20, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x0,
    0xcf, 0x20, 0x0, 0x0, 0xcf, 0x3a, 0xfe, 0x80,
    0xcf, 0xcc, 0xaf, 0xf5, 0xcf, 0xa0, 0x6, 0xfa,
    0xcf, 0x40, 0x3, 0xfb, 0xcf, 0x20, 0x2, 0xfc,
    0xcf, 0x20, 0x2, 0xfc, 0xcf, 0x20, 0x2, 0xfc,
    0xcf, 0x20, 0x2, 0xfc, 0xcf, 0x20, 0x2, 0xfc,

    /* U+0069 "i" */
    0xbf, 0x27, 0xa1, 0x0, 0xb, 0xf2, 0xbf, 0x2b,
    0xf2, 0xbf, 0x2b, 0xf2, 0xbf, 0x2b, 0xf2, 0xbf,
    0x2b, 0xf2,

    /* U+006A "j" */
    0x0, 0xcf, 0x20, 0x7, 0xa1, 0x0, 0x0, 0x0,
    0xc, 0xf2, 0x0, 0xcf, 0x20, 0xc, 0xf2, 0x0,
    0xcf, 0x20, 0xc, 0xf2, 0x0, 0xcf, 0x20, 0xc,
    0xf2, 0x0, 0xcf, 0x20, 0xc, 0xf2, 0x0, 0xcf,
    0x21, 0x6f, 0xf0, 0x4f, 0xe5, 0x0,

    /* U+006B "k" */
    0xcf, 0x20, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x0,
    0xcf, 0x20, 0x0, 0x0, 0xcf, 0x20, 0xc, 0xf5,
    0xcf, 0x20, 0xaf, 0x60, 0xcf, 0x28, 0xf8, 0x0,
    0xcf, 0x8f, 0xb0, 0x0, 0xcf, 0xff, 0xe1, 0x0,
    0xcf, 0x89, 0xfa, 0x0, 0xcf, 0x20, 0xdf, 0x40,
    0xcf, 0x20, 0x4f, 0xe0, 0xcf, 0x20, 0x9, 0xf9,

    /* U+006C "l" */
    0xbf, 0x2b, 0xf2, 0xbf, 0x2b, 0xf2, 0xbf, 0x2b,
    0xf2, 0xbf, 0x2b, 0xf2, 0xbf, 0x2b, 0xf2, 0xbf,
    0x2b, 0xf2,

    /* U+006D "m" */
    0xcf, 0x2b, 0xfe, 0x70, 0x8e, 0xea, 0xc, 0xfc,
    0x76, 0xff, 0xba, 0x6c, 0xf8, 0xbf, 0x80, 0x8,
    0xfd, 0x0, 0x3f, 0xcb, 0xf3, 0x0, 0x6f, 0x90,
    0x0, 0xfe, 0xbf, 0x20, 0x6, 0xf8, 0x0, 0xf,
    0xeb, 0xf2, 0x0, 0x6f, 0x80, 0x0, 0xfe, 0xbf,
    0x20, 0x6, 0xf8, 0x0, 0xf, 0xeb, 0xf2, 0x0,
    0x6f, 0x80, 0x0, 0xfe, 0xbf, 0x20, 0x6, 0xf8,
    0x0, 0xf, 0xe0,

    /* U+006E "n" */
    0xcf, 0x1a, 0xfe, 0x80, 0xcf, 0xc8, 0x6e, 0xf5,
    0xbf, 0x80, 0x5, 0xfa, 0xbf, 0x30, 0x2, 0xfc,
    0xbf, 0x20, 0x2, 0xfc, 0xbf, 0x20, 0x2, 0xfc,
    0xbf, 0x20, 0x2, 0xfc, 0xbf, 0x20, 0x2, 0xfc,
    0xbf, 0x20, 0x2, 0xfc,

    /* U+006F "o" */
    0x0, 0x4c, 0xff, 0xd6, 0x0, 0x5, 0xfe, 0x87,
    0xdf, 0x90, 0xe, 0xf2, 0x0, 0x1f, 0xf1, 0x3f,
    0xd0, 0x0, 0xa, 0xf5, 0x4f, 0xb0, 0x0, 0x9,
    0xf6, 0x2f, 0xd0, 0x0, 0xa, 0xf5, 0xe, 0xf2,
    0x0, 0x1f, 0xf1, 0x5, 0xfe, 0x87, 0xdf, 0x70,
    0x0, 0x4c, 0xff, 0xc6, 0x0,

    /* U+0070 "p" */
    0xcf, 0x3b, 0xff, 0xa1, 0xc, 0xfe, 0x74, 0x9f,
    0xc0, 0xcf, 0x80, 0x0, 0xdf, 0x3c, 0xf3, 0x0,
    0x9, 0xf6, 0xcf, 0x20, 0x0, 0x8f, 0x7c, 0xf4,
    0x0, 0x9, 0xf6, 0xcf, 0x90, 0x0, 0xef, 0x2c,
    0xfe, 0xa7, 0xcf, 0xa0, 0xcf, 0x4b, 0xfe, 0x90,
    0xc, 0xf2, 0x0, 0x0, 0x0, 0xcf, 0x20, 0x0,
    0x0, 0xc, 0xf2, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x7e, 0xfc, 0x3d, 0xf0, 0x7, 0xfd, 0x78,
    0xef, 0xf0, 0xe, 0xf2, 0x0, 0x5f, 0xf0, 0x2f,
    0xd0, 0x0, 0xf, 0xf0, 0x4f, 0xb0, 0x0, 0xe,
    0xf0, 0x2f, 0xd0, 0x0, 0xf, 0xf0, 0xf, 0xf2,
    0x0, 0x6f, 0xf0, 0x8, 0xfd, 0x78, 0xef, 0xf0,
    0x0, 0x8e, 0xfc, 0x3e, 0xf0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0xf, 0xf0,

    /* U+0072 "r" */
    0x0, 0x0, 0x0, 0xcf, 0x2c, 0xf1, 0xcf, 0xba,
    0x70, 0xcf, 0xa0, 0x0, 0xcf, 0x40, 0x0, 0xcf,
    0x20, 0x0, 0xcf, 0x20, 0x0, 0xcf, 0x20, 0x0,
    0xcf, 0x20, 0x0, 0xcf, 0x20, 0x0,

    /* U+0073 "s" */
    0x4, 0xdf, 0xe8, 0x1, 0xfc, 0x49, 0xf5, 0x3f,
    0x80, 0x1, 0x1, 0xff, 0x93, 0x0, 0x5, 0xef,
    0xfd, 0x10, 0x0, 0x3a, 0xfa, 0x14, 0x0, 0xf,
    0xc6, 0xf9, 0x48, 0xf8, 0x8, 0xef, 0xe8, 0x0,

    /* U+0074 "t" */
    0x0, 0x64, 0x0, 0x2, 0xf8, 0x0, 0xaf, 0xff,
    0xf9, 0x39, 0xfb, 0x63, 0x6, 0xf8, 0x0, 0x6,
    0xf8, 0x0, 0x6, 0xf8, 0x0, 0x6, 0xf8, 0x0,
    0x5, 0xf9, 0x0, 0x3, 0xfe, 0x75, 0x0, 0x9f,
    0xe8,

    /* U+0075 "u" */
    0xdf, 0x10, 0x4, 0xfa, 0xdf, 0x10, 0x4, 0xfa,
    0xdf, 0x10, 0x4, 0xfa, 0xdf, 0x10, 0x4, 0xfa,
    0xdf, 0x10, 0x4, 0xfa, 0xdf, 0x10, 0x5, 0xfa,
    0xbf, 0x40, 0x9, 0xfa, 0x7f, 0xd5, 0x8d, 0xfb,
    0x9, 0xef, 0xa2, 0xfb,

    /* U+0076 "v" */
    0xdf, 0x20, 0x0, 0x8f, 0x77, 0xf8, 0x0, 0xd,
    0xf1, 0x1f, 0xd0, 0x2, 0xfb, 0x0, 0xcf, 0x20,
    0x8f, 0x50, 0x6, 0xf7, 0xd, 0xf0, 0x0, 0x1f,
    0xb2, 0xfa, 0x0, 0x0, 0xbf, 0x8f, 0x40, 0x0,
    0x6, 0xff, 0xe0, 0x0, 0x0, 0xf, 0xf9, 0x0,
    0x0,

    /* U+0077 "w" */
    0xcf, 0x0, 0xb, 0xf8, 0x0, 0x3f, 0x98, 0xf4,
    0x0, 0xff, 0xc0, 0x6, 0xf5, 0x4f, 0x80, 0x4f,
    0xaf, 0x0, 0xaf, 0x10, 0xfb, 0x8, 0xf4, 0xf4,
    0xe, 0xc0, 0xc, 0xf0, 0xcc, 0xf, 0x92, 0xf8,
    0x0, 0x8f, 0x4f, 0x70, 0xbd, 0x5f, 0x40, 0x4,
    0xfa, 0xf3, 0x7, 0xfa, 0xf0, 0x0, 0xf, 0xff,
    0x0, 0x3f, 0xfb, 0x0, 0x0, 0xbf, 0xb0, 0x0,
    0xef, 0x70, 0x0,

    /* U+0078 "x" */
    0x7f, 0x90, 0x2, 0xfd, 0x0, 0xcf, 0x30, 0xcf,
    0x40, 0x2, 0xfd, 0x6f, 0x90, 0x0, 0x6, 0xff,
    0xd0, 0x0, 0x0, 0xf, 0xf7, 0x0, 0x0, 0x9,
    0xfe, 0xf2, 0x0, 0x4, 0xfa, 0x3f, 0xc0, 0x0,
    0xee, 0x10, 0x9f, 0x70, 0x9f, 0x60, 0x0, 0xef,
    0x20,

    /* U+0079 "y" */
    0xd, 0xf2, 0x0, 0x8, 0xf7, 0x6, 0xf8, 0x0,
    0xd, 0xf1, 0x1, 0xfd, 0x0, 0x2f, 0xb0, 0x0,
    0xaf, 0x30, 0x8f, 0x50, 0x0, 0x4f, 0x90, 0xde,
    0x0, 0x0, 0xd, 0xe2, 0xf9, 0x0, 0x0, 0x7,
    0xfa, 0xf3, 0x0, 0x0, 0x1, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x1, 0xef,
    0x10, 0x0, 0x2, 0x8d, 0xf7, 0x0, 0x0, 0x3,
    0xfe, 0x70, 0x0, 0x0,

    /* U+007A "z" */
    0x3f, 0xff, 0xff, 0xf3, 0x16, 0x66, 0x7f, 0xf1,
    0x0, 0x0, 0xbf, 0x60, 0x0, 0x7, 0xfa, 0x0,
    0x0, 0x3f, 0xd0, 0x0, 0x1, 0xef, 0x20, 0x0,
    0xb, 0xf6, 0x0, 0x0, 0x6f, 0xe6, 0x66, 0x63,
    0x9f, 0xff, 0xff, 0xf9,

    /* U+007B "{" */
    0x0, 0x3c, 0xf7, 0x0, 0xdf, 0x82, 0x0, 0xfd,
    0x0, 0x0, 0xfc, 0x0, 0x0, 0xfc, 0x0, 0x1,
    0xfb, 0x0, 0x2a, 0xf7, 0x0, 0xdf, 0xa0, 0x0,
    0x4c, 0xf5, 0x0, 0x2, 0xfb, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0xfc, 0x0, 0x0, 0xfd, 0x0, 0x0,
    0xdf, 0x82, 0x0, 0x3d, 0xf7,

    /* U+007C "|" */
    0x8f, 0x48, 0xf4, 0x8f, 0x48, 0xf4, 0x8f, 0x48,
    0xf4, 0x8f, 0x48, 0xf4, 0x8f, 0x48, 0xf4, 0x8f,
    0x48, 0xf4, 0x8f, 0x48, 0xf4, 0x8f, 0x40,

    /* U+007D "}" */
    0xde, 0x90, 0x0, 0x4c, 0xf6, 0x0, 0x3, 0xf9,
    0x0, 0x2, 0xfa, 0x0, 0x2, 0xfa, 0x0, 0x2,
    0xfb, 0x0, 0x0, 0xdf, 0x50, 0x0, 0x2e, 0xf7,
    0x0, 0xcf, 0x82, 0x1, 0xfb, 0x0, 0x2, 0xfa,
    0x0, 0x2, 0xfa, 0x0, 0x3, 0xf9, 0x0, 0x4c,
    0xf7, 0x0, 0xde, 0xa0, 0x0,

    /* U+007E "~" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xd8,
    0x33, 0x73, 0x69, 0x56, 0xbf, 0xff, 0xe2, 0x0,
    0x0, 0x0, 0x33, 0x0,

    /* U+201C "“" */
    0x6c, 0x4, 0xe0, 0xc4, 0xa, 0x70, 0xee, 0xc,
    0xe2, 0xff, 0xc, 0xf3,

    /* U+201D "”" */
    0xef, 0x1c, 0xf4, 0xcf, 0x1a, 0xf3, 0x2f, 0x0,
    0xf2, 0x7b, 0x5, 0xe0, 0x52, 0x4, 0x30,

    /* U+2192 "→" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2b, 0x0, 0x5, 0x55, 0x55,
    0x55, 0x55, 0xbb, 0x0, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcd, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xc4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x0,

    /* U+4E2D "中" */
    0x0, 0x0, 0x1, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf8, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xaf, 0x55, 0x56, 0xfa, 0x55,
    0x5b, 0xf0, 0x9e, 0x0, 0x1, 0xf8, 0x0, 0x8,
    0xf0, 0x9e, 0x0, 0x1, 0xf8, 0x0, 0x8, 0xf0,
    0x9e, 0x0, 0x1, 0xf8, 0x0, 0x8, 0xf0, 0x9f,
    0x77, 0x77, 0xfb, 0x77, 0x7b, 0xf0, 0xaf, 0xee,
    0xee, 0xff, 0xee, 0xef, 0xf0, 0xbf, 0x0, 0x1,
    0xf8, 0x0, 0x7, 0xc1, 0x0, 0x0, 0x1, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xf9, 0x0, 0x0, 0x0,

    /* U+4E3B "主" */
    0x0, 0x0, 0x0, 0x97, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf5, 0x0, 0x0, 0x0,
    0x7, 0x66, 0x66, 0x66, 0xd6, 0x66, 0x67, 0x20,
    0xf, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xef, 0x50,
    0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x21, 0x11, 0x2f, 0x81, 0x11, 0x20, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x33, 0x33, 0x4f, 0x93, 0x33, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x10,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x55, 0x44, 0x44, 0x44, 0x44, 0x44, 0x45, 0x60,

    /* U+4E49 "义" */
    0x0, 0x0, 0x0, 0x16, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf8, 0x0, 0x4, 0x0,
    0x0, 0x0, 0x8, 0x70, 0xa, 0xf7, 0x1, 0xff,
    0x20, 0x0, 0x0, 0xde, 0x0, 0xc, 0xf2, 0x6f,
    0x80, 0x0, 0x0, 0x6, 0xf5, 0x0, 0x15, 0xd,
    0xf1, 0x0, 0x0, 0x0, 0xe, 0xd0, 0x0, 0x5,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x50, 0x0,
    0xde, 0x10, 0x0, 0x0, 0x0, 0x0, 0xde, 0x10,
    0x8f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0x5f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xe9, 0xff, 0x70, 0x0, 0x0, 0x0, 0x4,
    0xcf, 0xd2, 0x5, 0xff, 0xe7, 0x0, 0x0, 0x6c,
    0xff, 0x90, 0x0, 0x2, 0xbf, 0xff, 0xa2, 0x1c,
    0xfc, 0x30, 0x0, 0x0, 0x0, 0x4c, 0xfb, 0x0,
    0x15, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0,

    /* U+4EAE "亮" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xa1, 0x0, 0x0, 0x0, 0x22,
    0x11, 0x11, 0x9f, 0x91, 0x11, 0x12, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x33, 0x48,
    0x77, 0x77, 0x77, 0x78, 0x34, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2f, 0x72,
    0x22, 0x22, 0xaf, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x46, 0x68, 0x76, 0x66,
    0x66, 0x78, 0x56, 0x28, 0xfe, 0xee, 0xee, 0xee,
    0xee, 0xef, 0xf4, 0x8f, 0x0, 0x45, 0x44, 0x45,
    0x20, 0x3f, 0x48, 0xe0, 0xa, 0xff, 0xff, 0xf4,
    0x4, 0xe5, 0x0, 0x0, 0xaf, 0x0, 0x5f, 0x40,
    0x1, 0x0, 0x0, 0x2f, 0xc0, 0x5, 0xf4, 0x0,
    0xe7, 0x25, 0x8f, 0xf5, 0x0, 0x4f, 0x96, 0xbf,
    0x77, 0xff, 0xb3, 0x0, 0x1, 0xcf, 0xfe, 0x90,
    0x4, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5149 "光" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x10,
    0x0, 0x0, 0x7, 0x50, 0x1, 0xf7, 0x0, 0x5e,
    0x40, 0x0, 0x1, 0xdf, 0x60, 0x1f, 0x70, 0x1e,
    0xe5, 0x0, 0x0, 0x1, 0xcf, 0x61, 0xf7, 0xc,
    0xf4, 0x0, 0x0, 0x0, 0x1, 0xef, 0x2f, 0x78,
    0xf6, 0x0, 0x0, 0x1, 0x0, 0x2, 0x31, 0xf7,
    0x4, 0x0, 0x1, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x6, 0x55, 0x55, 0xfc,
    0x5b, 0xf5, 0x55, 0x56, 0x0, 0x0, 0x0, 0x2f,
    0x90, 0x9f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf6, 0x9, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x9f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xc0, 0x9, 0xf0, 0x0, 0x6a, 0x10, 0x0,
    0x3e, 0xf3, 0x0, 0x9f, 0x0, 0x8, 0xf5, 0x4,
    0x9f, 0xf5, 0x0, 0x8, 0xf8, 0x67, 0xee, 0x0,
    0xcf, 0xd3, 0x0, 0x0, 0x2c, 0xff, 0xfd, 0x40,
    0x2, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+51FA "出" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbe, 0x30, 0x0, 0x0, 0x3, 0x63,
    0x0, 0xaf, 0x0, 0x2b, 0x80, 0x6, 0xf6, 0x0,
    0xaf, 0x0, 0x1f, 0x80, 0x5, 0xf3, 0x0, 0xaf,
    0x0, 0x1f, 0x70, 0x5, 0xf3, 0x0, 0xaf, 0x0,
    0x1f, 0x70, 0x5, 0xf3, 0x0, 0xaf, 0x0, 0x1f,
    0x70, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x3, 0x65, 0x55, 0xbf, 0x55, 0x55, 0x40, 0x8,
    0x50, 0x0, 0xaf, 0x0, 0x3, 0xb7, 0xf, 0xb0,
    0x0, 0xaf, 0x0, 0x3, 0xf7, 0xf, 0x90, 0x0,
    0xaf, 0x0, 0x3, 0xf6, 0xf, 0x90, 0x0, 0xaf,
    0x0, 0x3, 0xf6, 0xf, 0xa3, 0x44, 0xbf, 0x66,
    0x79, 0xf6, 0x1f, 0xff, 0xff, 0xfe, 0xed, 0xcd,
    0xf6, 0x3, 0x21, 0x0, 0x0, 0x0, 0x4, 0xf7,

    /* U+51FB "击" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x8, 0xf1, 0x0, 0x1, 0x10,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x44, 0x33, 0x39, 0xf4, 0x33, 0x34, 0x30,
    0x0, 0x0, 0x0, 0x7, 0xf1, 0x0, 0x0, 0x0,
    0x16, 0x55, 0x55, 0x5a, 0xf6, 0x55, 0x55, 0x56,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x1, 0x0, 0x7, 0xf1, 0x0, 0x10, 0x0,
    0x0, 0x1f, 0xc0, 0x7, 0xf1, 0x0, 0x9f, 0x20,
    0x0, 0xf, 0x80, 0x7, 0xf1, 0x0, 0x8f, 0x0,
    0x0, 0xf, 0x80, 0x7, 0xf1, 0x0, 0x8f, 0x0,
    0x0, 0xf, 0x80, 0x7, 0xf1, 0x0, 0x8f, 0x0,
    0x0, 0xf, 0xdb, 0xce, 0xfe, 0xef, 0xff, 0x0,
    0x0, 0x19, 0x98, 0x77, 0x65, 0x54, 0xbf, 0x10,

    /* U+52A0 "加" */
    0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf8, 0x0, 0x3, 0xfe, 0xdd, 0xe8,
    0xe, 0xee, 0xfe, 0xde, 0xb2, 0xfa, 0x67, 0xf7,
    0x7, 0x67, 0xfa, 0x6f, 0xa1, 0xf7, 0x1, 0xf7,
    0x0, 0x2, 0xf6, 0xf, 0x91, 0xf7, 0x1, 0xf7,
    0x0, 0x3, 0xf5, 0xf, 0x81, 0xf7, 0x1, 0xf7,
    0x0, 0x5, 0xf4, 0xf, 0x81, 0xf7, 0x1, 0xf7,
    0x0, 0x7, 0xf1, 0x1f, 0x71, 0xf7, 0x1, 0xf7,
    0x0, 0xb, 0xe0, 0x1f, 0x61, 0xf7, 0x1, 0xf7,
    0x0, 0xf, 0xb0, 0x2f, 0x61, 0xf7, 0x1, 0xf7,
    0x0, 0x5f, 0x50, 0x3f, 0x51, 0xff, 0xee, 0xf7,
    0x0, 0xde, 0x34, 0x8f, 0x41, 0xfa, 0x55, 0xf7,
    0x9, 0xf7, 0x1f, 0xfd, 0x2, 0xf8, 0x2, 0xf8,
    0x3d, 0xc0, 0x5, 0x40, 0x0, 0x42, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5355 "单" */
    0x0, 0x0, 0x1, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xe4, 0x0, 0x3, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xf6, 0x0, 0xcf, 0x20,
    0x0, 0x0, 0x0, 0x45, 0x5e, 0x75, 0x8f, 0xa5,
    0x61, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x9f, 0x0, 0x1f, 0x70,
    0x8, 0xf1, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x9f, 0x44, 0x5f,
    0xa4, 0x4a, 0xf1, 0x0, 0x0, 0x9, 0xf1, 0x12,
    0xf8, 0x11, 0x8f, 0x10, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3, 0x43,
    0x34, 0xf9, 0x33, 0x34, 0x0, 0x0, 0x65, 0x55,
    0x55, 0x5f, 0xa5, 0x55, 0x55, 0x60, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x0, 0x0,

    /* U+53C2 "参" */
    0x0, 0x0, 0x0, 0x7, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x2b, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1c, 0xf5, 0x4, 0xed, 0x20,
    0x0, 0x0, 0x0, 0x7e, 0xf7, 0x45, 0x58, 0xfe,
    0x20, 0x0, 0x0, 0xe, 0xff, 0xff, 0xdc, 0xbb,
    0xee, 0x10, 0x0, 0x10, 0x32, 0xc, 0xf5, 0x0,
    0x2, 0x60, 0x10, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x75, 0x59, 0xfb, 0x5b,
    0x6e, 0xf6, 0x56, 0x70, 0x0, 0x5, 0xfc, 0x1a,
    0xf9, 0x4f, 0xc1, 0x0, 0x0, 0x2a, 0xfb, 0x5d,
    0xf6, 0xac, 0x5e, 0xf7, 0x10, 0x4f, 0xf8, 0xcf,
    0xc3, 0xaf, 0x96, 0x6c, 0xff, 0x40, 0x53, 0x3,
    0x45, 0xdf, 0x62, 0xef, 0x45, 0x60, 0x0, 0x0,
    0x9d, 0xfd, 0x33, 0xee, 0x30, 0x0, 0x0, 0x0,
    0x7, 0xd5, 0x19, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x33, 0x57, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfb, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+540D "名" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x30, 0x0, 0x0, 0x10, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x4,
    0xef, 0x64, 0x44, 0x4a, 0xf8, 0x0, 0x19, 0xfe,
    0x5a, 0x10, 0x5, 0xf9, 0x0, 0x1, 0xad, 0x26,
    0xfe, 0x37, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0xf5, 0x0, 0x0, 0x10, 0x0, 0x16, 0xdf, 0xff,
    0xff, 0xff, 0xfb, 0x8, 0xcf, 0xff, 0xc5, 0x55,
    0x55, 0x5f, 0xa0, 0x7f, 0xa3, 0xea, 0x0, 0x0,
    0x0, 0xfa, 0x0, 0x10, 0xe, 0xa0, 0x0, 0x0,
    0xf, 0xa0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0xfa, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0xfc, 0x33, 0x33, 0x34, 0xfb,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+56DE "回" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xe, 0xc5,
    0x55, 0x55, 0x55, 0x55, 0xeb, 0xe, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xeb, 0xe, 0xa0, 0xff, 0xff,
    0xff, 0x90, 0xeb, 0xe, 0xa0, 0xeb, 0x55, 0x5f,
    0x80, 0xeb, 0xe, 0xa0, 0xe9, 0x0, 0xf, 0x70,
    0xeb, 0xe, 0xa0, 0xe9, 0x0, 0xf, 0x70, 0xeb,
    0xe, 0xa0, 0xea, 0x22, 0x2f, 0x70, 0xeb, 0xe,
    0xa0, 0xff, 0xff, 0xff, 0x80, 0xeb, 0xe, 0xa0,
    0x32, 0x11, 0x12, 0x20, 0xeb, 0xe, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0xeb, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xe, 0xc5, 0x55, 0x55, 0x55,
    0x55, 0xfb, 0xf, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x75,

    /* U+56FE "图" */
    0x5, 0x43, 0x33, 0x33, 0x33, 0x33, 0x44, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf,
    0x80, 0x7, 0xf8, 0x0, 0x0, 0x8f, 0x10, 0xf8,
    0x1, 0xef, 0xee, 0xed, 0x18, 0xf1, 0xf, 0x81,
    0xde, 0x86, 0xdf, 0x40, 0x8f, 0x10, 0xf8, 0xbe,
    0xaf, 0xaf, 0x60, 0x8, 0xf1, 0xf, 0x80, 0x22,
    0xef, 0xe4, 0x0, 0x8f, 0x10, 0xf8, 0x4a, 0xff,
    0x7c, 0xfd, 0x9a, 0xf1, 0xf, 0xdf, 0xf9, 0xed,
    0x66, 0xdf, 0xbf, 0x10, 0xf8, 0x41, 0x22, 0xaf,
    0x80, 0x28, 0xf1, 0xf, 0x80, 0xd, 0xe7, 0x42,
    0x0, 0x8f, 0x10, 0xf8, 0x0, 0x39, 0xff, 0x80,
    0x8, 0xf1, 0xf, 0x81, 0x11, 0x13, 0xbd, 0x11,
    0x8f, 0x11, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x2f, 0xa2, 0x22, 0x22, 0x22, 0x22, 0x8c,
    0x10,

    /* U+5730 "地" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x55, 0x10, 0x0,
    0x0, 0x2, 0xfa, 0x0, 0x55, 0x1b, 0xf2, 0x0,
    0x0, 0x0, 0x1f, 0x70, 0xb, 0xf1, 0xae, 0x0,
    0x0, 0x0, 0x1, 0xf7, 0x0, 0xaf, 0xa, 0xe0,
    0x0, 0x0, 0x0, 0x1f, 0x70, 0x9, 0xf0, 0xae,
    0x1, 0x60, 0x4, 0xed, 0xfe, 0xe7, 0x9f, 0x1b,
    0xfd, 0xff, 0x0, 0x27, 0x7f, 0xaa, 0xad, 0xff,
    0xff, 0x8a, 0xf0, 0x0, 0x1, 0xf7, 0x8f, 0xff,
    0x5a, 0xe0, 0x8f, 0x0, 0x0, 0x1f, 0x72, 0x29,
    0xf0, 0xae, 0x9, 0xf0, 0x0, 0x1, 0xf7, 0x0,
    0x9f, 0xa, 0xe0, 0x9e, 0x0, 0x0, 0x1f, 0x71,
    0x59, 0xf0, 0xae, 0x3c, 0xd0, 0x0, 0x1, 0xfd,
    0xfa, 0x9f, 0xa, 0xfc, 0xf7, 0x0, 0x4b, 0xff,
    0xb5, 0x19, 0xf0, 0xbf, 0x33, 0x22, 0x2, 0xe8,
    0x20, 0x0, 0x9f, 0x3, 0x40, 0x6, 0xf4, 0x0,
    0x0, 0x0, 0x8, 0xfa, 0x88, 0x88, 0xef, 0x10,
    0x0, 0x0, 0x0, 0x1b, 0xef, 0xff, 0xec, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5907 "备" */
    0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xe4, 0x33, 0x33, 0x43,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0x4e, 0xf8, 0xe4, 0x1, 0xcd,
    0x20, 0x0, 0x0, 0x8, 0xe4, 0xb, 0xf8, 0xec,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x37, 0xcf, 0xd6,
    0x9f, 0xe9, 0x52, 0x0, 0x3e, 0xff, 0xfb, 0x40,
    0x0, 0x28, 0xef, 0xff, 0x40, 0x7a, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xb9, 0x80, 0x0, 0x6, 0xf5,
    0x47, 0xf7, 0x44, 0xf8, 0x0, 0x0, 0x0, 0x6f,
    0x44, 0x6f, 0x74, 0x4f, 0x80, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x6f, 0x10, 0x3f, 0x40, 0xf, 0x80, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x7f, 0x54, 0x44, 0x44, 0x5f, 0x90, 0x0,
    0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5B8C "完" */
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xec, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x2, 0xf9, 0x55, 0x55, 0x55, 0x55,
    0x57, 0xf4, 0x0, 0x3f, 0x63, 0x22, 0x22, 0x22,
    0x23, 0x5f, 0x50, 0x2, 0x95, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x62, 0x0, 0x0, 0x3, 0x22, 0x22,
    0x22, 0x23, 0x0, 0x0, 0x0, 0x54, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x51, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x10, 0x0,
    0xdc, 0xa, 0xf0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0xf, 0xa0, 0xaf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xf6, 0xa, 0xf0, 0x0, 0x61, 0x0, 0x0,
    0x5, 0xfd, 0x0, 0xaf, 0x0, 0xd, 0xf0, 0x16,
    0x9d, 0xfd, 0x20, 0x9, 0xf8, 0x7a, 0xfb, 0x0,
    0xbf, 0xe7, 0x0, 0x0, 0x3d, 0xff, 0xeb, 0x20,
    0x1, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5B9A "定" */
    0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfb, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x3, 0xf9, 0x55, 0x55, 0x55, 0x55,
    0x56, 0xf7, 0x0, 0x3f, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x70, 0x1, 0x56, 0x86, 0x55, 0x55,
    0x55, 0x67, 0x74, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x20, 0x1,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xe4,
    0x1f, 0x81, 0x11, 0x21, 0x0, 0x0, 0x0, 0xfc,
    0x1, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x4f,
    0x70, 0x1f, 0x93, 0x33, 0x42, 0x0, 0x0, 0xa,
    0xfc, 0x1, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfb, 0xfd, 0x5f, 0x70, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x4, 0xef, 0xfd, 0x99, 0x89, 0xab, 0x23,
    0xfe, 0x20, 0x0, 0x6a, 0xce, 0xff, 0xff, 0xd0,
    0x3, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+5B9E "实" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xe4, 0x0, 0x0, 0x0, 0x2,
    0x11, 0x11, 0x4e, 0xf3, 0x11, 0x12, 0x11, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x1f, 0x93,
    0x33, 0x34, 0x53, 0x33, 0x5f, 0x51, 0xf8, 0x3d,
    0x30, 0x6f, 0xa0, 0x4, 0xf6, 0x4, 0x23, 0xdf,
    0x96, 0xf5, 0x0, 0x3, 0x10, 0x5, 0x50, 0x7f,
    0xaf, 0x50, 0x0, 0x0, 0x0, 0xaf, 0xc3, 0x25,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xf3, 0x7f,
    0x30, 0x0, 0x0, 0x35, 0x55, 0x5a, 0x5b, 0xf6,
    0x55, 0x55, 0x68, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x1, 0xcf, 0x2b, 0x70,
    0x0, 0x0, 0x0, 0x3, 0xdf, 0x41, 0xaf, 0xd3,
    0x0, 0x1, 0x5b, 0xfd, 0x30, 0x0, 0x5e, 0xf7,
    0x4, 0xff, 0xe7, 0x0, 0x0, 0x0, 0x1b, 0xfa,
    0x6, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7, 0x20,

    /* U+5BF9 "对" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf0,
    0x0, 0x9, 0xfe, 0xee, 0xea, 0x0, 0x0, 0x9f,
    0x0, 0x0, 0x57, 0x66, 0x7f, 0x91, 0x0, 0xa,
    0xf0, 0x10, 0x0, 0x94, 0x3, 0xfa, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x1d, 0xe1, 0x6f, 0x45, 0x44,
    0x4b, 0xf4, 0x41, 0x0, 0x2f, 0xba, 0xe0, 0x39,
    0x0, 0x9f, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x7,
    0xf6, 0x9, 0xf0, 0x0, 0x0, 0x0, 0xcf, 0x50,
    0xd, 0xe1, 0x9f, 0x0, 0x0, 0x0, 0x1f, 0xfc,
    0x0, 0x5f, 0x89, 0xf0, 0x0, 0x0, 0xb, 0xf8,
    0xf7, 0x0, 0x81, 0x9f, 0x0, 0x0, 0x9, 0xf6,
    0xc, 0xf1, 0x0, 0x9, 0xf0, 0x0, 0x1b, 0xf8,
    0x0, 0x4e, 0x30, 0x0, 0x9f, 0x0, 0x1, 0xa8,
    0x0, 0x0, 0x0, 0x8, 0x9e, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5DF2 "已" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x17,
    0x55, 0x55, 0x55, 0x55, 0x6f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xf8, 0x0, 0xd, 0xb0,
    0x0, 0x0, 0x0, 0x1f, 0x80, 0x0, 0xeb, 0x0,
    0x0, 0x0, 0x1, 0xf8, 0x0, 0xe, 0xc5, 0x55,
    0x55, 0x55, 0x6f, 0x80, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xe, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x80, 0xe, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x50, 0xdf, 0x86, 0x66, 0x66, 0x78, 0xcf,
    0xd0, 0x4, 0xce, 0xff, 0xff, 0xff, 0xec, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xb1, 0x0, 0x0,
    0x0, 0x0, 0x45, 0x55, 0x55, 0xaf, 0xd5, 0x55,
    0x56, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x8f, 0x10, 0x1c, 0xa0, 0x8,
    0xc3, 0x0, 0x0, 0x8, 0xf5, 0x66, 0xfb, 0x66,
    0xbf, 0x66, 0x40, 0x0, 0x8f, 0x9e, 0xdf, 0xed,
    0xde, 0xfd, 0xe8, 0x0, 0x8, 0xf1, 0x0, 0xf9,
    0x22, 0x9f, 0x0, 0x0, 0x0, 0x8f, 0x10, 0xf,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x9, 0xf0, 0x33,
    0x43, 0x33, 0x34, 0x40, 0x0, 0x0, 0xaf, 0xd,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xd, 0xc0,
    0x22, 0xec, 0x11, 0xde, 0x10, 0x0, 0x2, 0xf8,
    0x0, 0x4, 0xfb, 0xce, 0x20, 0x0, 0x0, 0x9f,
    0x20, 0x0, 0xa, 0xff, 0x80, 0x0, 0x0, 0x4f,
    0xa0, 0x25, 0x9e, 0xfa, 0xbf, 0xfb, 0x86, 0x32,
    0x92, 0x2e, 0xff, 0x92, 0x0, 0x38, 0xdf, 0xe1,
    0x0, 0x0, 0x45, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x0,

    /* U+5F55 "录" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x7, 0x99, 0x99, 0x99, 0xcf, 0x20, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0xbf, 0xee, 0xee, 0xee, 0xee, 0xff, 0xef, 0xf0,
    0x56, 0x68, 0x55, 0x6f, 0xa5, 0x55, 0x96, 0x70,
    0x0, 0x9e, 0x20, 0x1f, 0xa0, 0x9, 0xf6, 0x0,
    0x0, 0x1e, 0xe1, 0x2f, 0xf4, 0xaf, 0xa4, 0x0,
    0x0, 0x3, 0xc8, 0xff, 0xff, 0xe5, 0x0, 0x0,
    0x0, 0x2, 0xbf, 0x9f, 0x9e, 0xd1, 0x0, 0x0,
    0x0, 0x7f, 0xc2, 0x1f, 0x73, 0xee, 0x60, 0x0,
    0x5e, 0xf8, 0x0, 0x1f, 0x70, 0x1b, 0xfe, 0x82,
    0x1c, 0x40, 0x2a, 0xaf, 0x60, 0x0, 0x5d, 0xa0,
    0x0, 0x0, 0xd, 0xeb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6001 "态" */
    0x0, 0x0, 0x0, 0xa, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x55, 0x55, 0x5f, 0xb5, 0x55, 0x55,
    0x51, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x1, 0xec, 0xe, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x30, 0x4f,
    0x90, 0x0, 0x0, 0x0, 0x1, 0xcf, 0x7e, 0x70,
    0x6f, 0xc3, 0x0, 0x0, 0x38, 0xfe, 0x40, 0x8f,
    0x90, 0x5e, 0xfc, 0x81, 0x2e, 0xfa, 0x10, 0x0,
    0x75, 0x0, 0x18, 0xf8, 0x0, 0x24, 0x0, 0x1,
    0xd7, 0x0, 0x0, 0x1, 0x0, 0x0, 0x6d, 0x5c,
    0xaa, 0xfa, 0x1, 0xda, 0x0, 0x0, 0xd, 0xe1,
    0xfb, 0x7, 0xe2, 0x17, 0xfb, 0x0, 0x7, 0xf6,
    0xe, 0xa0, 0x1, 0xe, 0x66, 0xf8, 0x2, 0xec,
    0x0, 0xed, 0x55, 0x69, 0xf7, 0x4, 0x0, 0x1,
    0x10, 0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,

    /* U+6210 "成" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x5c, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf0, 0xaf,
    0x60, 0x0, 0x2, 0x32, 0x22, 0x22, 0xbf, 0x22,
    0xb4, 0x20, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x8, 0xf3, 0x22, 0x22, 0x8f,
    0x22, 0x22, 0x20, 0x0, 0x7f, 0x10, 0x0, 0x5,
    0xf2, 0xb, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf3,
    0x3f, 0x44, 0xfa, 0x0, 0x0, 0x7f, 0x65, 0x9f,
    0x31, 0xf7, 0x8f, 0x20, 0x0, 0x7, 0xf1, 0x5,
    0xf2, 0xd, 0xbe, 0xc0, 0x0, 0x0, 0x8f, 0x0,
    0x6f, 0x20, 0x9f, 0xf4, 0x0, 0x0, 0xa, 0xf0,
    0x6, 0xf1, 0x5, 0xfc, 0x0, 0x0, 0x0, 0xdb,
    0x78, 0xdf, 0x0, 0xcf, 0xc0, 0x12, 0x0, 0x4f,
    0x68, 0xfe, 0x51, 0xbf, 0xbf, 0x64, 0xe3, 0x2e,
    0xd0, 0x11, 0x7, 0xef, 0x50, 0x9f, 0xdf, 0x22,
    0xb3, 0x0, 0x0, 0x6c, 0x20, 0x0, 0x7e, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6237 "户" */
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x14, 0x32, 0x27, 0xe4, 0x22, 0x33, 0x0, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x3f,
    0x71, 0x11, 0x11, 0x11, 0xea, 0x0, 0x2, 0xf6,
    0x0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x2f, 0x60,
    0x0, 0x0, 0x0, 0xea, 0x0, 0x2, 0xf9, 0x55,
    0x55, 0x55, 0x5e, 0xa0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x5, 0xf4, 0x0, 0x0,
    0x0, 0xf, 0xb0, 0x0, 0x8f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xee, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x2, 0x0, 0x0, 0x1, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xfe, 0x0, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0x0, 0xe, 0xa0, 0x11, 0x11, 0xbc, 0x11,
    0x21, 0x0, 0x0, 0xea, 0x7, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x8, 0x6e, 0xc7, 0x53, 0xa4, 0x33,
    0xb6, 0x31, 0x0, 0xfe, 0xff, 0xe4, 0x3f, 0xc0,
    0x3f, 0xb0, 0x0, 0x0, 0xe, 0xa0, 0x0, 0x6f,
    0x3c, 0xd0, 0x0, 0x0, 0x0, 0xea, 0x7, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x0, 0xe, 0xdb, 0x35,
    0x4e, 0xc5, 0x44, 0x52, 0x0, 0x17, 0xff, 0x95,
    0x35, 0xfb, 0x33, 0x33, 0x51, 0x2f, 0xff, 0xa2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x73, 0xea,
    0x1, 0x4f, 0x60, 0x4f, 0x60, 0x10, 0x0, 0xe,
    0xa0, 0xd, 0xf9, 0x3b, 0xe1, 0x0, 0x0, 0x0,
    0xea, 0x0, 0x27, 0xdf, 0xfb, 0x10, 0x0, 0x0,
    0xe, 0xa0, 0x15, 0x9f, 0xea, 0xff, 0x80, 0x0,
    0x7e, 0xf8, 0x3f, 0xfe, 0x70, 0x1, 0xaf, 0x60,
    0x2, 0x96, 0x0, 0x53, 0x0, 0x0, 0x0, 0x40,
    0x0,

    /* U+6570 "数" */
    0x0, 0x0, 0x12, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x2, 0x24, 0xfa, 0x18, 0x5, 0xfa, 0x0,
    0x0, 0x0, 0xec, 0x4f, 0x4b, 0xf4, 0x8f, 0x20,
    0x0, 0x0, 0x5, 0xe6, 0xf8, 0xf5, 0xc, 0xd0,
    0x0, 0x0, 0x7, 0x9a, 0xbf, 0xbb, 0xa5, 0xfb,
    0x55, 0x56, 0x10, 0x8a, 0xbf, 0xff, 0xab, 0xdf,
    0xff, 0xff, 0xf2, 0x0, 0xb, 0xff, 0xfd, 0x8f,
    0x80, 0x1f, 0x70, 0x0, 0x3d, 0xf7, 0xf6, 0xdc,
    0x95, 0x13, 0xf5, 0x0, 0xc, 0xe3, 0x8a, 0x31,
    0x14, 0xf5, 0x6f, 0x20, 0x0, 0x33, 0x2f, 0xc1,
    0x12, 0xf, 0xba, 0xe0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xa0, 0x9f, 0xf9, 0x0, 0x0, 0x33, 0xfa,
    0x29, 0xf3, 0x1, 0xff, 0x30, 0x0, 0x0, 0x9f,
    0xa4, 0xea, 0x0, 0x4f, 0xf7, 0x0, 0x0, 0x2,
    0x7d, 0xff, 0x91, 0x2e, 0xdb, 0xf8, 0x0, 0x1,
    0x49, 0xfd, 0xaf, 0xbf, 0xe2, 0xd, 0xfb, 0x20,
    0x7f, 0xe8, 0x0, 0x9f, 0xd2, 0x0, 0x1c, 0xf5,
    0x0, 0x50, 0x0, 0x0, 0x60, 0x0, 0x0, 0x3,
    0x0,

    /* U+6587 "文" */
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0xa, 0xa0, 0x0,
    0x0, 0x10, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x46, 0x5b, 0xf7, 0x55, 0x58,
    0xfa, 0x55, 0x60, 0x0, 0x0, 0x2f, 0x90, 0x0,
    0x9f, 0x20, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x10,
    0xe, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa,
    0x7, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf6, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xcf, 0xad, 0xf9, 0x10, 0x0, 0x0, 0x0,
    0x18, 0xff, 0x60, 0xa, 0xff, 0x93, 0x0, 0x5,
    0xaf, 0xfb, 0x20, 0x0, 0x4, 0xdf, 0xfe, 0x20,
    0x5f, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x5c, 0x80,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+65B0 "新" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x1, 0x7d,
    0x40, 0x1, 0x10, 0x9f, 0x40, 0x14, 0xac, 0xff,
    0xb9, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x6f, 0xa4,
    0x0, 0x0, 0x3, 0x5b, 0x43, 0x9a, 0x55, 0xf4,
    0x0, 0x0, 0x0, 0x5, 0xfa, 0xd, 0xd2, 0x4f,
    0x40, 0x0, 0x0, 0x4, 0x4b, 0xd8, 0xf6, 0x56,
    0xfa, 0x88, 0x88, 0x40, 0xef, 0xff, 0xff, 0xff,
    0x9f, 0xdc, 0xfe, 0xd5, 0x1, 0x0, 0x5f, 0x30,
    0x15, 0xf4, 0x1f, 0x70, 0x0, 0x47, 0x7a, 0xf8,
    0x77, 0x5f, 0x41, 0xf7, 0x0, 0x7, 0xed, 0xef,
    0xed, 0xd6, 0xf3, 0x1f, 0x70, 0x0, 0x6, 0x96,
    0xf8, 0xa0, 0x9f, 0x11, 0xf7, 0x0, 0x1, 0xeb,
    0x6f, 0x7f, 0x7d, 0xd0, 0x1f, 0x70, 0x0, 0xcf,
    0x15, 0xf3, 0x89, 0xf7, 0x1, 0xf7, 0x0, 0x6,
    0x57, 0xcf, 0x22, 0xfd, 0x0, 0x1f, 0x80, 0x0,
    0x0, 0x6d, 0x90, 0x3d, 0x30, 0x2, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+65F6 "时" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xc3, 0x0,
    0x16, 0x55, 0x55, 0x30, 0x0, 0x8, 0xf0, 0x0,
    0x1f, 0xff, 0xff, 0x80, 0x0, 0x8, 0xf0, 0x0,
    0x1f, 0x70, 0xf, 0x80, 0x0, 0x8, 0xf0, 0x0,
    0x1f, 0x70, 0xf, 0xbe, 0xdd, 0xde, 0xfd, 0xe5,
    0x1f, 0x70, 0xf, 0x95, 0x44, 0x4a, 0xf4, 0x52,
    0x1f, 0xff, 0xff, 0x82, 0x70, 0x8, 0xf0, 0x0,
    0x1f, 0x95, 0x5f, 0x87, 0xf5, 0x8, 0xf0, 0x0,
    0x1f, 0x70, 0xf, 0x80, 0xde, 0x8, 0xf0, 0x0,
    0x1f, 0x70, 0xf, 0x80, 0x5f, 0x48, 0xf0, 0x0,
    0x1f, 0x70, 0xf, 0x80, 0x1, 0x8, 0xf0, 0x0,
    0x1f, 0xfe, 0xef, 0x80, 0x0, 0x8, 0xf0, 0x0,
    0x1f, 0xa6, 0x6f, 0x80, 0x0, 0x8, 0xf0, 0x0,
    0x2d, 0x70, 0x8, 0x40, 0x1c, 0xbe, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+672C "本" */
    0x0, 0x0, 0x0, 0x1, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0,
    0x0, 0x0, 0x3, 0xfe, 0xdd, 0xdd, 0xfe, 0xdd,
    0xdd, 0xea, 0x0, 0x18, 0x76, 0x68, 0xff, 0xfb,
    0x66, 0x67, 0x50, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xcf,
    0xcf, 0x60, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf3,
    0xf7, 0xce, 0x10, 0x0, 0x0, 0x0, 0x5, 0xf8,
    0x1f, 0x74, 0xfb, 0x0, 0x0, 0x0, 0x2, 0xfc,
    0x1, 0xf7, 0x9, 0xf9, 0x0, 0x0, 0x2, 0xef,
    0x41, 0x2f, 0x81, 0x2e, 0xf9, 0x0, 0x5, 0xee,
    0x7f, 0xff, 0xff, 0xff, 0xbd, 0xfd, 0x31, 0xdd,
    0x21, 0x43, 0x4f, 0x93, 0x42, 0x1b, 0xc2, 0x0,
    0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x0, 0x0,

    /* U+673A "机" */
    0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf1, 0x0, 0xaf, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x7f, 0x10, 0x9, 0xe5, 0x5c,
    0xf0, 0x0, 0x14, 0x38, 0xf4, 0x33, 0x9e, 0x0,
    0xaf, 0x0, 0x4, 0xff, 0xff, 0xff, 0xa9, 0xe0,
    0xa, 0xf0, 0x0, 0x2, 0x1e, 0xf2, 0x22, 0x9e,
    0x0, 0xae, 0x0, 0x0, 0x4, 0xff, 0xc0, 0x9,
    0xe0, 0xa, 0xe0, 0x0, 0x0, 0xcf, 0xff, 0xc0,
    0xae, 0x0, 0xae, 0x0, 0x0, 0x5f, 0xdf, 0x8f,
    0x2b, 0xc0, 0xb, 0xe0, 0x0, 0x2e, 0xd7, 0xf1,
    0x30, 0xea, 0x0, 0xbe, 0x0, 0x6, 0xe4, 0x7f,
    0x10, 0x2f, 0x50, 0xb, 0xd0, 0x0, 0x0, 0x7,
    0xf1, 0x9, 0xf1, 0x0, 0xbd, 0x24, 0x0, 0x0,
    0x7f, 0x14, 0xf7, 0x0, 0xb, 0xd3, 0xf8, 0x0,
    0x7, 0xf5, 0xfd, 0x0, 0x0, 0xbf, 0xbf, 0x50,
    0x0, 0x8f, 0x6c, 0x10, 0x0, 0x6, 0xef, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6BD4 "比" */
    0x3, 0x30, 0x0, 0x1, 0x75, 0x0, 0x0, 0x0,
    0xa, 0xf5, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x1, 0xf8, 0x0, 0x11, 0x0,
    0x9, 0xf0, 0x0, 0x1, 0xf8, 0x3, 0xed, 0x10,
    0x9, 0xf3, 0x34, 0x51, 0xf9, 0x8f, 0xe8, 0x20,
    0x9, 0xff, 0xff, 0xf1, 0xff, 0xf8, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x11, 0xfb, 0x10, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x11, 0xf8, 0x0, 0x2, 0x0,
    0x9, 0xf0, 0x1a, 0xa1, 0xf8, 0x0, 0xb, 0xa1,
    0x9, 0xf8, 0xff, 0x61, 0xf8, 0x0, 0xc, 0xf0,
    0x1d, 0xff, 0x91, 0x0, 0xfc, 0x66, 0x9f, 0xa0,
    0xd, 0xb2, 0x0, 0x0, 0x8f, 0xff, 0xfb, 0x20,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6D41 "流" */
    0x0, 0x0, 0x0, 0x0, 0x5, 0x10, 0x0, 0x0,
    0x0, 0x2, 0x30, 0x0, 0x2, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x3, 0xfe, 0x10,
    0x1, 0x0, 0x1, 0xcf, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0xb4, 0x65, 0x9f, 0xd5,
    0x75, 0x56, 0x0, 0x52, 0x0, 0x0, 0xd, 0xd1,
    0x7f, 0x40, 0x0, 0xe, 0xf6, 0x0, 0xa, 0xf2,
    0x1, 0xdf, 0x40, 0x0, 0x2c, 0xf8, 0xd, 0xff,
    0xef, 0xff, 0xff, 0x20, 0x0, 0x8, 0x10, 0x68,
    0x75, 0x43, 0x23, 0xf6, 0x0, 0x0, 0x7, 0x1,
    0xca, 0x6b, 0x5a, 0xb4, 0x0, 0x0, 0x4, 0xf5,
    0xf, 0x96, 0xf3, 0xbd, 0x0, 0x0, 0x0, 0xbf,
    0x11, 0xf7, 0x6f, 0x2b, 0xc0, 0x0, 0x0, 0x3f,
    0x80, 0x3f, 0x66, 0xf2, 0xbc, 0x0, 0x0, 0xc,
    0xf1, 0xa, 0xf2, 0x6f, 0x2b, 0xc0, 0xa2, 0x6,
    0xf8, 0x8, 0xfb, 0x6, 0xf2, 0xbd, 0x5f, 0x80,
    0x6e, 0x13, 0xed, 0x10, 0x7f, 0x37, 0xff, 0xd1,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0,

    /* U+6DFB "添" */
    0x0, 0x84, 0x0, 0x10, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x1, 0xdf, 0xc3, 0xbf, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x4, 0xd8, 0x44, 0x4c, 0xf4,
    0x44, 0x20, 0x0, 0x0, 0x0, 0x16, 0x55, 0x5e,
    0xd5, 0x55, 0x55, 0x0, 0xb, 0x30, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x2d, 0xf9, 0x10,
    0x2, 0xed, 0x2e, 0xc1, 0x0, 0x0, 0x0, 0x8f,
    0x50, 0x4e, 0xe3, 0x2, 0xef, 0x72, 0x0, 0x0,
    0x3, 0x4c, 0xfd, 0x2f, 0xd0, 0x1a, 0xff, 0x90,
    0x0, 0x2, 0x5b, 0x91, 0xf, 0xa0, 0x6, 0x58,
    0x0, 0x0, 0x8, 0xe0, 0x3f, 0x8e, 0xbb, 0x8f,
    0xb0, 0x0, 0x0, 0x1f, 0xb0, 0xaf, 0x2e, 0x9d,
    0xe7, 0xf5, 0x0, 0x0, 0x8f, 0x35, 0xf7, 0xe,
    0x95, 0xf5, 0xde, 0x0, 0x2, 0xfb, 0x1e, 0xd0,
    0xe, 0x90, 0xf8, 0x6b, 0x20, 0xc, 0xf3, 0x0,
    0x25, 0x8f, 0x90, 0x0, 0x0, 0x0, 0x5, 0xa0,
    0x0, 0x3, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+70B9 "点" */
    0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xa, 0xf5, 0x55, 0x55, 0x61,
    0x0, 0x1, 0x0, 0xa, 0xf0, 0x0, 0x11, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x9, 0xf4, 0x44, 0x44, 0x45, 0xf7, 0x0,
    0x0, 0x9, 0xf0, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x9, 0xf2, 0x22, 0x22, 0x23, 0xf7, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x3, 0x32, 0x22, 0x22, 0x22, 0x33, 0x0,
    0x0, 0x2c, 0x42, 0x81, 0x2b, 0x30, 0x8f, 0x20,
    0x0, 0xbf, 0x75, 0xf7, 0xe, 0xb0, 0x4f, 0xc0,
    0x7, 0xf9, 0x0, 0xfc, 0x7, 0xf3, 0xa, 0xf6,
    0x2e, 0xd0, 0x0, 0xb9, 0x1, 0xa1, 0x2, 0xf7,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,

    /* U+7259 "牙" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x35, 0x68, 0x44, 0x44, 0xcf, 0x45, 0x60,
    0x0, 0x0, 0x9f, 0x90, 0x0, 0xae, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0xae, 0x0, 0x0,
    0x0, 0x9, 0xf3, 0x0, 0x0, 0xae, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x29, 0x55, 0x55, 0xbf, 0xff, 0x55, 0x56,
    0x0, 0x0, 0x0, 0x7, 0xfa, 0xae, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xaf, 0x90, 0xae, 0x0, 0x0,
    0x0, 0x0, 0x5e, 0xf6, 0x0, 0xae, 0x0, 0x0,
    0x0, 0x4c, 0xfc, 0x30, 0x0, 0xae, 0x0, 0x0,
    0xb, 0xfe, 0x70, 0x0, 0x0, 0xae, 0x0, 0x0,
    0x1, 0x71, 0x0, 0x2, 0xba, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xb3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+72B6 "状" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf4, 0x0, 0xa, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x0, 0x0, 0xaf, 0x3b,
    0x10, 0x0, 0x12, 0xa, 0xf0, 0x0, 0xa, 0xf3,
    0xfc, 0x0, 0xd, 0xd1, 0xaf, 0x0, 0x0, 0xae,
    0x6, 0xf8, 0x0, 0x5f, 0xaa, 0xf0, 0x10, 0xb,
    0xe0, 0x7, 0x21, 0x0, 0x9f, 0xef, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x1, 0x8a, 0xf2, 0x54,
    0x5f, 0xf7, 0x44, 0x52, 0x0, 0x2, 0xaf, 0x0,
    0x4, 0xff, 0x80, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0x7f, 0xee, 0x0, 0x0, 0x0, 0xaf, 0xdf,
    0x0, 0xe, 0xe6, 0xf6, 0x0, 0x0, 0x9f, 0x6a,
    0xf0, 0x5, 0xf7, 0xe, 0xe0, 0x0, 0x6f, 0xa0,
    0xaf, 0x2, 0xfe, 0x0, 0x6f, 0xa0, 0x0, 0x80,
    0xa, 0xf1, 0xdf, 0x30, 0x0, 0xbf, 0x80, 0x0,
    0x0, 0xaf, 0xef, 0x70, 0x0, 0x1, 0xef, 0x90,
    0x0, 0xb, 0xf7, 0x70, 0x0, 0x0, 0x2, 0x80,

    /* U+7387 "率" */
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf8, 0x10, 0x0, 0x0,
    0x0, 0x4, 0x44, 0x44, 0x5d, 0xfa, 0x44, 0x44,
    0x50, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x2, 0xa1, 0x0, 0x5f, 0xa7, 0x40,
    0x6d, 0x20, 0x0, 0x3f, 0xe2, 0x4e, 0xa4, 0xfd,
    0x6f, 0xa1, 0x0, 0x0, 0x2e, 0x9d, 0xff, 0xfd,
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x2a, 0x22, 0xdd,
    0x85, 0x47, 0x0, 0x0, 0x0, 0xb, 0xf3, 0xcd,
    0x1b, 0xe6, 0xfa, 0x0, 0x0, 0xa, 0xf5, 0xef,
    0xff, 0xff, 0x97, 0xfa, 0x0, 0x5, 0xf7, 0x6,
    0x8c, 0xe6, 0x9d, 0xa, 0xa0, 0x2, 0x57, 0x44,
    0x44, 0xbf, 0x55, 0x44, 0x44, 0x51, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x9f, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x0, 0x0,

    /* U+7528 "用" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x8f, 0x44, 0x49, 0xf5, 0x44, 0x9f, 0x10, 0x8,
    0xf0, 0x0, 0x7f, 0x10, 0x7, 0xf1, 0x0, 0x8f,
    0x44, 0x4a, 0xf5, 0x44, 0xaf, 0x10, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x8f, 0x0,
    0x7, 0xf1, 0x0, 0x7f, 0x10, 0x8, 0xf0, 0x0,
    0x7f, 0x10, 0x7, 0xf1, 0x0, 0x8f, 0xee, 0xef,
    0xfe, 0xee, 0xff, 0x10, 0x9, 0xf6, 0x66, 0xaf,
    0x66, 0x6a, 0xf1, 0x0, 0xbd, 0x0, 0x7, 0xf1,
    0x0, 0x7f, 0x10, 0xf, 0xa0, 0x0, 0x7f, 0x10,
    0x7, 0xf1, 0x7, 0xf5, 0x0, 0x8, 0xf1, 0x0,
    0x7f, 0x12, 0xed, 0x0, 0x0, 0x7d, 0x24, 0x9d,
    0xf0, 0x7f, 0x40, 0x0, 0x0, 0x0, 0xf, 0xe8,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7535 "电" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf1, 0x0, 0x0, 0x0, 0x1, 0x76,
    0x66, 0xaf, 0x66, 0x66, 0x74, 0x0, 0x2f, 0xfe,
    0xef, 0xfe, 0xee, 0xef, 0x80, 0x1, 0xf7, 0x0,
    0x7f, 0x10, 0x1, 0xf7, 0x0, 0x1f, 0x70, 0x7,
    0xf1, 0x0, 0x1f, 0x70, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x1f, 0xa4, 0x49, 0xf5,
    0x44, 0x5f, 0x70, 0x1, 0xf7, 0x0, 0x7f, 0x10,
    0x1, 0xf7, 0x0, 0x1f, 0xa5, 0x59, 0xf6, 0x55,
    0x6f, 0x70, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x7, 0xf1, 0x0, 0x0,
    0x7, 0x10, 0x0, 0x0, 0x7f, 0x10, 0x0, 0x0,
    0xfd, 0x0, 0x0, 0x6, 0xfa, 0x77, 0x77, 0xcf,
    0x70, 0x0, 0x0, 0x1b, 0xef, 0xff, 0xfe, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+767B "登" */
    0x0, 0x1, 0x0, 0x0, 0x28, 0x30, 0xb3, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xf9, 0xeb, 0xdf, 0x91,
    0x0, 0x0, 0x1a, 0x54, 0xde, 0x17, 0xfb, 0x2a,
    0xe1, 0x0, 0x1, 0xec, 0x8f, 0x40, 0xc, 0xed,
    0xe8, 0x20, 0x0, 0x3, 0xff, 0x60, 0x0, 0x1d,
    0xf7, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x84, 0x17, 0xee, 0x66, 0x55, 0x55,
    0x55, 0x57, 0xff, 0x71, 0xcd, 0x26, 0x65, 0x55,
    0x55, 0x56, 0x41, 0x80, 0x1, 0x0, 0xcf, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xb, 0xc0,
    0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x6,
    0x7c, 0x55, 0x5b, 0xe7, 0x40, 0x0, 0x0, 0x0,
    0x9, 0xf4, 0x0, 0xee, 0x30, 0x0, 0x0, 0x15,
    0x44, 0x5f, 0xd4, 0x8f, 0x94, 0x45, 0x40, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+786E "确" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x6, 0x55, 0x55, 0x61, 0x8f, 0xca, 0xaa, 0x60,
    0x1f, 0xff, 0xff, 0xf7, 0xfc, 0x9a, 0xfe, 0x10,
    0x0, 0xf, 0x90, 0x2e, 0xd1, 0xa, 0xf4, 0x0,
    0x0, 0x4f, 0x44, 0xef, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xbe, 0x25, 0xbc, 0xf5, 0x9f, 0x55, 0xf7,
    0x4, 0xff, 0xff, 0x68, 0xf4, 0x9f, 0x55, 0xf7,
    0x2e, 0xfb, 0x3f, 0x58, 0xff, 0xff, 0xff, 0xf7,
    0x4d, 0xea, 0x1f, 0x58, 0xf0, 0x6f, 0x11, 0xf7,
    0x0, 0xda, 0x1f, 0x59, 0xfa, 0xcf, 0xaa, 0xf7,
    0x0, 0xdb, 0x5f, 0x5b, 0xfa, 0xcf, 0xaa, 0xf7,
    0x0, 0xdf, 0xff, 0x6f, 0xb0, 0x6f, 0x11, 0xf7,
    0x0, 0xea, 0x2f, 0xdf, 0x50, 0x7f, 0x21, 0xf7,
    0x0, 0x75, 0x3, 0xfd, 0x0, 0x37, 0x45, 0xf6,
    0x0, 0x0, 0x2e, 0xf3, 0x0, 0x0, 0xef, 0xf3,
    0x0, 0x0, 0x3, 0x50, 0x0, 0x0, 0x46, 0x20,

    /* U+79F0 "称" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x55, 0x0, 0x8b, 0x30,
    0x0, 0x0, 0x2, 0x79, 0xbe, 0xff, 0x30, 0xde,
    0x10, 0x0, 0x0, 0x0, 0xde, 0xef, 0x51, 0x2,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x10,
    0x7, 0xff, 0xff, 0xff, 0xd0, 0x2, 0x76, 0xaf,
    0x77, 0x2e, 0xc4, 0x44, 0x7f, 0x70, 0x3, 0xed,
    0xff, 0xee, 0xcf, 0x35, 0x83, 0xbf, 0x10, 0x0,
    0x0, 0xef, 0x40, 0x68, 0x7, 0xf3, 0x34, 0x0,
    0x0, 0x6, 0xff, 0xf4, 0x0, 0x7, 0xf1, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xcf, 0x2c, 0xa8, 0xf5,
    0xd2, 0x0, 0x0, 0x9f, 0xaf, 0x28, 0x3f, 0x97,
    0xf2, 0xec, 0x0, 0x8, 0xf9, 0x7f, 0x10, 0xbe,
    0x7, 0xf1, 0x4f, 0x80, 0x1, 0x61, 0x7f, 0x16,
    0xf5, 0x7, 0xf1, 0x9, 0xf4, 0x0, 0x0, 0x7f,
    0x17, 0xa0, 0x7, 0xf1, 0x0, 0x81, 0x0, 0x0,
    0x7f, 0x10, 0x4, 0xad, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x20, 0x1, 0xec, 0x50, 0x0, 0x0,

    /* U+7A0B "程" */
    0x0, 0x0, 0x15, 0x60, 0x0, 0x0, 0x0, 0x10,
    0x1, 0xde, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
    0x0, 0x9, 0xcd, 0xf6, 0x33, 0xf9, 0x44, 0x4b,
    0xe0, 0x0, 0x0, 0x7f, 0x10, 0x1f, 0x70, 0x0,
    0x9e, 0x0, 0x8, 0x7b, 0xf7, 0x77, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xfe, 0xff, 0xef, 0x84, 0x33,
    0x33, 0x35, 0x0, 0x0, 0xc, 0xf5, 0x7, 0x65,
    0x55, 0x55, 0x65, 0x0, 0x3, 0xff, 0xf6, 0xde,
    0xee, 0xfe, 0xee, 0xb0, 0x0, 0xbf, 0xfb, 0xf5,
    0x0, 0x7f, 0x0, 0x0, 0x0, 0x5f, 0xdf, 0x1a,
    0x45, 0x4a, 0xf4, 0x45, 0x10, 0x2e, 0xd7, 0xf1,
    0x8, 0xff, 0xff, 0xff, 0xf3, 0x4, 0xc4, 0x7f,
    0x10, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0, 0x7,
    0xf1, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x8, 0xf2, 0x44, 0x44, 0x44, 0x44, 0x44, 0x20,

    /* U+7CFB "系" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x48, 0xd2, 0x0,
    0x98, 0x89, 0xab, 0xde, 0xff, 0xfe, 0xc0, 0xa,
    0xed, 0xce, 0xfe, 0x64, 0x31, 0x0, 0x0, 0x0,
    0x7, 0xfd, 0x50, 0x2d, 0xd1, 0x0, 0x0, 0x3b,
    0xfb, 0x22, 0x5e, 0xf7, 0x10, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x75, 0x36,
    0xef, 0x60, 0xca, 0x0, 0x0, 0x0, 0x5b, 0xf9,
    0x10, 0x7, 0xfb, 0x0, 0xb, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xf8, 0x0, 0x6a, 0x87, 0x66, 0xf9,
    0x22, 0x1a, 0x90, 0x0, 0x9, 0xd4, 0x1f, 0x81,
    0xda, 0x10, 0x0, 0x7, 0xfc, 0x31, 0xf8, 0x7,
    0xfd, 0x20, 0x1b, 0xfb, 0x0, 0x1f, 0x80, 0x4,
    0xff, 0x47, 0xf9, 0x1, 0x9a, 0xf7, 0x0, 0x3,
    0xd3, 0x2, 0x0, 0xc, 0xfb, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xb4, 0x0, 0x0, 0xce, 0x20, 0x0,
    0x0, 0x0, 0x5f, 0xb0, 0x0, 0x3, 0xee, 0x20,
    0x0, 0x0, 0xb, 0xf1, 0x12, 0x55, 0x58, 0xf6,
    0x55, 0x60, 0x3, 0xf7, 0x5e, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xbe, 0xb, 0xf5, 0x1, 0xef,
    0x35, 0x20, 0x0, 0x6f, 0xff, 0xf9, 0x0, 0x9f,
    0x41, 0xed, 0x0, 0x0, 0x86, 0xdf, 0x10, 0x6f,
    0x81, 0x37, 0xfb, 0x0, 0x0, 0x3f, 0x70, 0x5f,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0xd, 0xd0, 0x26,
    0x9b, 0xc6, 0x87, 0x1b, 0x70, 0xe, 0xfe, 0xff,
    0x90, 0x6f, 0x5a, 0xf0, 0x0, 0x0, 0x8a, 0x74,
    0x10, 0x7, 0xf2, 0x9f, 0x0, 0x0, 0x0, 0x0,
    0x26, 0x90, 0x9f, 0x9, 0xf0, 0x10, 0x2, 0x8b,
    0xef, 0xe8, 0x2e, 0xb0, 0x9f, 0x3, 0xe4, 0x2f,
    0xd8, 0x31, 0x7f, 0xf2, 0x9, 0xf8, 0xbf, 0x40,
    0x10, 0x0, 0x9f, 0xc2, 0x0, 0x4d, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7F16 "编" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xcd, 0x20, 0x0, 0xaf, 0x60, 0x0,
    0x0, 0x0, 0x2f, 0xa0, 0x1, 0x1, 0xcf, 0x20,
    0x0, 0x0, 0x9, 0xf1, 0x62, 0xef, 0xff, 0xff,
    0xff, 0x80, 0x1, 0xf9, 0xe, 0xee, 0xc4, 0x44,
    0x45, 0xf7, 0x0, 0xaf, 0x47, 0xf4, 0xcb, 0x0,
    0x0, 0x1f, 0x70, 0x2f, 0xff, 0xf9, 0xc, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x43, 0xae, 0x0, 0xdb,
    0x33, 0x33, 0x34, 0x40, 0x0, 0x5f, 0x30, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x5f, 0xda, 0xe7,
    0xff, 0x7f, 0x7f, 0xac, 0xe0, 0xd, 0xff, 0xc9,
    0x4f, 0xf4, 0xf4, 0xf8, 0xae, 0x0, 0x43, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x2, 0x58,
    0xcf, 0xdf, 0xf7, 0xf7, 0xfa, 0xbe, 0x0, 0xff,
    0xeb, 0xbf, 0xaf, 0x4f, 0x4f, 0x9a, 0xe0, 0x5,
    0x30, 0x2e, 0xc6, 0xf5, 0xa4, 0xb9, 0xce, 0x0,
    0x0, 0x0, 0x11, 0x7f, 0x30, 0x4, 0xfe, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0,

    /* U+7F6E "置" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x4,
    0xf5, 0x2c, 0xc2, 0x5f, 0x52, 0xbe, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x5, 0x65,
    0x55, 0x8f, 0x95, 0x55, 0x56, 0x32, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x2, 0x25, 0x44,
    0xde, 0x44, 0x44, 0x50, 0x20, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x1f, 0xb8, 0x88,
    0x88, 0x8e, 0xb0, 0x0, 0x1, 0xfb, 0x88, 0x88,
    0x88, 0xeb, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x1, 0xf9, 0x55, 0x55, 0x55,
    0xdb, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x5, 0x45, 0xf9, 0x44, 0x44, 0x44, 0xdc,
    0x45, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+80CC "背" */
    0x0, 0x0, 0x0, 0x20, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xc0, 0xaf, 0x40, 0x1,
    0x0, 0x3, 0x54, 0x46, 0xf6, 0x9, 0xf0, 0x5c,
    0xe2, 0x0, 0x8f, 0xff, 0xff, 0x50, 0x9f, 0xff,
    0xb6, 0x30, 0x0, 0x0, 0x2, 0xf5, 0x9, 0xf5,
    0x0, 0x45, 0x0, 0x2, 0x58, 0xcf, 0x50, 0x8f,
    0x20, 0xa, 0xf3, 0x5f, 0xff, 0xca, 0xf6, 0x5,
    0xff, 0xff, 0xfb, 0x0, 0xa5, 0x26, 0x6a, 0x74,
    0x46, 0x9a, 0x83, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x1f, 0x94,
    0x44, 0x44, 0x6f, 0x50, 0x0, 0x0, 0x1, 0xfe,
    0xee, 0xee, 0xee, 0xf5, 0x0, 0x0, 0x0, 0x1f,
    0x94, 0x44, 0x44, 0x5f, 0x50, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x1f, 0x60, 0x0, 0x0, 0x2f, 0x50, 0x0, 0x0,
    0x1, 0xf6, 0x0, 0x2, 0x57, 0xf5, 0x0, 0x0,
    0x0, 0x1f, 0x80, 0x0, 0x1f, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x42, 0x0, 0x0,
    0x0,

    /* U+81EA "自" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xea, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x40, 0x0, 0x0, 0x0, 0x65, 0x5f, 0xd5, 0x55,
    0x55, 0x61, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0xfa, 0x0, 0x0, 0x0, 0x9, 0xf1, 0xf,
    0xb2, 0x22, 0x22, 0x22, 0xaf, 0x10, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0xf, 0xb3, 0x33, 0x33,
    0x33, 0xaf, 0x10, 0xfa, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0xfc, 0x55, 0x55, 0x55, 0x5b, 0xf1, 0xf, 0xa0,
    0x0, 0x0, 0x0, 0x9f, 0x10, 0xfc, 0x66, 0x66,
    0x66, 0x6c, 0xf1, 0xf, 0xfe, 0xee, 0xee, 0xee,
    0xff, 0x21, 0xfb, 0x0, 0x0, 0x0, 0x9, 0xe2,

    /* U+83DC "菜" */
    0x0, 0x0, 0x2, 0x10, 0x0, 0x21, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x30, 0xd, 0xf2, 0x0,
    0x0, 0x4, 0x44, 0x4c, 0xe4, 0x44, 0xdd, 0x44,
    0x44, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x1, 0x0, 0xb, 0xe0, 0x0, 0xdd,
    0x45, 0x11, 0x0, 0x6, 0x76, 0xab, 0x89, 0xbf,
    0xff, 0xf2, 0x0, 0x0, 0x6f, 0xff, 0xef, 0xeb,
    0x97, 0xd5, 0x10, 0x0, 0x0, 0x2d, 0x60, 0xde,
    0x10, 0x7f, 0xb0, 0x0, 0x0, 0x0, 0xaf, 0x43,
    0xf8, 0x3f, 0xc0, 0x0, 0x0, 0x20, 0x0, 0xb3,
    0x2f, 0x91, 0x71, 0x0, 0x20, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x53, 0x33,
    0x5f, 0xff, 0xff, 0x73, 0x34, 0x50, 0x0, 0x0,
    0x5e, 0xe4, 0xf8, 0xcf, 0x92, 0x0, 0x0, 0x4,
    0xbf, 0xb1, 0x1f, 0x70, 0x7f, 0xfc, 0x72, 0x1c,
    0xfe, 0x50, 0x1, 0xf7, 0x0, 0x18, 0xea, 0x0,
    0x16, 0x0, 0x0, 0x2f, 0x80, 0x0, 0x0, 0x0,

    /* U+84DD "蓝" */
    0x0, 0x0, 0x7c, 0x30, 0xb, 0xb0, 0x0, 0x0,
    0x55, 0x55, 0xaf, 0x55, 0x5c, 0xd5, 0x55, 0x60,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x8f, 0x0, 0xc, 0xc0, 0x0, 0x0,
    0x0, 0x31, 0x2f, 0x80, 0x5f, 0x70, 0x0, 0x0,
    0x0, 0xce, 0xe, 0xa0, 0xdf, 0x86, 0x67, 0x20,
    0x0, 0xbc, 0xe, 0x97, 0xfe, 0xee, 0xef, 0x40,
    0x0, 0xbc, 0xe, 0xdf, 0xc2, 0xd5, 0x0, 0x0,
    0x0, 0xcc, 0xe, 0xcc, 0x20, 0xaf, 0x40, 0x0,
    0x0, 0xaa, 0x2d, 0xa2, 0x22, 0x2b, 0x60, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0xf8, 0x2d, 0xb2, 0xae, 0x28, 0xf1, 0x0,
    0x0, 0xf7, 0xc, 0xb0, 0x9e, 0x7, 0xf1, 0x0,
    0x55, 0xf9, 0x4d, 0xc4, 0xaf, 0x49, 0xf5, 0x50,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,

    /* U+89C6 "视" */
    0x0, 0x6, 0x20, 0x0, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x1d, 0xf5, 0x0, 0x9f, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x8f, 0x33,
    0x33, 0xea, 0x0, 0x1, 0x33, 0x39, 0x64, 0x8f,
    0x7, 0x61, 0xea, 0x0, 0x5, 0xff, 0xff, 0xf9,
    0x8f, 0xd, 0xf1, 0xea, 0x0, 0x0, 0x10, 0xc,
    0xf1, 0x8f, 0xc, 0xc0, 0xea, 0x0, 0x0, 0x0,
    0x8f, 0x60, 0x8f, 0xd, 0xc0, 0xea, 0x0, 0x0,
    0x6, 0xff, 0x30, 0x8f, 0xe, 0xa0, 0xea, 0x0,
    0x0, 0x7f, 0xff, 0xf3, 0x8f, 0x2f, 0xd4, 0xea,
    0x0, 0xb, 0xfd, 0xfb, 0xef, 0xaf, 0x8f, 0xf8,
    0xea, 0x0, 0x1, 0xa1, 0xe9, 0x35, 0x2, 0xee,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xe9, 0x0, 0xa,
    0xf6, 0xf6, 0x2, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x9f, 0x81, 0xf6, 0x7, 0xe2, 0x0, 0x0, 0xe9,
    0x3c, 0xf8, 0x1, 0xfb, 0x6e, 0xf1, 0x0, 0x0,
    0xfa, 0x6d, 0x40, 0x0, 0x9f, 0xfd, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x1,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x17, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x56,
    0x0, 0x5, 0x54, 0x44, 0x44, 0x44, 0x53, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x5, 0x54, 0x44, 0x44, 0x44, 0x53, 0x0,
    0x0, 0x4, 0x32, 0x22, 0x22, 0x22, 0x33, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0xc, 0xc1, 0x11, 0x11, 0x11, 0xf8, 0x0,
    0x0, 0xc, 0xb0, 0x0, 0x0, 0x0, 0xf8, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xe, 0xd3, 0x33, 0x33, 0x34, 0xfa, 0x0,

    /* U+8BA4 "认" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x80, 0x0, 0x0, 0xf, 0xb0, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0x0, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xb0, 0x0, 0xf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x50, 0x0, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x0, 0x1f,
    0xa0, 0x0, 0x0, 0x7e, 0xde, 0xa0, 0x0, 0x2,
    0xfe, 0x0, 0x0, 0x4, 0x76, 0xf9, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0xf, 0x90, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0xf8, 0x0,
    0x0, 0xde, 0xce, 0x0, 0x0, 0x0, 0x1f, 0x70,
    0x10, 0x3f, 0x96, 0xf6, 0x0, 0x0, 0x1, 0xf7,
    0x79, 0xb, 0xf3, 0xe, 0xe1, 0x0, 0x0, 0x2f,
    0xdf, 0xa5, 0xfb, 0x0, 0x6f, 0xa0, 0x0, 0x5,
    0xff, 0x92, 0xff, 0x10, 0x0, 0xcf, 0x80, 0x0,
    0x5f, 0x74, 0xef, 0x50, 0x0, 0x1, 0xef, 0x40,
    0x0, 0x20, 0x8f, 0x70, 0x0, 0x0, 0x3, 0x30,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8BBE "设" */
    0x0, 0x15, 0x0, 0x1, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x9, 0xf7, 0x0, 0xaf, 0xff, 0xff, 0x20,
    0x0, 0x0, 0xa, 0xf6, 0xa, 0xe4, 0x49, 0xf1,
    0x0, 0x0, 0x0, 0x7, 0x0, 0xcd, 0x0, 0x7f,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x80, 0x7,
    0xf2, 0x12, 0x2, 0xdc, 0xc8, 0x1d, 0xf1, 0x0,
    0x4f, 0xff, 0x50, 0x17, 0x6f, 0x82, 0xd5, 0x0,
    0x0, 0x14, 0x40, 0x0, 0x0, 0xf7, 0xf, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x1f, 0x70, 0x68,
    0xf8, 0x56, 0xf9, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0xd, 0xb0, 0x8f, 0x20, 0x0, 0x0, 0x1f, 0x67,
    0x0, 0x5f, 0x9f, 0x90, 0x0, 0x0, 0x1, 0xfd,
    0xf3, 0x0, 0x9f, 0xf1, 0x0, 0x0, 0x0, 0x4f,
    0xf8, 0x0, 0x8f, 0xff, 0xd6, 0x0, 0x0, 0x3,
    0xf8, 0x5a, 0xef, 0xb1, 0x4d, 0xff, 0xc3, 0x0,
    0x2, 0xa, 0xfb, 0x30, 0x0, 0x4, 0xbd, 0x0,
    0x0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BED "语" */
    0x0, 0x41, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x0, 0xe, 0xe4, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x1c, 0xf5, 0x44, 0x4d, 0xc4, 0x44,
    0x44, 0x0, 0x0, 0xa, 0x20, 0x76, 0xfc, 0x66,
    0x65, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xef, 0xed,
    0xdf, 0xb0, 0x4, 0xcc, 0xcb, 0x0, 0x2, 0xf5,
    0x0, 0xfa, 0x0, 0x38, 0x7d, 0xd0, 0x0, 0x4f,
    0x30, 0xf, 0x80, 0x0, 0x0, 0xbd, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0xb, 0xc2, 0x65,
    0x55, 0x55, 0x55, 0x55, 0x40, 0x0, 0xcc, 0x0,
    0x34, 0x44, 0x44, 0x45, 0x30, 0x0, 0xc, 0xc0,
    0x7, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xcb,
    0x76, 0x7f, 0x10, 0x0, 0x3f, 0x50, 0x0, 0xd,
    0xff, 0xa7, 0xf1, 0x0, 0x3, 0xf5, 0x0, 0x2,
    0xff, 0x80, 0x7f, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x6, 0x50, 0x7, 0xf6, 0x44, 0x47, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x13, 0x0, 0x0, 0x0, 0x0,

    /* U+8F91 "辑" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6c, 0x50, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x8, 0xf3, 0x0, 0x7f, 0xff, 0xff,
    0xc0, 0x1, 0x65, 0xcf, 0x55, 0x66, 0xf6, 0x44,
    0xdb, 0x0, 0x2c, 0xbf, 0xdb, 0xbb, 0x7f, 0xff,
    0xff, 0xc0, 0x0, 0x6, 0xf5, 0x11, 0x35, 0x65,
    0x55, 0x56, 0x31, 0x0, 0xbc, 0xdd, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0x70, 0x2f, 0x6d, 0xa0, 0x18,
    0xf0, 0x0, 0xf9, 0x10, 0xa, 0xfd, 0xff, 0xda,
    0x8f, 0xff, 0xff, 0x90, 0x0, 0x39, 0x6e, 0xc7,
    0x68, 0xf4, 0x44, 0xf9, 0x0, 0x0, 0x0, 0xda,
    0x1, 0x8f, 0x55, 0x5f, 0x90, 0x0, 0x0, 0xd,
    0xdc, 0xf8, 0xff, 0xff, 0xf9, 0x0, 0x1b, 0xdf,
    0xfe, 0x85, 0x8f, 0x12, 0x3f, 0xc7, 0x50, 0xb8,
    0x3d, 0xa4, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x0,
    0x0, 0xda, 0x18, 0x54, 0x31, 0xf, 0x90, 0x0,
    0x0, 0xe, 0xb0, 0x0, 0x0, 0x0, 0xfa, 0x0,
    0x0, 0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8F93 "输" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xcd, 0x10, 0x0, 0x6f, 0xb0, 0x0,
    0x0, 0x0, 0xf, 0x90, 0x0, 0x1e, 0xff, 0x20,
    0x0, 0x4, 0xaa, 0xfb, 0x9a, 0xb, 0xf3, 0xdd,
    0x20, 0x0, 0x4a, 0xde, 0xaa, 0xb9, 0xf5, 0x1,
    0xdf, 0x70, 0x0, 0xd, 0xb2, 0x1a, 0xff, 0xff,
    0xff, 0xef, 0xf7, 0x2, 0xfd, 0xf6, 0xfb, 0x35,
    0x44, 0x53, 0x5c, 0x0, 0x8e, 0x9e, 0x4, 0x75,
    0x57, 0x0, 0x7e, 0x50, 0x3f, 0xbb, 0xf5, 0x5f,
    0xee, 0xf6, 0x98, 0xf1, 0x3, 0xec, 0xef, 0xc8,
    0xf7, 0x9f, 0x7f, 0x6f, 0x10, 0x0, 0x9, 0xe0,
    0x2f, 0xff, 0xf7, 0xe6, 0xf1, 0x0, 0x0, 0x9f,
    0xaa, 0xf5, 0x7f, 0x7e, 0x6f, 0x10, 0x5c, 0xef,
    0xfc, 0x7f, 0xff, 0xf7, 0xf6, 0xf1, 0x2, 0xa6,
    0xbe, 0x2, 0xf7, 0x9f, 0x7d, 0x6f, 0x10, 0x0,
    0x9, 0xe0, 0x2f, 0xac, 0xf1, 0x9b, 0xf1, 0x0,
    0x0, 0xaf, 0x3, 0xf9, 0xfa, 0xe, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8FD4 "返" */
    0x0, 0xa4, 0x0, 0x0, 0x0, 0x1, 0x37, 0x50,
    0x0, 0x1d, 0xf4, 0x8, 0xed, 0xef, 0xff, 0xfe,
    0x0, 0x0, 0x1d, 0xf2, 0x7f, 0x76, 0x54, 0x31,
    0x20, 0x0, 0x0, 0x17, 0x7, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xaa, 0xaa,
    0xab, 0x40, 0x3, 0x76, 0x75, 0x7, 0xfa, 0xaa,
    0xad, 0xf1, 0x0, 0x6e, 0xdf, 0xa0, 0x7f, 0x34,
    0x0, 0xdb, 0x0, 0x0, 0x0, 0xe9, 0x8, 0xfa,
    0xf8, 0x4f, 0x50, 0x0, 0x0, 0xe, 0x90, 0xad,
    0x7, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xe9, 0xe,
    0x90, 0x9, 0xfe, 0x30, 0x0, 0x0, 0xe, 0x96,
    0xf3, 0x1a, 0xf8, 0xdf, 0x50, 0x0, 0x0, 0xea,
    0xdc, 0x9f, 0xf6, 0x0, 0xbf, 0x50, 0x0, 0x4f,
    0xc4, 0x19, 0xb2, 0x0, 0x0, 0x91, 0x0, 0x8f,
    0xcc, 0xfe, 0xa8, 0x77, 0x89, 0xac, 0xc0, 0x9,
    0xc0, 0x2, 0x8d, 0xff, 0xff, 0xee, 0xd7, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FDC "远" */
    0x3, 0x60, 0x0, 0x10, 0x0, 0x0, 0x1, 0x0,
    0x0, 0xbf, 0x90, 0xe, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x9f, 0xa0, 0x65, 0x55, 0x55, 0x56,
    0x10, 0x0, 0x0, 0x8c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x33, 0x33, 0x33,
    0x33, 0x41, 0x3, 0x66, 0x63, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x7f, 0xff, 0x82, 0x25, 0xf6,
    0x9f, 0x21, 0x21, 0x0, 0x0, 0xf8, 0x0, 0x5f,
    0x48, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x80, 0x7,
    0xf2, 0x8f, 0x0, 0x0, 0x0, 0x0, 0xf8, 0x0,
    0xde, 0x8, 0xf0, 0x7, 0x0, 0x0, 0xf, 0x80,
    0x9f, 0x60, 0x8f, 0x12, 0xfb, 0x0, 0x0, 0xf8,
    0xbf, 0x90, 0x6, 0xff, 0xff, 0x40, 0x0, 0x8f,
    0xd8, 0x70, 0x0, 0x5, 0x76, 0x20, 0x2, 0xef,
    0x9a, 0xff, 0xa7, 0x65, 0x67, 0x9b, 0xf2, 0xc,
    0x50, 0x1, 0x7c, 0xff, 0xff, 0xff, 0xeb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,

    /* U+8FDE "连" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x2, 0xf9, 0x0, 0x0,
    0x0, 0x1, 0xe9, 0x0, 0x0, 0x7f, 0x40, 0x0,
    0x0, 0x0, 0xa, 0xf5, 0x7e, 0xdf, 0xfd, 0xdd,
    0xef, 0x20, 0x0, 0xe, 0xb4, 0x7a, 0xf9, 0x77,
    0x77, 0x81, 0x0, 0x0, 0x10, 0x0, 0xbe, 0x4d,
    0x60, 0x0, 0x0, 0x1, 0x1, 0x10, 0x5f, 0x73,
    0xf6, 0x0, 0x0, 0x4, 0xff, 0xfa, 0xe, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x16, 0x4f, 0xa0, 0x67,
    0x57, 0xf9, 0x57, 0x50, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0x3f, 0x50, 0x0, 0x0, 0x0, 0xe, 0x94,
    0x76, 0x68, 0xf9, 0x66, 0x73, 0x0, 0x0, 0xe9,
    0x8f, 0xee, 0xef, 0xfe, 0xef, 0x50, 0x0, 0xe,
    0x90, 0x0, 0x3, 0xf5, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x40, 0x0, 0x3f, 0x60, 0x0, 0x0, 0x2d,
    0xf7, 0xaf, 0xd8, 0x68, 0xca, 0x89, 0xbb, 0x1,
    0xe5, 0x0, 0x39, 0xef, 0xff, 0xff, 0xff, 0x70,
    0x1, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0,

    /* U+9009 "选" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0xae, 0x30, 0x0,
    0x0, 0x3, 0xf7, 0x0, 0x4e, 0x68, 0xe0, 0x0,
    0x0, 0x0, 0xb, 0xf4, 0x8, 0xf6, 0xae, 0x55,
    0x65, 0x0, 0x0, 0x1e, 0xc0, 0xef, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x31, 0x8f, 0x30, 0x8e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xa6, 0x6b,
    0xf6, 0x66, 0x81, 0x7, 0xff, 0xfb, 0xfe, 0xef,
    0xfe, 0xfe, 0xef, 0x30, 0x35, 0x4f, 0x90, 0x0,
    0xf8, 0x7f, 0x10, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x3f, 0x67, 0xf1, 0x0, 0x0, 0x0, 0xe, 0x90,
    0x9, 0xf1, 0x7f, 0x14, 0x92, 0x0, 0x0, 0xe9,
    0x5, 0xfa, 0x7, 0xf1, 0x7f, 0x40, 0x0, 0xe,
    0x98, 0xfd, 0x0, 0x5f, 0xef, 0xd0, 0x0, 0x29,
    0xfe, 0x89, 0x0, 0x0, 0x68, 0x71, 0x0, 0x4f,
    0xe6, 0xaf, 0xea, 0x76, 0x77, 0x89, 0xaa, 0x0,
    0xb2, 0x0, 0x29, 0xdf, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x0, 0x0,
    0x0,

    /* U+95F4 "间" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0x70, 0x1, 0x10, 0x0, 0x1, 0x20, 0x0,
    0xbf, 0x90, 0xaf, 0xff, 0xff, 0xff, 0x20, 0x0,
    0xb8, 0x3, 0x43, 0x33, 0x39, 0xf1, 0x2f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x11, 0xf7, 0x4,
    0x54, 0x44, 0x54, 0x7, 0xf1, 0x1f, 0x70, 0xaf,
    0xff, 0xff, 0xa0, 0x7f, 0x11, 0xf7, 0x9, 0xe0,
    0x0, 0xe9, 0x7, 0xf1, 0x1f, 0x70, 0x9e, 0x11,
    0x1e, 0x90, 0x7f, 0x11, 0xf7, 0x9, 0xff, 0xff,
    0xf9, 0x7, 0xf1, 0x1f, 0x70, 0x9e, 0x22, 0x2e,
    0x90, 0x7f, 0x11, 0xf7, 0x9, 0xe0, 0x0, 0xe9,
    0x7, 0xf1, 0x1f, 0x70, 0xaf, 0xff, 0xff, 0xa0,
    0x7f, 0x11, 0xf7, 0x3, 0x43, 0x33, 0x43, 0x7,
    0xf1, 0x1f, 0x70, 0x0, 0x0, 0x0, 0x16, 0xbf,
    0x2, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xdb, 0x50,

    /* U+9645 "际" */
    0x10, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xfc, 0x9f, 0xff, 0xff, 0xff, 0x30,
    0x8f, 0x47, 0xf5, 0x45, 0x44, 0x44, 0x45, 0x10,
    0x8f, 0xa, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x2f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x9f, 0x18, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x8f, 0x2f, 0xa3, 0x55, 0x5c, 0xf5, 0x55, 0x61,
    0x8f, 0x8, 0xf2, 0x0, 0xa, 0xe0, 0x0, 0x0,
    0x8f, 0x2, 0xf6, 0x3c, 0x3a, 0xe2, 0xd2, 0x0,
    0x8f, 0x3, 0xf6, 0xaf, 0x3a, 0xe1, 0xfc, 0x0,
    0x8f, 0xdf, 0xf5, 0xf8, 0xa, 0xe0, 0x6f, 0x50,
    0x8f, 0x79, 0x3d, 0xe0, 0xa, 0xe0, 0xd, 0xd0,
    0x8f, 0x0, 0x5e, 0x40, 0xa, 0xe0, 0x5, 0xa0,
    0x8f, 0x0, 0x0, 0x9, 0x9e, 0xd0, 0x0, 0x0,
    0x9f, 0x10, 0x0, 0x8, 0xfd, 0x50, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9875 "页" */
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x45,
    0x44, 0x44, 0x9f, 0x74, 0x44, 0x44, 0x60, 0x0,
    0x32, 0x2b, 0xf2, 0x22, 0x23, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x1, 0xf8,
    0x22, 0x22, 0x22, 0xcd, 0x0, 0x0, 0x1f, 0x70,
    0x2c, 0xa0, 0xb, 0xd0, 0x0, 0x1, 0xf7, 0x1,
    0xf9, 0x0, 0xbd, 0x0, 0x0, 0x1f, 0x70, 0x1f,
    0x80, 0xb, 0xd0, 0x0, 0x1, 0xf7, 0x2, 0xf7,
    0x0, 0xbd, 0x0, 0x0, 0x1f, 0x70, 0x6f, 0x40,
    0xc, 0xd0, 0x0, 0x2, 0xf8, 0x1e, 0xd5, 0xd4,
    0xbd, 0x0, 0x0, 0x17, 0x6d, 0xf3, 0x5e, 0xfc,
    0x30, 0x0, 0x26, 0xbf, 0xe4, 0x0, 0x6, 0xef,
    0xa2, 0x3e, 0xfe, 0x80, 0x0, 0x0, 0x1, 0x9f,
    0xa0, 0x33, 0x0, 0x0, 0x0, 0x0, 0x0, 0x41,

    /* U+9879 "项" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x29, 0x99, 0x99, 0xb5, 0x44, 0xdf, 0x54,
    0x55, 0x1, 0x55, 0xf9, 0x53, 0x55, 0x5f, 0xc5,
    0x56, 0x0, 0x0, 0x1f, 0x70, 0xd, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x1, 0xf7, 0x0, 0xdb, 0x4,
    0x30, 0x9f, 0x0, 0x0, 0x1f, 0x70, 0xd, 0xb0,
    0xdf, 0x29, 0xf0, 0x0, 0x1, 0xf7, 0x0, 0xdb,
    0xc, 0xe0, 0x9f, 0x0, 0x0, 0x1f, 0x73, 0x5d,
    0xb0, 0xcd, 0x9, 0xf0, 0x0, 0x4, 0xff, 0xf9,
    0xdb, 0xd, 0xb0, 0x9f, 0x0, 0x4f, 0xff, 0x94,
    0xd, 0xb1, 0xf9, 0xa, 0xf0, 0x0, 0x96, 0x0,
    0x0, 0xdc, 0x9f, 0x56, 0x8d, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x8f, 0xb9, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xbf, 0xc0, 0x9, 0xfc, 0x10, 0x0,
    0x0, 0x2e, 0xfe, 0x60, 0x0, 0x7, 0xfd, 0x0,
    0x0, 0x0, 0x66, 0x0, 0x0, 0x0, 0x6, 0x20,

    /* U+9891 "频" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xe2, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x96, 0xae, 0x0, 0xff, 0xff, 0xff,
    0xff, 0x60, 0xf, 0x9a, 0xfd, 0xc3, 0x46, 0xfa,
    0x55, 0x41, 0x0, 0xf8, 0xaf, 0x68, 0x39, 0xbf,
    0xb9, 0x96, 0x0, 0xf, 0x8a, 0xe0, 0x4, 0xfe,
    0xee, 0xef, 0x70, 0x5f, 0xff, 0xff, 0xff, 0xbf,
    0x42, 0x21, 0xf7, 0x2, 0x55, 0x5c, 0xa5, 0x57,
    0xf4, 0xbf, 0x4f, 0x70, 0x0, 0x30, 0xec, 0x32,
    0x4f, 0x4b, 0xe1, 0xf7, 0x0, 0xf, 0xde, 0x9a,
    0xf9, 0xf4, 0xbd, 0x1f, 0x70, 0x5, 0xf4, 0xe9,
    0xeb, 0x4f, 0x4c, 0xc1, 0xf7, 0x0, 0xdc, 0xe,
    0xef, 0x44, 0xf5, 0xf9, 0x2f, 0x70, 0x3d, 0x40,
    0x9f, 0xb0, 0x39, 0xaf, 0x84, 0x52, 0x0, 0x0,
    0x2d, 0xe1, 0x0, 0x4f, 0xcc, 0xf6, 0x0, 0x2,
    0x9f, 0xd2, 0x3, 0xaf, 0xc1, 0xa, 0xf9, 0x3,
    0xff, 0x90, 0x4, 0xff, 0x80, 0x0, 0x9, 0xf4,
    0x6, 0x10, 0x0, 0x5, 0x10, 0x0, 0x0, 0x4,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 75, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 83, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 18, .adv_w = 119, .box_w = 6, .box_h = 4, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 30, .adv_w = 161, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 90, .adv_w = 151, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 153, .adv_w = 136, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 207, .adv_w = 224, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 285, .adv_w = 70, .box_w = 3, .box_h = 4, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 291, .adv_w = 90, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 336, .adv_w = 90, .box_w = 5, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 374, .adv_w = 118, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 402, .adv_w = 189, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 447, .adv_w = 66, .box_w = 3, .box_h = 6, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 456, .adv_w = 136, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 462, .adv_w = 66, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 467, .adv_w = 112, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 526, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 580, .adv_w = 151, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 628, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 682, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 736, .adv_w = 151, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 796, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 850, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 904, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 958, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1012, .adv_w = 151, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1066, .adv_w = 66, .box_w = 3, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1080, .adv_w = 66, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 1098, .adv_w = 189, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1148, .adv_w = 189, .box_w = 10, .box_h = 6, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1178, .adv_w = 189, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1228, .adv_w = 121, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1276, .adv_w = 260, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1396, .adv_w = 183, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1468, .adv_w = 164, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1522, .adv_w = 169, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1588, .adv_w = 195, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1654, .adv_w = 141, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1702, .adv_w = 137, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1744, .adv_w = 190, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1810, .adv_w = 200, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1876, .adv_w = 79, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1894, .adv_w = 108, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1930, .adv_w = 166, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1990, .adv_w = 133, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2032, .adv_w = 251, .box_w = 14, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2116, .adv_w = 209, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2182, .adv_w = 206, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2260, .adv_w = 159, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2314, .adv_w = 206, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2412, .adv_w = 169, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2472, .adv_w = 148, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2526, .adv_w = 150, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2586, .adv_w = 191, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2646, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2712, .adv_w = 263, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2814, .adv_w = 168, .box_w = 12, .box_h = 12, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2886, .adv_w = 157, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2946, .adv_w = 160, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3006, .adv_w = 90, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3044, .adv_w = 110, .box_w = 7, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3086, .adv_w = 90, .box_w = 5, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3124, .adv_w = 189, .box_w = 10, .box_h = 8, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 3164, .adv_w = 113, .box_w = 9, .box_h = 1, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 3169, .adv_w = 78, .box_w = 5, .box_h = 3, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 3177, .adv_w = 142, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3213, .adv_w = 164, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3267, .adv_w = 128, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3303, .adv_w = 164, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3363, .adv_w = 145, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3404, .adv_w = 94, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3440, .adv_w = 164, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3500, .adv_w = 158, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3548, .adv_w = 71, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3566, .adv_w = 71, .box_w = 5, .box_h = 15, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 3604, .adv_w = 143, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3652, .adv_w = 71, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3670, .adv_w = 241, .box_w = 13, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3729, .adv_w = 159, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3765, .adv_w = 162, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3810, .adv_w = 164, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3864, .adv_w = 164, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3924, .adv_w = 101, .box_w = 6, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3954, .adv_w = 117, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3986, .adv_w = 98, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4019, .adv_w = 159, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4055, .adv_w = 138, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4096, .adv_w = 206, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4155, .adv_w = 136, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4196, .adv_w = 138, .box_w = 10, .box_h = 12, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 4256, .adv_w = 126, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4292, .adv_w = 90, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4337, .adv_w = 76, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4360, .adv_w = 90, .box_w = 6, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4405, .adv_w = 189, .box_w = 10, .box_h = 4, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 4425, .adv_w = 117, .box_w = 6, .box_h = 4, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 4437, .adv_w = 117, .box_w = 6, .box_h = 5, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 4452, .adv_w = 235, .box_w = 13, .box_h = 6, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 4491, .adv_w = 272, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 4603, .adv_w = 272, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4723, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4859, .adv_w = 272, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4987, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5132, .adv_w = 272, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 5244, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5372, .adv_w = 272, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5508, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5644, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5789, .adv_w = 272, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5917, .adv_w = 272, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6022, .adv_w = 272, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 6135, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6280, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6425, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6570, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6715, .adv_w = 272, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 6843, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6979, .adv_w = 272, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7099, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7244, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 7372, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7508, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7653, .adv_w = 272, .box_w = 15, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7781, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7926, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8071, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8216, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8361, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8489, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8625, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8770, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8898, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9043, .adv_w = 272, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9187, .adv_w = 272, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9323, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9451, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9587, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9723, .adv_w = 272, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9843, .adv_w = 272, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 9971, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10107, .adv_w = 272, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10243, .adv_w = 272, .box_w = 18, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10387, .adv_w = 272, .box_w = 17, .box_h = 15, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10515, .adv_w = 272, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 10635, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10780, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10925, .adv_w = 272, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11045, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11190, .adv_w = 272, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 11294, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11430, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 11558, .adv_w = 272, .box_w = 18, .box_h = 16, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11702, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11830, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11975, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12111, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12247, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12392, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12537, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12673, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12809, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12954, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13099, .adv_w = 272, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 13219, .adv_w = 272, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13347, .adv_w = 272, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13467, .adv_w = 272, .box_w = 17, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13603, .adv_w = 272, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1, 0x176, 0x2e11, 0x2e1f, 0x2e2d, 0x2e92, 0x312d,
    0x31de, 0x31df, 0x3284, 0x3339, 0x33a6, 0x33f1, 0x36c2, 0x36e2,
    0x3714, 0x38eb, 0x3b70, 0x3b7e, 0x3b82, 0x3bdd, 0x3dd6, 0x3e8a,
    0x3f39, 0x3fe5, 0x41f4, 0x421b, 0x4389, 0x4554, 0x456b, 0x4594,
    0x45da, 0x4710, 0x471e, 0x4bb8, 0x4d25, 0x4ddf, 0x509d, 0x523d,
    0x529a, 0x536b, 0x550c, 0x5519, 0x565f, 0x5852, 0x59d4, 0x59ef,
    0x5cdf, 0x5ec3, 0x5efa, 0x5f52, 0x60b0, 0x61ce, 0x63c0, 0x64c1,
    0x69aa, 0x69e4, 0x6b88, 0x6ba2, 0x6bd1, 0x6f75, 0x6f77, 0x6fb8,
    0x6fc0, 0x6fc2, 0x6fed, 0x75d8, 0x7629, 0x7859, 0x785d, 0x7875
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 8220, .range_length = 30838, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 72, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_FontDengXianBold = {
#else
lv_font_t ui_font_FontDengXianBold = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 18,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_FONTDENGXIANBOLD*/

