/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "usart.h"
#include "GlobalTypes.h"
#include "HalWireComm.h"
#include "HalWirelessComm.h"
#include "LedHandle.h"
#include "KeyHandle.h"
#include "Event.h"

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
static HALWIRECOMM_HANDLE_T sHalWireCommHandle;  /* HalWireComm模块句柄 */
static HALWIRELESSCOMM_HANDLE_T sHalWirelessCommHandle;  /* HalWirelessComm模块句柄 */

/* USER CODE END Variables */
osThreadId defaultTaskHandle;
osThreadId wireCommTaskHandle;
osThreadId wirelessCommHandle;
osThreadId KeyHandleTaskHandle;
osThreadId LedHandleTaskHandle;

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */

/* USER CODE END FunctionPrototypes */

void StartDefaultTask(void const * argument);
void StartWireCommTask(void const * argument);
void StartWirelessCommTask(void const * argument);
void StartKeyHandleTask(void const * argument);
void StartLedHandleTask(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */
  /* 初始化Event模块 */
  EVENT_STATUS_E eEventStatus = Event_eInit();

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of defaultTask */
  osThreadDef(defaultTask, StartDefaultTask, osPriorityNormal, 0, 128);
  defaultTaskHandle = osThreadCreate(osThread(defaultTask), NULL);

  /* definition and creation of wireCommTask */
  osThreadDef(wireCommTask, StartWireCommTask, osPriorityNormal, 0, 512);
  wireCommTaskHandle = osThreadCreate(osThread(wireCommTask), NULL);

  /* definition and creation of wirelessComm */
  osThreadDef(wirelessComm, StartWirelessCommTask, osPriorityNormal, 0, 512);
  wirelessCommHandle = osThreadCreate(osThread(wirelessComm), NULL);

  /* definition and creation of KeyHandleTask */
  osThreadDef(KeyHandleTask, StartKeyHandleTask, osPriorityNormal, 0, 512);
  KeyHandleTaskHandle = osThreadCreate(osThread(KeyHandleTask), NULL);

  /* definition and creation of LedHandleTask */
  osThreadDef(LedHandleTask, StartLedHandleTask, osPriorityNormal, 0, 512);
  LedHandleTaskHandle = osThreadCreate(osThread(LedHandleTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartDefaultTask */
/**
  * @brief  Function implementing the defaultTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartDefaultTask */
void StartDefaultTask(void const * argument)
{
  /* USER CODE BEGIN StartDefaultTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartDefaultTask */
}

/* USER CODE BEGIN Header_StartWireCommTask */
/**
* @brief Function implementing the wireCommTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartWireCommTask */
void StartWireCommTask(void const * argument)
{
  /* USER CODE BEGIN StartWireCommTask */
  /* 注册回调函数到UartCallbackManager */
  UartCallbackManager_vAddIdleCallback(&huart3, HalWireComm_vRxCompleteCallback, &sHalWireCommHandle);
  UartCallbackManager_vAddTxCompleteCallback(&huart3, HalWireComm_vTxCompleteCallback, &sHalWireCommHandle);
  UartCallbackManager_vAddErrorCallback(&huart3, HalWireComm_vErrorOccurCallback, &sHalWireCommHandle);

  HALWIRECOMM_CONFIG_T sConfig;
  HALWIRECOMM_STATUS_E eStatus;
  BOOL bRxComplete = FALSE_D;
  BOOL bTxComplete = FALSE_D;
  U16 u16RxLength = 0U;
  U8* pu8RxBuffer = NULL_D;
  U8* pu8TxBuffer = NULL_D;

  /* 获取默认配置 */
  eStatus = HalWireComm_eGetDefaultConfig(&sConfig);

  /* 初始化HalWireComm模块 */
  eStatus = HalWireComm_eInit(&sHalWireCommHandle, &sConfig);

  /* 获取缓冲区指�? */
  HalWireComm_eGetRxBuffer(&sHalWireCommHandle, &pu8RxBuffer);
  HalWireComm_eGetTxBuffer(&sHalWireCommHandle, &pu8TxBuffer);

  /* 启动接收模式 */
  eStatus = HalWireComm_eReceive(&sHalWireCommHandle);

  /* Infinite loop */
  for(;;)
  {
	/* �?查是否有数据接收完成 */
	HalWireComm_eIsRxComplete(&sHalWireCommHandle, &bRxComplete);

	if (bRxComplete == TRUE_D)
	{
	  /* 获取接收到的数据长度 */
	  HalWireComm_eGetRxLength(&sHalWireCommHandle, &u16RxLength);

	  if (u16RxLength > 0U)
	  {
		/* 将接收到的数据复制到发�?�缓冲区 */
		memcpy(pu8TxBuffer, pu8RxBuffer, u16RxLength);

		/* 设置发�?�长�? */
		HalWireComm_eSetTxLength(&sHalWireCommHandle, u16RxLength);

		/* 清除接收完成标志 */
		HalWireComm_eClearRxComplete(&sHalWireCommHandle);

		/* 发�?�数据（回显�? */
		eStatus = HalWireComm_eTransmit(&sHalWireCommHandle);

		/* 等待发�?�完�? */
		do
		{
		  HalWireComm_eIsTxComplete(&sHalWireCommHandle, &bTxComplete);
		  osDelay(1);
		} while (bTxComplete == FALSE_D);

		/* 清除发�?�完成标�? */
		HalWireComm_eClearTxComplete(&sHalWireCommHandle);

		/* 重新启动接收模式 */
		HalWireComm_eReceive(&sHalWireCommHandle);
	  }
	  else
	  {
		/* 接收长度�?0，清除标志并重新启动接收 */
		HalWireComm_eClearRxComplete(&sHalWireCommHandle);
		HalWireComm_eReceive(&sHalWireCommHandle);
	  }
	}

	osDelay(10);
  }
  /* USER CODE END StartWireCommTask */
}

/* USER CODE BEGIN Header_StartWirelessCommTask */
/**
* @brief Function implementing the wirelessComm thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartWirelessCommTask */
void StartWirelessCommTask(void const * argument)
{
  /* USER CODE BEGIN StartWirelessCommTask */
  /* 注册回调函数到UartCallbackManager */
  UartCallbackManager_vAddIdleCallback(&huart1, HalWirelessComm_vRxCompleteCallback, &sHalWirelessCommHandle);
  UartCallbackManager_vAddTxCompleteCallback(&huart1, HalWirelessComm_vTxCompleteCallback, &sHalWirelessCommHandle);
  UartCallbackManager_vAddErrorCallback(&huart1, HalWirelessComm_vErrorOccurCallback, &sHalWirelessCommHandle);

  HALWIRELESSCOMM_CONFIG_T sConfig;
  HALWIRELESSCOMM_STATUS_E eStatus;
  HALWIRELESSCOMM_COMM_STATE_E eRxState = HALWIRELESSCOMM_COMM_IDLE_E;
  HALWIRELESSCOMM_COMM_STATE_E eTxState = HALWIRELESSCOMM_COMM_IDLE_E;
  U16 u16RxLength = 0U;
  U8* pu8RxBuffer = NULL_D;
  U8* pu8TxBuffer = NULL_D;

  /* 获取默认配置 */
  eStatus = HalWirelessComm_eGetDefaultConfig(&sConfig);

  /* 初始化HalWirelessComm模块 */
  eStatus = HalWirelessComm_eInit(&sHalWirelessCommHandle, &sConfig);

  /* 获取缓冲区指�? */
  HalWirelessComm_eGetRxBuffer(&sHalWirelessCommHandle, &pu8RxBuffer);
  HalWirelessComm_eGetTxBuffer(&sHalWirelessCommHandle, &pu8TxBuffer);

  /* 启动接收模式 */
  eStatus = HalWirelessComm_eReceive(&sHalWirelessCommHandle);

  /* Infinite loop */
  for(;;)
  {
	/* �?查是否有数据接收完成 */
	HalWirelessComm_eGetRxState(&sHalWirelessCommHandle, &eRxState);

	if (eRxState == HALWIRELESSCOMM_COMM_COMPLETE_E)
	{
	  /* 获取接收到的数据长度 */
	  HalWirelessComm_eGetRxLength(&sHalWirelessCommHandle, &u16RxLength);

	  if (u16RxLength > 0U)
	  {
		/* 将接收到的数据复制到发�?�缓冲区 */
		memcpy(pu8TxBuffer, pu8RxBuffer, u16RxLength);

		/* 设置发�?�长�? */
		HalWirelessComm_eSetTxLength(&sHalWirelessCommHandle, u16RxLength);

		/* 清除接收完成标志 */
		HalWirelessComm_eClearRxComplete(&sHalWirelessCommHandle);

		/* 发�?�数�? */
		eStatus = HalWirelessComm_eTransmit(&sHalWirelessCommHandle);

		/* 等待发�?�完�? */
		do
		{
		  HalWirelessComm_eGetTxState(&sHalWirelessCommHandle, &eTxState);
		  osDelay(1);
		} while (eTxState != HALWIRELESSCOMM_COMM_COMPLETE_E);

		/* 清除发�?�完成标�? */
		HalWirelessComm_eClearTxComplete(&sHalWirelessCommHandle);

		/* 重新启动接收模式 */
		HalWirelessComm_eReceive(&sHalWirelessCommHandle);
	  }
	  else
	  {
		/* 接收长度清除，清除标志并重新启动接收 */
		HalWirelessComm_eClearRxComplete(&sHalWirelessCommHandle);
		HalWirelessComm_eReceive(&sHalWirelessCommHandle);
	  }
	}

	osDelay(10);
  }
  /* USER CODE END StartWirelessCommTask */
}

/* USER CODE BEGIN Header_StartKeyHandleTask */
/**
* @brief Function implementing the KeyHandleTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartKeyHandleTask */
void StartKeyHandleTask(void const * argument)
{
  /* USER CODE BEGIN StartKeyHandleTask */
  KEYHANDLE_STATUS_E eStatus;
  
  /* 初始化KeyHandle模块 */
  eStatus = KeyHandle_eInit();
  
  if (eStatus != KEYHANDLE_OK_E)
  {
    /* 初始化失败，进入错误处理循环 */
    for(;;)
    {
      osDelay(1000);  /* 错误状态下延时1秒 */
    }
  }
  
  /* Infinite loop */
  for(;;)
  {
    /* 运行KeyHandle模块处理 */
    KeyHandle_vRun();
    
    /* 延时10ms，控制任务执行频率 */
    osDelay(10);
  }
  /* USER CODE END StartKeyHandleTask */
}

/* USER CODE BEGIN Header_StartLedHandleTask */
/**
* @brief Function implementing the LedHandleTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartLedHandleTask */
void StartLedHandleTask(void const * argument)
{
  /* USER CODE BEGIN StartLedHandleTask */
  LedHandle_vInit();
  /* Infinite loop */
  for(;;)
  {
	  LedHandle_vRun();
    osDelay(1);
  }
  /* USER CODE END StartLedHandleTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/* USER CODE END Application */
