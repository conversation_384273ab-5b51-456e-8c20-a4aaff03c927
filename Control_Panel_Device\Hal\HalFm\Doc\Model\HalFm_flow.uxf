<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>9</zoom_level>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>396</x>
      <y>1143</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>342</x>
      <y>1188</y>
      <w>126</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Configure SPI handle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>396</x>
      <y>1152</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>342</x>
      <y>1260</y>
      <w>126</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Configure CS GPIO</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>396</x>
      <y>1224</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>342</x>
      <y>1332</y>
      <w>126</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Set CS active level</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>396</x>
      <y>1296</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>396</x>
      <y>1413</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>396</x>
      <y>1368</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>279</x>
      <y>1089</y>
      <w>243</w>
      <h>378</h>
    </coordinates>
    <panel_attributes>HalFm eInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>315</x>
      <y>54</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>261</x>
      <y>99</y>
      <w>126</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Check entry point
Validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>63</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>306</x>
      <y>171</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>135</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>873</y>
      <w>144</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>513</x>
      <y>180</y>
      <w>27</w>
      <h>711</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;770.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>333</x>
      <y>171</y>
      <w>207</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>234</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Check address validity</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>198</y>
      <w>108</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>306</x>
      <y>306</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>270</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>333</x>
      <y>306</y>
      <w>207</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Invalid address]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>369</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Prepare read command
Set cmd and address</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>333</y>
      <w>108</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid address]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>441</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Select FM device
CS signal active</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>405</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>513</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Send read command
Transmit via SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>477</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>306</x>
      <y>585</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>549</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>333</x>
      <y>585</y>
      <w>207</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Transmit failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>657</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Receive data
Read via SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>612</y>
      <w>135</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Transmit success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>306</x>
      <y>729</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>693</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>333</x>
      <y>729</y>
      <w>207</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Receive failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>801</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Deselect FM device
CS signal inactive</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>756</y>
      <w>126</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Receive success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>873</y>
      <w>144</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>837</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>315</x>
      <y>972</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>900</y>
      <w>27</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>900</y>
      <w>225</w>
      <h>63</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;230.0;50.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>162</x>
      <y>0</y>
      <w>486</w>
      <h>1035</h>
    </coordinates>
    <panel_attributes>HalFm ReadData
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>900</x>
      <y>54</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>846</x>
      <y>99</y>
      <w>126</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Check entry point
Validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>63</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>891</x>
      <y>171</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>135</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1044</x>
      <y>1350</y>
      <w>144</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1107</x>
      <y>180</y>
      <w>27</w>
      <h>1188</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;1300.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>918</x>
      <y>171</y>
      <w>216</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>234</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Check address validity</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>198</y>
      <w>108</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>891</x>
      <y>306</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>270</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>918</x>
      <y>306</y>
      <w>216</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Invalid address]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>369</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Initialize counters
Set write parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>333</y>
      <w>108</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid address]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>819</x>
      <y>441</y>
      <w>180</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Write page loop
While remaining &gt; 0</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>405</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>513</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Calculate bytes to write
Page boundary check</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>477</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>828</x>
      <y>585</y>
      <w>162</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Enable write operation
Call HalFm_eWriteEnable</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>549</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>891</x>
      <y>657</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>621</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>918</x>
      <y>657</y>
      <w>216</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Enable failed]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>729</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Prepare write command
Set cmd and address</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>684</y>
      <w>117</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Enable success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>801</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Select FM device
CS signal active</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>765</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>873</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Send write command
Transmit via SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>837</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>891</x>
      <y>945</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>909</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>918</x>
      <y>945</y>
      <w>216</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Transmit failed]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>1017</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Send write data
Transmit page data</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>972</y>
      <w>135</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Transmit success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>891</x>
      <y>1089</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>1053</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>918</x>
      <y>1089</y>
      <w>216</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Data send failed]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>1161</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Deselect FM device
CS signal inactive</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>1116</y>
      <w>135</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Data send success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>1233</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Update counters
Next page preparation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>1197</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>765</x>
      <y>450</y>
      <w>162</w>
      <h>882</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[More pages to write]</panel_attributes>
    <additional_attributes>60.0;10.0;10.0;10.0;10.0;960.0;160.0;960.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>1350</y>
      <w>144</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>1269</y>
      <w>126</w>
      <h>99</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[All pages written]</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>900</x>
      <y>1449</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>1377</y>
      <w>27</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>900</x>
      <y>1377</y>
      <w>234</w>
      <h>54</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;40.0;240.0;40.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>711</x>
      <y>0</y>
      <w>522</w>
      <h>1503</h>
    </coordinates>
    <panel_attributes>HalFm WriteData
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1485</x>
      <y>54</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1413</x>
      <y>99</y>
      <w>162</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Prepare write enable cmd
Set command byte</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>63</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1431</x>
      <y>171</y>
      <w>126</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Select FM device
CS signal active</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>135</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1422</x>
      <y>243</y>
      <w>144</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Send write enable cmd
Transmit via SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>207</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1476</x>
      <y>315</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>279</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1593</x>
      <y>468</y>
      <w>144</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1656</x>
      <y>324</y>
      <w>27</w>
      <h>162</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;160.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1503</x>
      <y>315</y>
      <w>180</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Transmit failed]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1431</x>
      <y>387</y>
      <w>126</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Deselect FM device
CS signal inactive</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>342</y>
      <w>135</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Transmit success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1431</x>
      <y>468</y>
      <w>126</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>423</y>
      <w>27</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1485</x>
      <y>576</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>495</y>
      <w>27</w>
      <h>99</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1485</x>
      <y>495</y>
      <w>198</w>
      <h>63</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;200.0;50.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1332</x>
      <y>0</y>
      <w>459</w>
      <h>639</h>
    </coordinates>
    <panel_attributes>HalFm WriteEnable
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1458</x>
      <y>747</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1386</x>
      <y>792</y>
      <w>162</w>
      <h>45</h>
    </coordinates>
    <panel_attributes>Check address range
Calculate end address</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1458</x>
      <y>756</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1449</x>
      <y>864</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1458</x>
      <y>828</y>
      <w>27</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1575</x>
      <y>972</y>
      <w>144</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1638</x>
      <y>873</y>
      <w>27</w>
      <h>117</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;110.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1476</x>
      <y>864</y>
      <w>189</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>[Address overflow]</panel_attributes>
    <additional_attributes>190.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1404</x>
      <y>972</y>
      <w>126</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Return 
HALFM_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1458</x>
      <y>891</y>
      <w>108</w>
      <h>99</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid address]</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1458</x>
      <y>1080</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1458</x>
      <y>999</y>
      <w>27</w>
      <h>99</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1458</x>
      <y>999</y>
      <w>207</w>
      <h>63</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;210.0;50.0;210.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1341</x>
      <y>693</y>
      <w>414</w>
      <h>450</h>
    </coordinates>
    <panel_attributes>HalFm IsAddressValid
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
</diagram>
