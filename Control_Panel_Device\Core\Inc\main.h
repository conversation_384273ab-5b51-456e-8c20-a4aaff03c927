/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define RS485_DIR_Pin GPIO_PIN_2
#define RS485_DIR_GPIO_Port GPIOB
#define KEY_OUTPUT_LINE_4_Pin GPIO_PIN_9
#define KEY_OUTPUT_LINE_4_GPIO_Port GPIOH
#define KEY_OUTPUT_LINE_3_Pin GPIO_PIN_10
#define KEY_OUTPUT_LINE_3_GPIO_Port GPIOH
#define KEY_OUTPUT_LINE_2_Pin GPIO_PIN_11
#define KEY_OUTPUT_LINE_2_GPIO_Port GPIOH
#define KEY_OUTPUT_LINE_1_Pin GPIO_PIN_12
#define KEY_OUTPUT_LINE_1_GPIO_Port GPIOH
#define LED_4G_WHITE_Pin GPIO_PIN_2
#define LED_4G_WHITE_GPIO_Port GPIOG
#define LED_BLUETOOTH_WHITE_Pin GPIO_PIN_3
#define LED_BLUETOOTH_WHITE_GPIO_Port GPIOG
#define LED_WIFI_WHITE_Pin GPIO_PIN_4
#define LED_WIFI_WHITE_GPIO_Port GPIOG
#define LED_STATUS_GREEN_Pin GPIO_PIN_5
#define LED_STATUS_GREEN_GPIO_Port GPIOG
#define LED_WARNING_RED_Pin GPIO_PIN_6
#define LED_WARNING_RED_GPIO_Port GPIOG
#define LED_WARNING_YELLOW_Pin GPIO_PIN_7
#define LED_WARNING_YELLOW_GPIO_Port GPIOG
#define KEY_INPUT_LINE_1_Pin GPIO_PIN_14
#define KEY_INPUT_LINE_1_GPIO_Port GPIOH
#define KEY_INPUT_LINE_2_Pin GPIO_PIN_4
#define KEY_INPUT_LINE_2_GPIO_Port GPIOI
#define KEY_INPUT_LINE_3_Pin GPIO_PIN_6
#define KEY_INPUT_LINE_3_GPIO_Port GPIOI
#define KEY_INPUT_LINE_4_Pin GPIO_PIN_7
#define KEY_INPUT_LINE_4_GPIO_Port GPIOI

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
