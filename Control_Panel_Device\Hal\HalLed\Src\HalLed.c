//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file HalLed.c
*
* @brief LED hardware abstraction layer implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "HalLed.h"
#include "main.h"
#include "GlobalTypes.h"
#include "FreeRTOS.h"
#include "task.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define HALLED_BLINK_PERIOD_MS_D            1000U                               /* LED blink period in milliseconds */
#define HALLED_ACTIVE_LEVEL_HIGH_D          1U                                  /* LED active when GPIO is high */
#define HALLED_ACTIVE_LEVEL_LOW_D           0U                                  /* LED active when GPIO is low */
#define HALLED_4G_LED_PORT_D                (LED_4G_WHITE_GPIO_Port)            /* 4G LED GPIO port */
#define HALLED_4G_LED_PIN_D                 (LED_4G_WHITE_Pin)                  /* 4G LED GPIO pin */
#define HALLED_BLUETOOTH_LED_PORT_D         (LED_BLUETOOTH_WHITE_GPIO_Port)     /* Bluetooth LED GPIO port */
#define HALLED_BLUETOOTH_LED_PIN_D          (LED_BLUETOOTH_WHITE_Pin)           /* Bluetooth LED GPIO pin */
#define HALLED_WIFI_LED_PORT_D              (LED_WIFI_WHITE_GPIO_Port)          /* WiFi LED GPIO port */
#define HALLED_WIFI_LED_PIN_D               (LED_WIFI_WHITE_Pin)                /* WiFi LED GPIO pin */
#define HALLED_STATUS_LED_PORT_D            (LED_STATUS_GREEN_GPIO_Port)        /* Status LED GPIO port */
#define HALLED_STATUS_LED_PIN_D             (LED_STATUS_GREEN_Pin)              /* Status LED GPIO pin */
#define HALLED_WARNING_RED_LED_PORT_D       (LED_WARNING_RED_GPIO_Port)         /* Warning Red LED GPIO port */
#define HALLED_WARNING_RED_LED_PIN_D        (LED_WARNING_RED_Pin)               /* Warning Red LED GPIO pin */
#define HALLED_WARNING_YELLOW_LED_PORT_D    (LED_WARNING_YELLOW_GPIO_Port)      /* Warning Yellow LED GPIO port */
#define HALLED_WARNING_YELLOW_LED_PIN_D     (LED_WARNING_YELLOW_Pin)            /* Warning Yellow LED GPIO pin */

/**
* @brief HAL layer LED configuration structure
*/
typedef struct
{
    GPIO_TypeDef* apsGpioPorts[HALLED_LED_MAX_E];       /* GPIO port pointers array (such as GPIOA, GPIOB, etc.) */
    U16 au16GpioPins[HALLED_LED_MAX_E];                 /* GPIO pin numbers array (such as GPIO_PIN_0, GPIO_PIN_1, etc.) */
    BOOL abActiveLevels[HALLED_LED_MAX_E];              /* LED active levels array (TRUE_D for active high, FALSE_D for active low) */
} HALLED_CONFIG_T;

/**
* @brief HAL layer LED runtime state structure
*/
typedef struct
{
    HALLED_LED_STATE_E aeStates[HALLED_LED_MAX_E];      /* Current LED states array */
    BOOL abCurrentLevels[HALLED_LED_MAX_E];             /* Current GPIO levels array */
    U32 au32LastToggleTimes[HALLED_LED_MAX_E];          /* Last toggle timestamps array in milliseconds */
} HALLED_STATE_T;

/**
* @brief HAL layer LED handle structure
*/
typedef struct
{
    HALLED_CONFIG_T sConfig;                            /* Configuration parameter structure */
    HALLED_STATE_T sState;                              /* Runtime state structure */
} HALLED_HANDLE_T;

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static void HalLed_eSetLedLevel(const HALLED_LED_INDEX_E eLedIndex, const BOOL bLevel);
static U32 HalLed_u32GetTickMs(void);
static U32 HalLed_u32GetElapsedTime(const U32 u32CurrentTime, const U32 u32StartTime);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------
static HALLED_HANDLE_T sHalLed;                         /* Static LED handle */

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize LED hardware abstraction layer.
* @remark None.
*
* @return None.
*/
void HalLed_eInit(void)
{
    /* Configure LED pins */
    sHalLed.sConfig.apsGpioPorts[HALLED_LED_4G_WHITE_E] = HALLED_4G_LED_PORT_D;
    sHalLed.sConfig.au16GpioPins[HALLED_LED_4G_WHITE_E] = HALLED_4G_LED_PIN_D;
    sHalLed.sConfig.abActiveLevels[HALLED_LED_4G_WHITE_E] = HALLED_ACTIVE_LEVEL_LOW_D;
    
    sHalLed.sConfig.apsGpioPorts[HALLED_LED_BLUETOOTH_WHITE_E] = HALLED_BLUETOOTH_LED_PORT_D;
    sHalLed.sConfig.au16GpioPins[HALLED_LED_BLUETOOTH_WHITE_E] = HALLED_BLUETOOTH_LED_PIN_D;
    sHalLed.sConfig.abActiveLevels[HALLED_LED_BLUETOOTH_WHITE_E] = HALLED_ACTIVE_LEVEL_LOW_D;
    
    sHalLed.sConfig.apsGpioPorts[HALLED_LED_WIFI_WHITE_E] = HALLED_WIFI_LED_PORT_D;
    sHalLed.sConfig.au16GpioPins[HALLED_LED_WIFI_WHITE_E] = HALLED_WIFI_LED_PIN_D;
    sHalLed.sConfig.abActiveLevels[HALLED_LED_WIFI_WHITE_E] = HALLED_ACTIVE_LEVEL_LOW_D;
    
    sHalLed.sConfig.apsGpioPorts[HALLED_LED_STATUS_GREEN_E] = HALLED_STATUS_LED_PORT_D;
    sHalLed.sConfig.au16GpioPins[HALLED_LED_STATUS_GREEN_E] = HALLED_STATUS_LED_PIN_D;
    sHalLed.sConfig.abActiveLevels[HALLED_LED_STATUS_GREEN_E] = HALLED_ACTIVE_LEVEL_LOW_D;
    
    sHalLed.sConfig.apsGpioPorts[HALLED_LED_WARNING_RED_E] = HALLED_WARNING_RED_LED_PORT_D;
    sHalLed.sConfig.au16GpioPins[HALLED_LED_WARNING_RED_E] = HALLED_WARNING_RED_LED_PIN_D;
    sHalLed.sConfig.abActiveLevels[HALLED_LED_WARNING_RED_E] = HALLED_ACTIVE_LEVEL_LOW_D;
    
    sHalLed.sConfig.apsGpioPorts[HALLED_LED_WARNING_YELLOW_E] = HALLED_WARNING_YELLOW_LED_PORT_D;
    sHalLed.sConfig.au16GpioPins[HALLED_LED_WARNING_YELLOW_E] = HALLED_WARNING_YELLOW_LED_PIN_D;
    sHalLed.sConfig.abActiveLevels[HALLED_LED_WARNING_YELLOW_E] = HALLED_ACTIVE_LEVEL_LOW_D;
    
    /* Initialize LED control data */
    for (U8 u8LedIndex = 0U; u8LedIndex < HALLED_LED_MAX_E; u8LedIndex++)
    {
        sHalLed.sState.aeStates[u8LedIndex] = HALLED_STATE_OFF_E;
        sHalLed.sState.abCurrentLevels[u8LedIndex] = FALSE_D;
        sHalLed.sState.au32LastToggleTimes[u8LedIndex] = 0U;
        
        /* Set initial LED state to OFF */
        HalLed_eSetLedLevel((HALLED_LED_INDEX_E)u8LedIndex, FALSE_D);
    }
}

/**
* @brief Set LED state (ON, OFF, or BLINK).
* @remark None.
*
* @param eLedIndex [in]: LED index.
* @param eState [in]: LED state to set.
* @return None.
*/
void HalLed_eSetLedState(const HALLED_LED_INDEX_E eLedIndex, const HALLED_LED_STATE_E eState)
{   
    /* Update LED state */
    sHalLed.sState.aeStates[eLedIndex] = eState;
    
    switch (eState)
    {
        case HALLED_STATE_OFF_E: HalLed_eSetLedLevel(eLedIndex, FALSE_D); break;
        case HALLED_STATE_ON_E: HalLed_eSetLedLevel(eLedIndex, TRUE_D); break;
        case HALLED_STATE_BLINK_E:
        {
            sHalLed.sState.au32LastToggleTimes[eLedIndex] = HalLed_u32GetTickMs();
            sHalLed.sState.abCurrentLevels[eLedIndex] = TRUE_D;
            HalLed_eSetLedLevel(eLedIndex, TRUE_D);
        } break;
        default: break;
    }
}

/**
* @brief Update blinking LEDs status.
* @remark This function should be called periodically to update blinking LEDs.
*
* @return None.
*/
void HalLed_eUpdateBlinkLeds(void)
{
    U32 u32CurrentTime = HalLed_u32GetTickMs();

    /* Half period for 50% duty cycle */
    U32 u32HalfPeriod = HALLED_BLINK_PERIOD_MS_D / 2U;
    
    /* Update all blinking LEDs */
    for (U8 u8LedIndex = 0U; u8LedIndex < HALLED_LED_MAX_E; u8LedIndex++)
    {
        if (sHalLed.sState.aeStates[u8LedIndex] == HALLED_STATE_BLINK_E)
        {
            U32 u32ElapsedTime = HalLed_u32GetElapsedTime(u32CurrentTime, sHalLed.sState.au32LastToggleTimes[u8LedIndex]);
            
            /* Check if it's time to toggle LED state */
            if (u32ElapsedTime >= u32HalfPeriod)
            {
                /* Toggle LED level */
                sHalLed.sState.abCurrentLevels[u8LedIndex] = !sHalLed.sState.abCurrentLevels[u8LedIndex];
                sHalLed.sState.au32LastToggleTimes[u8LedIndex] = u32CurrentTime;

                HalLed_eSetLedLevel((HALLED_LED_INDEX_E)u8LedIndex, sHalLed.sState.abCurrentLevels[u8LedIndex]);
            }
        }
    }
}

/**
* @brief Set LED GPIO level.
* @remark None.
*
* @param eLedIndex [in]: LED index.
* @param bLevel [in]: LED level (TRUE_D for active, FALSE_D for inactive).
* @return None.
*/
void HalLed_eSetLedLevel(const HALLED_LED_INDEX_E eLedIndex, const BOOL bLevel)
{    
    /* Determine actual GPIO level based on active level configuration */
    BOOL bGpioLevel = (sHalLed.sConfig.abActiveLevels[eLedIndex]) ? bLevel : (!bLevel);
    
    /* Convert BOOL value to GPIO status */
    GPIO_PinState pinState = bGpioLevel ? GPIO_PIN_SET : GPIO_PIN_RESET;
    
    /* Set GPIO output status */
    HAL_GPIO_WritePin(sHalLed.sConfig.apsGpioPorts[eLedIndex], sHalLed.sConfig.au16GpioPins[eLedIndex], pinState);
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Get current system time in milliseconds.
* @remark None.
*
* @return Current time in milliseconds.
*/
U32 HalLed_u32GetTickMs(void)
{
    return (U32)(xTaskGetTickCount() * portTICK_PERIOD_MS);
}

/**
* @brief Calculate elapsed time handling potential overflow.
* @remark None.
*
* @param u32CurrentTime [in]: Current time in milliseconds.
* @param u32StartTime [in]: Start time in milliseconds.
* @return Elapsed time in milliseconds.
*/
U32 HalLed_u32GetElapsedTime(const U32 u32CurrentTime, const U32 u32StartTime)
{
    U32 u32ElapsedTime = 0U;
    
    if (u32CurrentTime >= u32StartTime)
    {
        /* Normal case: no overflow occurred */
        u32ElapsedTime = u32CurrentTime - u32StartTime;
    }
    else
    {
        /* Calculate elapsed time accounting for 32-bit overflow */
        u32ElapsedTime = (0xFFFFFFFFU * portTICK_PERIOD_MS - u32StartTime) + u32CurrentTime + 1U;
    }
    
    return u32ElapsedTime;
}

//===========================================================================
// End of file.
//===========================================================================








