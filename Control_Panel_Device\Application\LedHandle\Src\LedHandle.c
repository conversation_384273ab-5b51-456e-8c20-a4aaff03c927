//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file LedHandle.c
*
* @brief LED Handle module implementation file - LED indicator control logic
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "LedHandle.h"
#include "Event.h"
#include "HalLed.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static void LedHandle_v4GEnabledCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_v4GDisabledCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_v4GErrorCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vBluetoothEnabledCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vBluetoothDisabledCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vBluetoothErrorCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vWifiEnabledCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vWifiDisabledCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vWifiErrorCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vDeviceStandbyCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vDeviceRunningCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vDeviceShutdownCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vDeviceCriticalFaultCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vDeviceMinorFaultCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vDeviceWarningCallback(const EVENT_EVENT_DATA_T* const psEventData);
static void LedHandle_vDeviceNoFaultWarningCallback(const EVENT_EVENT_DATA_T* const psEventData);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize LED Handle module.
* @remark None.
*
* @return Nothing.
*/
void LedHandle_vInit(void)
{
    /* Initialize HAL LED module */
    HalLed_eInit();
    
    /* Subscribe to LED-related events directly with specific callbacks */
    Event_eSubscribe(EVENT_TYPE_4G_COMM_ENABLED_E, LedHandle_v4GEnabledCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_4G_COMM_DISABLED_E, LedHandle_v4GDisabledCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_4G_COMM_ERROR_E, LedHandle_v4GErrorCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E, LedHandle_vBluetoothEnabledCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_BLUETOOTH_COMM_DISABLED_E, LedHandle_vBluetoothDisabledCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_BLUETOOTH_COMM_ERROR_E, LedHandle_vBluetoothErrorCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_WIFI_COMM_ENABLED_E, LedHandle_vWifiEnabledCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_WIFI_COMM_DISABLED_E, LedHandle_vWifiDisabledCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_WIFI_COMM_ERROR_E, LedHandle_vWifiErrorCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_DEVICE_STANDBY_E, LedHandle_vDeviceStandbyCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_DEVICE_RUNNING_E, LedHandle_vDeviceRunningCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_DEVICE_SHUTDOWN_E, LedHandle_vDeviceShutdownCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_DEVICE_CRITICAL_FAULT_E, LedHandle_vDeviceCriticalFaultCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_DEVICE_MINOR_FAULT_E, LedHandle_vDeviceMinorFaultCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_DEVICE_WARNING_E, LedHandle_vDeviceWarningCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
    Event_eSubscribe(EVENT_TYPE_DEVICE_NO_FAULT_WARNING_E, LedHandle_vDeviceNoFaultWarningCallback, EVENT_SUBSCRIBER_LED_HANDLE_E);
}

/**
* @brief Run LED Handle module (should be called periodically).
* @remark None.
*
* @return Nothing.
*/
void LedHandle_vRun(void)
{
    /* Process subscribed events */
    Event_eProcessSubscriberEvents(EVENT_SUBSCRIBER_LED_HANDLE_E);
    
    /* Update HAL LED blinking */
    HalLed_eUpdateBlinkLeds();
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief 4G communication enabled event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_v4GEnabledCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_4G_WHITE_E, HALLED_STATE_ON_E);
}

/**
* @brief 4G communication disabled event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_v4GDisabledCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_4G_WHITE_E, HALLED_STATE_OFF_E);
}

/**
* @brief 4G communication error event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_v4GErrorCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_4G_WHITE_E, HALLED_STATE_BLINK_E);
}

/**
* @brief Bluetooth communication enabled event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vBluetoothEnabledCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_BLUETOOTH_WHITE_E, HALLED_STATE_ON_E);
}

/**
* @brief Bluetooth communication disabled event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vBluetoothDisabledCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_BLUETOOTH_WHITE_E, HALLED_STATE_OFF_E);
}

/**
* @brief Bluetooth communication error event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vBluetoothErrorCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_BLUETOOTH_WHITE_E, HALLED_STATE_BLINK_E);
}

/**
* @brief WiFi communication enabled event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vWifiEnabledCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_WIFI_WHITE_E, HALLED_STATE_ON_E);
}

/**
* @brief WiFi communication disabled event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vWifiDisabledCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_WIFI_WHITE_E, HALLED_STATE_OFF_E);
}

/**
* @brief WiFi communication error event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vWifiErrorCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_WIFI_WHITE_E, HALLED_STATE_BLINK_E);
}

/**
* @brief Device standby event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vDeviceStandbyCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_STATUS_GREEN_E, HALLED_STATE_ON_E);
}

/**
* @brief Device running event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vDeviceRunningCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_STATUS_GREEN_E, HALLED_STATE_BLINK_E);
}

/**
* @brief Device shutdown event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vDeviceShutdownCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_STATUS_GREEN_E, HALLED_STATE_OFF_E);
}

/**
* @brief Device critical fault event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vDeviceCriticalFaultCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_WARNING_RED_E, HALLED_STATE_ON_E);
    HalLed_eSetLedState(HALLED_LED_WARNING_YELLOW_E, HALLED_STATE_OFF_E);
}

/**
* @brief Device minor fault event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vDeviceMinorFaultCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_WARNING_RED_E, HALLED_STATE_BLINK_E);
    HalLed_eSetLedState(HALLED_LED_WARNING_YELLOW_E, HALLED_STATE_OFF_E);
}

/**
* @brief Device warning event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vDeviceWarningCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_WARNING_RED_E, HALLED_STATE_OFF_E);
    HalLed_eSetLedState(HALLED_LED_WARNING_YELLOW_E, HALLED_STATE_BLINK_E);
}

/**
* @brief Device no-fault/warning event callback.
* @remark None.
*
* @param psEventData [in]: Pointer to event data.
* @return Nothing.
*/
void LedHandle_vDeviceNoFaultWarningCallback(const EVENT_EVENT_DATA_T* const psEventData)
{
    HalLed_eSetLedState(HALLED_LED_WARNING_RED_E, HALLED_STATE_OFF_E);
    HalLed_eSetLedState(HALLED_LED_WARNING_YELLOW_E, HALLED_STATE_OFF_E);
}

//===========================================================================
// End of file.
//===========================================================================








