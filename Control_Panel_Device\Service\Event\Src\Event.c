//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file Event.c
*
* @brief Event module implementation file - Publisher-Subscriber pattern
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "Event.h"
#include "FreeRTOS.h"
#include "task.h"
#include "cmsis_os.h"
#include <string.h>

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define EVENT_MUTEX_TIMEOUT_MS_D                1000U                                                           /* Timeout for mutex operations in milliseconds */
#define EVENT_MAX_EVENTS_PER_SUBSCRIBER_D       20U                                                             /* Maximum events allowed in each subscriber's queue */
#define EVENT_MAX_EVENTS_D                      (EVENT_SUBSCRIBER_MAX_E * EVENT_MAX_EVENTS_PER_SUBSCRIBER_D)    /* Total event nodes in system pool */
#define EVENT_MAX_SUBSCRIPTIONS_D               (EVENT_SUBSCRIBER_MAX_E * 20U)                                  /* Total subscription nodes in system pool */

/**
* @brief Event subscription node structure - Linked list node for subscriber's event type subscriptions
*/
typedef struct EVENT_SUBSCRIPTION_NODE_T
{
    EVENT_EVENT_TYPE_E eEventType;                                          /* Event type that subscriber is interested in */
    void (*pvEventCallback)(const EVENT_EVENT_DATA_T* const psEventData);   /* Callback function to be called when event occurs */
    struct EVENT_SUBSCRIPTION_NODE_T* psNext;                               /* Pointer to next subscription node in linked list */
} EVENT_SUBSCRIPTION_NODE_T;

/**
* @brief Event node structure - Linked list node for queued events in subscriber's event queue
*/
typedef struct EVENT_EVENT_NODE_T
{
    EVENT_EVENT_DATA_T sEventData;                                          /* Event data including type and attached data */
    struct EVENT_EVENT_NODE_T* psNext;                                      /* Pointer to next event node in queue (FIFO order) */
} EVENT_EVENT_NODE_T;

/**
* @brief Event subscriber structure - Contains all data for one event subscriber
*/
typedef struct
{
    EVENT_SUBSCRIPTION_NODE_T* psSubscriptionListHead;                      /* Head of subscription linked list - events this subscriber is interested in */
    EVENT_EVENT_NODE_T* psEventListHead;                                    /* Head of event queue linked list - pending events for this subscriber */
    EVENT_EVENT_NODE_T* psEventListTail;                                    /* Tail of event queue linked list for O(1) enqueue operations */
    U16 u16EventCount;                                                      /* Current number of events in queue (for queue limit checking) */
    osMutexId xSubscriberMutex;                                             /* Mutex for thread-safe access to this subscriber's data */
} EVENT_SUBSCRIBER_T;

/**
* @brief Event node static memory pool structure - Pre-allocated pool for event nodes
*/
typedef struct
{
    EVENT_EVENT_NODE_T asPool[EVENT_MAX_EVENTS_D];                          /* Static array of pre-allocated event nodes */
    BOOL abUsed[EVENT_MAX_EVENTS_D];                                        /* Usage flags array - TRUE if corresponding node is allocated */
    U16 u16Index;                                                           /* Current allocation index for round-robin allocation strategy */
} EVENT_EVENT_NODE_STATIC_POOL_T;

/**
* @brief Event subscription node static memory pool structure - Pre-allocated pool for subscription nodes
*/
typedef struct
{
    EVENT_SUBSCRIPTION_NODE_T asPool[EVENT_MAX_SUBSCRIPTIONS_D];            /* Static array of pre-allocated subscription nodes */
    BOOL abUsed[EVENT_MAX_SUBSCRIPTIONS_D];                                 /* Usage flags array - TRUE if corresponding node is allocated */
    U16 u16Index;                                                           /* Current allocation index for round-robin allocation strategy */
} EVENT_SUBSCRIPTION_NODE_STATIC_POOL_T;

/**
* @brief Event manager structure - Central management structure for the entire event system
*/
typedef struct
{
    EVENT_SUBSCRIBER_T asSubscriberArray[EVENT_SUBSCRIBER_MAX_E];           /* Array of all event subscribers in the system */
    EVENT_EVENT_NODE_STATIC_POOL_T sEventNodePool;                          /* Static memory pool for event nodes */
    EVENT_SUBSCRIPTION_NODE_STATIC_POOL_T sSubscriptionNodePool;            /* Static memory pool for subscription nodes */
    osMutexId xMemoryPoolMutex;                                             /* System-wide mutex for thread-safe memory pool access */
} EVENT_MANAGER_T;

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static EVENT_STATUS_E Event_eLockSubscriberMutex(EVENT_SUBSCRIBER_T* const psSubscriber);
static EVENT_STATUS_E Event_eUnlockSubscriberMutex(EVENT_SUBSCRIBER_T* const psSubscriber);
static BOOL Event_bIsSubscriberSubscribedToEvent(EVENT_SUBSCRIBER_T* const psSubscriber, const EVENT_EVENT_TYPE_E eEventType);
static EVENT_STATUS_E Event_eEnqueueEventToSubscriber(EVENT_SUBSCRIBER_T* const psSubscriber, const EVENT_EVENT_DATA_T* const psEventData);
static EVENT_STATUS_E Event_eDequeueEventFromSubscriber(EVENT_SUBSCRIBER_T* const psSubscriber, EVENT_EVENT_DATA_T* const psEventData);
static EVENT_SUBSCRIPTION_NODE_T* Event_psAllocateSubscriptionNode(void);
static void Event_vFreeSubscriptionNode(EVENT_SUBSCRIPTION_NODE_T* const psNode);
static EVENT_EVENT_NODE_T* Event_psAllocateEventNode(void);
static void Event_vFreeEventNode(EVENT_EVENT_NODE_T* const psNode);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------
static EVENT_MANAGER_T sEventManager = {0U};

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize the Event module (Singleton pattern).
* @remark Must be called before any other Event module functions.
*
* @return Operation status.
*/
EVENT_STATUS_E Event_eInit(void)
{
    /* Initialize Event Node Pool */
    sEventManager.sEventNodePool.u16Index = 0U;

    for (U16 u16Index = 0U; u16Index < EVENT_MAX_EVENTS_D; u16Index++)
    {
        /* Initialize usage flags */
        sEventManager.sEventNodePool.abUsed[u16Index] = FALSE_D;
        
        /* Initialize each event node in the pool */
        sEventManager.sEventNodePool.asPool[u16Index].sEventData.eEventType = EVENT_TYPE_NONE_E;
        sEventManager.sEventNodePool.asPool[u16Index].sEventData.u8DataSize = 0U;
        memset(sEventManager.sEventNodePool.asPool[u16Index].sEventData.au8Data, 0U, EVENT_MAX_EVENT_DATA_SIZE_D);
        sEventManager.sEventNodePool.asPool[u16Index].psNext = NULL_D;
    }

    /* Initialize Subscription Node Pool */
    sEventManager.sSubscriptionNodePool.u16Index = 0U;

    for (U16 u16Index = 0U; u16Index < EVENT_MAX_SUBSCRIPTIONS_D; u16Index++)
    {
        /* Initialize usage flags */
        sEventManager.sSubscriptionNodePool.abUsed[u16Index] = FALSE_D;
        
        /* Initialize each subscription node in the pool */
        sEventManager.sSubscriptionNodePool.asPool[u16Index].eEventType = EVENT_TYPE_NONE_E;
        sEventManager.sSubscriptionNodePool.asPool[u16Index].pvEventCallback = NULL_D;
        sEventManager.sSubscriptionNodePool.asPool[u16Index].psNext = NULL_D;
    }

    /* Create memory pool mutex */
    sEventManager.xMemoryPoolMutex = xSemaphoreCreateMutex();

    if (sEventManager.xMemoryPoolMutex == NULL_D)
    {
        return EVENT_ERROR_E;
    }

    /* Initialize Subscribers Array */
    for (U8 u8SubscriberIndex = 0U; u8SubscriberIndex < EVENT_SUBSCRIBER_MAX_E; u8SubscriberIndex++)
    {
        /* Initialize subscriber structure fields */
        sEventManager.asSubscriberArray[u8SubscriberIndex].psSubscriptionListHead = NULL_D;
        sEventManager.asSubscriberArray[u8SubscriberIndex].psEventListHead = NULL_D;
        sEventManager.asSubscriberArray[u8SubscriberIndex].psEventListTail = NULL_D;
        sEventManager.asSubscriberArray[u8SubscriberIndex].u16EventCount = 0U;

        /* Create mutex for each subscriber */
        sEventManager.asSubscriberArray[u8SubscriberIndex].xSubscriberMutex = xSemaphoreCreateMutex();
        
        if (sEventManager.asSubscriberArray[u8SubscriberIndex].xSubscriberMutex == NULL_D)
        {
            return EVENT_ERROR_E;
        }
    }
    
    return EVENT_OK_E;
}

/**
* @brief Subscribe to a specific event type.
* @remark Register a callback function to be called when the specified event occurs.
*
* @param eEventType [in]: Event type to subscribe to.
* @param pvCallback [in]: Callback function pointer.
* @param eSubscriberId [in]: Subscriber ID that wants to subscribe.
* @return Operation status.
*/
EVENT_STATUS_E Event_eSubscribe(const EVENT_EVENT_TYPE_E eEventType, void (*pvCallback)(const EVENT_EVENT_DATA_T* const psEventData), const EVENT_SUBSCRIBER_E eSubscriberId)
{
    /* Parameter validation */
    if ((eEventType == EVENT_TYPE_NONE_E) || (eEventType >= EVENT_TYPE_MAX_E) || (pvCallback == NULL_D) || (eSubscriberId >= EVENT_SUBSCRIBER_MAX_E))
    {
        return EVENT_ERROR_E;
    }
    
    /* Find subscriber (direct access by ID) */
    EVENT_SUBSCRIBER_T* psSubscriber = &sEventManager.asSubscriberArray[eSubscriberId];
    
    /* Lock subscriber's own mutex for subscription modification */
    if (Event_eLockSubscriberMutex(psSubscriber) != EVENT_OK_E)
    {
        return EVENT_ERROR_E;
    }
    
    /* Check if already subscribed */
    EVENT_SUBSCRIPTION_NODE_T* psCurrentNode = psSubscriber->psSubscriptionListHead;

    while (psCurrentNode != NULL_D)
    {
        if (psCurrentNode->eEventType == eEventType)
        {
            /* Update callback if already subscribed */
            psCurrentNode->pvEventCallback = pvCallback;

            Event_eUnlockSubscriberMutex(psSubscriber);

            return EVENT_OK_E;
        }

        psCurrentNode = psCurrentNode->psNext;
    }
    
    /* Allocate new subscription node */
    EVENT_SUBSCRIPTION_NODE_T* psNewNode = Event_psAllocateSubscriptionNode();

    if (psNewNode == NULL_D)
    {
        Event_eUnlockSubscriberMutex(psSubscriber);

        return EVENT_ERROR_E;
    }
    
    /* Initialize and add to head of list */
    psNewNode->eEventType = eEventType;
    psNewNode->pvEventCallback = pvCallback;
    psNewNode->psNext = psSubscriber->psSubscriptionListHead;

    psSubscriber->psSubscriptionListHead = psNewNode;
    
    Event_eUnlockSubscriberMutex(psSubscriber);

    return EVENT_OK_E;
}

/**
* @brief Unsubscribe from a specific event type.
* @remark Remove the subscriber from the event's subscription list.
*
* @param eEventType [in]: Event type to unsubscribe from.
* @param eSubscriberId [in]: Subscriber ID to remove.
* @return Operation status.
*/
EVENT_STATUS_E Event_eUnsubscribe(const EVENT_EVENT_TYPE_E eEventType, const EVENT_SUBSCRIBER_E eSubscriberId)
{
    /* Parameter validation */
    if ((eEventType == EVENT_TYPE_NONE_E) || (eEventType >= EVENT_TYPE_MAX_E) || (eSubscriberId >= EVENT_SUBSCRIBER_MAX_E))
    {
        return EVENT_ERROR_E;
    }
    
    /* Find subscriber */
    EVENT_SUBSCRIBER_T* psSubscriber = &sEventManager.asSubscriberArray[eSubscriberId];
    
    /* Lock subscriber's own mutex for subscription modification */
    if (Event_eLockSubscriberMutex(psSubscriber) != EVENT_OK_E)
    {
        return EVENT_ERROR_E;
    }
    
    /* Remove subscription from subscriber */
    EVENT_SUBSCRIPTION_NODE_T* psCurrentNode = psSubscriber->psSubscriptionListHead;
    EVENT_SUBSCRIPTION_NODE_T* psPreviousNode = NULL_D;
    
    /* Find and remove subscription node */
    while (psCurrentNode != NULL_D)
    {
        if (psCurrentNode->eEventType == eEventType)
        {
            /* Remove from list */
            if (psPreviousNode == NULL_D)
            {
                /* First node */
                psSubscriber->psSubscriptionListHead = psCurrentNode->psNext;
            }
            else
            {
                psPreviousNode->psNext = psCurrentNode->psNext;
            }

            Event_vFreeSubscriptionNode(psCurrentNode);
    
            Event_eUnlockSubscriberMutex(psSubscriber);

            return EVENT_OK_E;
        }
        
        psPreviousNode = psCurrentNode;
        psCurrentNode = psCurrentNode->psNext;
    }
    
    /* Subscription not found */
    Event_eUnlockSubscriberMutex(psSubscriber);

    return EVENT_ERROR_E;
}

/**
* @brief Unsubscribe from all event types for a specific subscriber.
* @remark Remove all subscriptions for the specified subscriber.
*
* @param eSubscriberId [in]: Subscriber ID to remove all subscriptions for.
* @return Operation status.
*/
EVENT_STATUS_E Event_eUnsubscribeAll(const EVENT_SUBSCRIBER_E eSubscriberId)
{
    /* Parameter validation */
    if (eSubscriberId >= EVENT_SUBSCRIBER_MAX_E)
    {
        return EVENT_ERROR_E;
    }
    
    /* Find subscriber */
    EVENT_SUBSCRIBER_T* psSubscriber = &sEventManager.asSubscriberArray[eSubscriberId];
    
    /* Lock subscriber's own mutex for subscription modification */
    if (Event_eLockSubscriberMutex(psSubscriber) != EVENT_OK_E)
    {
        return EVENT_ERROR_E;
    }
    
    /* Remove all subscriptions from subscriber */
    EVENT_SUBSCRIPTION_NODE_T* psCurrentNode = psSubscriber->psSubscriptionListHead;
    EVENT_SUBSCRIPTION_NODE_T* psNextNode;
    
    while (psCurrentNode != NULL_D)
    {
        /* Save next node pointer before freeing current node */
        psNextNode = psCurrentNode->psNext;
        
        /* Free current subscription node */
        Event_vFreeSubscriptionNode(psCurrentNode);
        
        /* Move to next node */
        psCurrentNode = psNextNode;
    }
    
    /* Clear subscription list head pointer */
    psSubscriber->psSubscriptionListHead = NULL_D;
    
    Event_eUnlockSubscriberMutex(psSubscriber);
    
    return EVENT_OK_E;
}

/**
* @brief Publish an event with attached data.
* @remark Notify all subscribers of the specified event type.
*
* @param eEventType [in]: Event type to publish.
* @param pu8Data [in]: Pointer to event data (can be NULL if no data).
* @param u8DataSize [in]: Size of event data in bytes (0 if no data).
* @return Operation status.
*/
EVENT_STATUS_E Event_ePublish(const EVENT_EVENT_TYPE_E eEventType, const U8* const pu8Data, const U8 u8DataSize)
{
    /* Parameter validation */
    if ((eEventType == EVENT_TYPE_NONE_E) || (eEventType >= EVENT_TYPE_MAX_E) || (u8DataSize > EVENT_MAX_EVENT_DATA_SIZE_D) || ((u8DataSize > 0U) && (pu8Data == NULL_D)))
    {
        return EVENT_ERROR_E;
    }
    
    /* Create event data structure */
    EVENT_EVENT_DATA_T sEventData;
    sEventData.eEventType = eEventType;
    sEventData.u8DataSize = u8DataSize;
    
    /* Copy event data if provided */
    if ((pu8Data != NULL_D) && (u8DataSize > 0U))
    {
        memcpy(sEventData.au8Data, pu8Data, u8DataSize);
    }
    else
    {
        memset(sEventData.au8Data, 0U, EVENT_MAX_EVENT_DATA_SIZE_D);
    }
    
    /* Traverse all subscribers and check if they subscribed to this event */
    BOOL bHasError = FALSE_D;
    
    for (U8 u8SubscriberIndex = 0U; u8SubscriberIndex < EVENT_SUBSCRIBER_MAX_E; u8SubscriberIndex++)
    {
        EVENT_SUBSCRIBER_T* psCurrentSubscriber = &sEventManager.asSubscriberArray[u8SubscriberIndex];
        
        /* Check if subscriber is subscribed to this event type (using subscriber's own mutex) */
        if (Event_bIsSubscriberSubscribedToEvent(psCurrentSubscriber, eEventType))
        {
            /* Add event to subscriber's queue (using subscriber's own mutex) */
            if (Event_eEnqueueEventToSubscriber(psCurrentSubscriber, &sEventData) != EVENT_OK_E)
            {
                /* Record error but continue with other subscribers */
                bHasError = TRUE_D;
            }
        }
    }
    
    /* Return error if any subscriber failed to receive the event */
    return (bHasError == TRUE_D) ? EVENT_ERROR_E : EVENT_OK_E;
}

/**
* @brief Process all pending events in the subscriber's event queue.
* @remark This function should be called periodically by subscriber modules to process their queued events.
*
* @param eSubscriberId [in]: Subscriber ID to process events for.
* @return Operation status.
*/
EVENT_STATUS_E Event_eProcessSubscriberEvents(const EVENT_SUBSCRIBER_E eSubscriberId)
{
    /* Parameter validation */
    if (eSubscriberId >= EVENT_SUBSCRIBER_MAX_E)
    {
        return EVENT_ERROR_E;
    }
    
    /* Find subscriber */
    EVENT_SUBSCRIBER_T* psSubscriber = &sEventManager.asSubscriberArray[eSubscriberId];
    
    /* Lock subscriber's own mutex for event processing */
    if (Event_eLockSubscriberMutex(psSubscriber) != EVENT_OK_E)
    {
        return EVENT_ERROR_E;
    }
    
    /* Process all events in the subscriber's queue */
    EVENT_EVENT_DATA_T sEventData;

    while (Event_eDequeueEventFromSubscriber(psSubscriber, &sEventData) == EVENT_OK_E)
    {
        /* Get the specific callback for this event type from subscriber's subscription list */
        void (*pvEventCallback)(const EVENT_EVENT_DATA_T* const psEventData) = NULL_D;

        EVENT_SUBSCRIPTION_NODE_T* psCurrentSubNode = psSubscriber->psSubscriptionListHead;

        while (psCurrentSubNode != NULL_D)
        {
            if (psCurrentSubNode->eEventType == sEventData.eEventType)
            {
                pvEventCallback = psCurrentSubNode->pvEventCallback;

                break;
            }

            psCurrentSubNode = psCurrentSubNode->psNext;
        }

        if (pvEventCallback != NULL_D)
        {
            /* Unlock mutex before calling callback to avoid deadlock */
            Event_eUnlockSubscriberMutex(psSubscriber);
            
            /* Call the specific callback function for this event */
            pvEventCallback(&sEventData);
            
            /* Re-lock mutex for next iteration */
            if (Event_eLockSubscriberMutex(psSubscriber) != EVENT_OK_E)
            {
                return EVENT_ERROR_E;
            }
        }
    }
    
    Event_eUnlockSubscriberMutex(psSubscriber);
    
    return EVENT_OK_E;
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Lock a specific subscriber's mutex.
* @remark Acquire exclusive access to subscriber's data structures.
*
* @param psSubscriber [in]: Pointer to subscriber.
* @return Operation status.
*/
EVENT_STATUS_E Event_eLockSubscriberMutex(EVENT_SUBSCRIBER_T* const psSubscriber)
{
    if ((psSubscriber == NULL_D) || (psSubscriber->xSubscriberMutex == NULL_D))
    {
        return EVENT_ERROR_E;
    }
    
    if (xSemaphoreTake(psSubscriber->xSubscriberMutex, pdMS_TO_TICKS(EVENT_MUTEX_TIMEOUT_MS_D)) != pdTRUE)
    {
        return EVENT_ERROR_E;
    }
    
    return EVENT_OK_E;
}

/**
* @brief Unlock a specific subscriber's mutex.
* @remark Release exclusive access to subscriber's data structures.
*
* @param psSubscriber [in]: Pointer to subscriber.
* @return Operation status.
*/
EVENT_STATUS_E Event_eUnlockSubscriberMutex(EVENT_SUBSCRIBER_T* const psSubscriber)
{
    if ((psSubscriber == NULL_D) || (psSubscriber->xSubscriberMutex == NULL_D))
    {
        return EVENT_ERROR_E;
    }
    
    if (xSemaphoreGive(psSubscriber->xSubscriberMutex) != pdTRUE)
    {
        return EVENT_ERROR_E;
    }
    
    return EVENT_OK_E;
}

/**
* @brief Check if subscriber is subscribed to specific event type.
* @remark Search subscriber's event type list for the specified event.
*
* @param psSubscriber [in]: Pointer to subscriber.
* @param eEventType [in]: Event type to check.
* @return TRUE if subscribed, FALSE otherwise.
*/
BOOL Event_bIsSubscriberSubscribedToEvent(EVENT_SUBSCRIBER_T* const psSubscriber, const EVENT_EVENT_TYPE_E eEventType)
{
    if (psSubscriber == NULL_D)
    {
        return FALSE_D;
    }
    
    /* Lock subscriber's mutex for safe access to subscription list */
    if (Event_eLockSubscriberMutex(psSubscriber) != EVENT_OK_E)
    {
        return FALSE_D;
    }
    
    EVENT_SUBSCRIPTION_NODE_T* psCurrentNode = psSubscriber->psSubscriptionListHead;

    BOOL bIsSubscribed = FALSE_D;
    
    while (psCurrentNode != NULL_D)
    {
        if (psCurrentNode->eEventType == eEventType)
        {
            bIsSubscribed = TRUE_D;

            break;
        }

        psCurrentNode = psCurrentNode->psNext;
    }
    
    Event_eUnlockSubscriberMutex(psSubscriber);

    return bIsSubscribed;
}

/**
* @brief Add event to subscriber's event queue (linked list).
* @remark Thread-safe enqueueing of event data to specific subscriber.
*
* @param psSubscriber [in]: Pointer to subscriber.
* @param psEventData [in]: Pointer to event data to enqueue.
* @return Operation status.
*/
EVENT_STATUS_E Event_eEnqueueEventToSubscriber(EVENT_SUBSCRIBER_T* const psSubscriber, const EVENT_EVENT_DATA_T* const psEventData)
{
    if ((psSubscriber == NULL_D) || (psEventData == NULL_D))
    {
        return EVENT_ERROR_E;
    }
    
    /* Lock subscriber's mutex for safe queue access */
    if (Event_eLockSubscriberMutex(psSubscriber) != EVENT_OK_E)
    {
        return EVENT_ERROR_E;
    }
    
    /* Check if queue is full (optional limit) */
    if (psSubscriber->u16EventCount >= EVENT_MAX_EVENTS_PER_SUBSCRIBER_D)
    {
        Event_eUnlockSubscriberMutex(psSubscriber);

        return EVENT_ERROR_E;
    }
    
    /* Allocate a new event node */
    EVENT_EVENT_NODE_T* psNewNode = Event_psAllocateEventNode();

    if (psNewNode == NULL_D)
    {
        Event_eUnlockSubscriberMutex(psSubscriber);

        return EVENT_ERROR_E;
    }
    
    /* Copy event data to node */
    memcpy(&psNewNode->sEventData, psEventData, sizeof(EVENT_EVENT_DATA_T));

    psNewNode->psNext = NULL_D;
    
    /* Add node to tail of linked list (FIFO - First In First Out) */
    if (psSubscriber->psEventListHead == NULL_D)
    {
        /* Queue is empty - set as first node */
        psSubscriber->psEventListHead = psNewNode;
        psSubscriber->psEventListTail = psNewNode;
    }
    else
    {
        /* Append to tail using tail pointer (O(1) operation) */
        psSubscriber->psEventListTail->psNext = psNewNode;
        psSubscriber->psEventListTail = psNewNode;
    }

    psSubscriber->u16EventCount++;
    
    Event_eUnlockSubscriberMutex(psSubscriber);

    return EVENT_OK_E;
}

/**
* @brief Remove event from subscriber's event queue (linked list).
* @remark This function must be called with subscriber's mutex already locked. The caller is responsible for proper thread synchronization.
*
* @param psSubscriber [in]: Pointer to subscriber.
* @param psEventData [out]: Pointer to store dequeued event data.
* @return Operation status.
*/
EVENT_STATUS_E Event_eDequeueEventFromSubscriber(EVENT_SUBSCRIBER_T* const psSubscriber, EVENT_EVENT_DATA_T* const psEventData)
{
    if ((psSubscriber == NULL_D) || (psEventData == NULL_D))
    {
        return EVENT_ERROR_E;
    }
    
    /* Check if queue is empty */
    if ((psSubscriber->psEventListHead == NULL_D) || (psSubscriber->u16EventCount == 0U))
    {
        return EVENT_ERROR_E;
    }
    
    /* Get node from head (FIFO - First In First Out) */
    EVENT_EVENT_NODE_T* psNode = psSubscriber->psEventListHead;
    
    /* Copy event data */
    memcpy(psEventData, &psNode->sEventData, sizeof(EVENT_EVENT_DATA_T));
    
    /* Update queue head */
    psSubscriber->psEventListHead = psNode->psNext;
    
    /* Update tail pointer if queue becomes empty */
    if (psSubscriber->psEventListHead == NULL_D)
    {
        psSubscriber->psEventListTail = NULL_D;
    }
    
    psSubscriber->u16EventCount--;
    
    /* Free the node */
    Event_vFreeEventNode(psNode);
    
    return EVENT_OK_E;
}

/**
* @brief Allocate subscription node from the static pool.
* @remark Find and return an unused subscription node from the pool.
*
* @return Pointer to allocated node (NULL if none available).
*/
EVENT_SUBSCRIPTION_NODE_T* Event_psAllocateSubscriptionNode(void)
{
    EVENT_SUBSCRIPTION_NODE_T* psAllocatedNode = NULL_D;

    /* Lock memory pool mutex for thread-safe allocation */
    if (xSemaphoreTake(sEventManager.xMemoryPoolMutex, pdMS_TO_TICKS(EVENT_MUTEX_TIMEOUT_MS_D)) == pdTRUE)
    {
        /* Search for an unused node starting from current index */
        for (U16 u16SearchIndex = 0U; u16SearchIndex < EVENT_MAX_SUBSCRIPTIONS_D; u16SearchIndex++)
        {
            U16 u16Index = (sEventManager.sSubscriptionNodePool.u16Index + u16SearchIndex) % EVENT_MAX_SUBSCRIPTIONS_D;
            
            if (!sEventManager.sSubscriptionNodePool.abUsed[u16Index])
            {
                sEventManager.sSubscriptionNodePool.abUsed[u16Index] = TRUE_D;
                sEventManager.sSubscriptionNodePool.u16Index = (u16Index + 1U) % EVENT_MAX_SUBSCRIPTIONS_D;

                psAllocatedNode = &sEventManager.sSubscriptionNodePool.asPool[u16Index];

                break;
            }
        }

        /* Release memory pool mutex */
        xSemaphoreGive(sEventManager.xMemoryPoolMutex);
    }
    
    return psAllocatedNode;
}

/**
* @brief Free subscription node back to the static pool.
* @remark Mark the subscription node as unused in the pool.
*
* @param psNode [in]: Pointer to node to free.
* @return None.
*/
void Event_vFreeSubscriptionNode(EVENT_SUBSCRIPTION_NODE_T* const psNode)
{
    if (psNode == NULL_D)
    {
        return;
    }
    
    /* Lock memory pool mutex for thread-safe deallocation */
    if (xSemaphoreTake(sEventManager.xMemoryPoolMutex, pdMS_TO_TICKS(EVENT_MUTEX_TIMEOUT_MS_D)) == pdTRUE)
    {
        /* Calculate node index in the pool */
        EVENT_SUBSCRIPTION_NODE_T* psBase = sEventManager.sSubscriptionNodePool.asPool;
        EVENT_SUBSCRIPTION_NODE_T* psEnd  = psBase + EVENT_MAX_SUBSCRIPTIONS_D;

        if ((psNode >= psBase) && (psNode < psEnd))
        {
            U16 u16Index = (U16)(psNode - psBase);

            if (sEventManager.sSubscriptionNodePool.abUsed[u16Index] == TRUE_D)
            {
                sEventManager.sSubscriptionNodePool.abUsed[u16Index] = FALSE_D;
            }
        }

        /* Release memory pool mutex */
        xSemaphoreGive(sEventManager.xMemoryPoolMutex);
    }
}

/**
* @brief Allocate event node from the static pool.
* @remark Find and return an unused event node from the pool.
*
* @return Pointer to allocated node (NULL if none available).
*/
EVENT_EVENT_NODE_T* Event_psAllocateEventNode(void)
{
    EVENT_EVENT_NODE_T* psAllocatedNode = NULL_D;

    /* Lock memory pool mutex for thread-safe allocation */
    if (xSemaphoreTake(sEventManager.xMemoryPoolMutex, pdMS_TO_TICKS(EVENT_MUTEX_TIMEOUT_MS_D)) == pdTRUE)
    {
        /* Search for an unused node starting from current index */
        for (U16 u16SearchIndex = 0U; u16SearchIndex < EVENT_MAX_EVENTS_D; u16SearchIndex++)
        {
            U16 u16Index = (sEventManager.sEventNodePool.u16Index + u16SearchIndex) % EVENT_MAX_EVENTS_D;
            
            if (!sEventManager.sEventNodePool.abUsed[u16Index])
            {
                sEventManager.sEventNodePool.abUsed[u16Index] = TRUE_D;
                sEventManager.sEventNodePool.u16Index = (u16Index + 1U) % EVENT_MAX_EVENTS_D;
                
                psAllocatedNode = &sEventManager.sEventNodePool.asPool[u16Index];
                
                break;
            }
        }

        /* Release memory pool mutex */
        xSemaphoreGive(sEventManager.xMemoryPoolMutex);
    }
    
    return psAllocatedNode;
}

/**
* @brief Free event node back to the static pool.
* @remark Mark the event node as unused in the pool.
*
* @param psNode [in]: Pointer to node to free.
* @return None.
*/
void Event_vFreeEventNode(EVENT_EVENT_NODE_T* const psNode)
{
    if (psNode == NULL_D)
    {
        return;
    }
    
    /* Lock memory pool mutex for thread-safe deallocation */
    if (xSemaphoreTake(sEventManager.xMemoryPoolMutex, pdMS_TO_TICKS(EVENT_MUTEX_TIMEOUT_MS_D)) == pdTRUE)
    {
        /* Calculate node index in the pool */
        EVENT_EVENT_NODE_T* psBase = sEventManager.sEventNodePool.asPool;
        EVENT_EVENT_NODE_T* psEnd  = psBase + EVENT_MAX_EVENTS_D;

        if ((psNode >= psBase) && (psNode < psEnd))
        {
            U16 u16Index = (U16)(psNode - psBase);

            if (sEventManager.sEventNodePool.abUsed[u16Index] == TRUE_D)
            {
                sEventManager.sEventNodePool.abUsed[u16Index] = FALSE_D;
            }
        }

        /* Release memory pool mutex */
        xSemaphoreGive(sEventManager.xMemoryPoolMutex);
    }
}

//===========================================================================
// End of file.
//===========================================================================








