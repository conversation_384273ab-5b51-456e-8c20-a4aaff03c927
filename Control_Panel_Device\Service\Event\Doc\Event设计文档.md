- [Event](#Event)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# Event

## 描述

`Event`模块作为系统的事件管理中心，提供发布-订阅模式的事件处理机制。该模块主要负责事件的注册、发布、订阅和分发，为系统中各个模块之间提供松耦合的通信方式，支持多线程环境下的安全事件处理。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0075    |   左软键功能   |
| MNT0076 | 右软键功能 |
| MNT0077 | 方向键功能 |
| MNT0078 | 启动键功能 |
| MNT0079 | 停止键功能 |
| MNT0080 | 帮助键功能 |
| MNT0081 | 控制权切换功能 |
| MNT0082 | 左方向键+右方向键 |
| MNT0083 | 左方向键+上方向键 |
| MNT0084 | 左方向键+下方向键 |
| MNT0085 | 右方向键+上方向键 |
| MNT0086 | 右方向键+下方向键 |
| MNT0087 | 左方向键+帮助键   |

### 软件需求

1) 应能够识别按键输入事件并入栈，在处理完按键事件后出栈
2) 应能够识别外部输入事件并入栈，在处理完外部指令后出栈
3) 应能够识别内部反馈事件，并在处理完后出栈

### 假设

1) 系统运行在FreeRTOS环境中，支持互斥锁和信号量机制。
2) GlobalTypes类型定义已正确包含并定义了基本数据类型。
3) 所有订阅者模块会定期调用事件处理函数来处理待处理的事件。

***

## 平台资源

接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `xSemaphoreCreateMutex()` | 创建互斥锁，用于线程同步。 |
| `xSemaphoreTake()` | 获取互斥锁，实现线程安全访问。 |
| `xSemaphoreGive()` | 释放互斥锁，解除线程锁定。 |
| `vSemaphoreDelete()` | 删除互斥锁，释放系统资源。 |
| `memset()` | 内存清零，用于结构体初始化。 |
| `memcpy()` | 内存拷贝，用于事件数据复制。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用发布-订阅设计模式，实现系统中模块间的松耦合通信：

1. **事件管理器**：作为系统的中央事件管理器，负责管理所有订阅者和事件队列。
2. **静态内存池**：采用预分配的静态内存池来管理事件节点和订阅节点，避免动态内存分配的开销和碎片化。
3. **独立事件队列**：为每个订阅者维护独立的FIFO事件队列，确保事件的有序处理。
4. **线程安全设计**：使用FreeRTOS互斥锁机制，确保多线程环境下的数据一致性。

具体工作流程：
1. 系统初始化时调用`Event_eInit()`创建事件管理器和相关资源。
2. 各模块通过`Event_eSubscribe()`订阅感兴趣的事件类型。
3. 模块通过`Event_ePublish()`发布事件到系统中。
4. 事件管理器将事件分发到所有相关订阅者的事件队列中。
5. 订阅者定期调用`Event_eProcessSubscriberEvents()`处理队列中的事件。
6. 模块可以通过`Event_eUnsubscribe()`取消特定事件类型的订阅。
7. 模块可以通过`Event_eUnsubscribeAll()`一次性清空指定订阅者的所有订阅。

### 静态设计

模块的静态设计如下所示：

![类图](Image/Event_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/Event_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：
   
   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `Event_eInit()` | 5      |
   | `Event_eSubscribe()` | 6 |
   | `Event_eUnsubscribe()` | 6 |
   | `Event_eUnsubscribeAll()` | 6 |
   | `Event_ePublish()` | 6 |
   | `Event_eProcessSubscriberEvents()` | 7 |
   | `Event_eLockSubscriberMutex()` | 3 |
   | `Event_eUnlockSubscriberMutex()` | 3 |
   | `Event_bIsSubscriberSubscribedToEvent()` | 6 |
   | `Event_eEnqueueEventToSubscriber()` | 6 |
   | `Event_eDequeueEventFromSubscriber()` | 4 |
   | `Event_psAllocateSubscriptionNode()` | 4 |
   | `Event_vFreeSubscriptionNode()` | 5 |
   | `Event_psAllocateEventNode()` | 4 |
   | `Event_vFreeEventNode()` | 5 |
   
2. 其他测试项：目前无免费工具，暂不列出。  

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用STM32CubeIDE搭建测试工程，硬件板卡为安富莱STM32-V5开发板。

   1.2 使用FreeRTOS新建StartKeyHandleTask任务，新建StartLedHandleTask任务，在任务之间使用Event模块的发布-订阅机制测试方案。

2. 函数测试详细结果

   2.1 Event_eInit()

      2.1.1 分支1：互斥锁创建失败（内存池互斥锁）

      - 测试用例：模拟`xSemaphoreCreateMutex()`返回NULL
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.1.2 分支2：互斥锁创建失败（订阅者互斥锁）

      - 测试用例：第一个订阅者互斥锁创建失败
      - 预期结果：返回 `EVENT_ERROR_E`，清理已创建的资源
      - 测试结果：通过，函数正确清理资源并返回错误状态

      2.1.3 分支3：互斥锁创建失败（中间订阅者互斥锁）

      - 测试用例：第二个订阅者互斥锁创建失败
      - 预期结果：返回 `EVENT_ERROR_E`，清理所有已创建的资源
      - 测试结果：通过，函数正确清理所有先前创建的互斥锁

      2.1.4 分支4：正常初始化

      - 测试用例：所有资源创建成功
      - 预期结果：返回 `EVENT_OK_E`，所有数据结构正确初始化
      - 测试结果：通过，内存池、订阅者数组、互斥锁均正确初始化

   2.2 Event_eSubscribe()

      2.2.1 分支1：事件类型参数无效

      - 测试用例：传入 `eEventType = EVENT_TYPE_NONE_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.2.2 分支2：事件类型参数超出范围

      - 测试用例：传入 `eEventType = EVENT_TYPE_MAX_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测参数范围

      2.2.3 分支3：回调函数指针为空

      - 测试用例：传入 `pvCallback = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.2.4 分支4：订阅者ID超出范围

      - 测试用例：传入 `eSubscriberId = EVENT_SUBSCRIBER_MAX_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测订阅者ID范围

      2.2.5 分支5：互斥锁获取失败

      - 测试用例：模拟`xSemaphoreTake()`返回失败
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理互斥锁获取失败

      2.2.6 分支6：重复订阅同一事件类型

      - 测试用例：先订阅EVENT_TYPE_KEY_LEFT_SHORT_PRESS_E，再次订阅相同事件
      - 预期结果：返回 `EVENT_OK_E`，更新回调函数指针
      - 测试结果：通过，函数正确更新现有订阅的回调函数

      2.2.7 分支7：订阅节点分配失败

      - 测试用例：内存池已满，无法分配新的订阅节点
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理内存分配失败

      2.2.8 分支8：正常订阅新事件

      - 测试用例：订阅EVENT_TYPE_KEY_LEFT_SHORT_PRESS_E事件
      - 预期结果：返回 `EVENT_OK_E`，订阅节点正确添加到链表头部
      - 测试结果：通过，订阅成功，回调函数正确设置

   2.3 Event_eUnsubscribe()

      2.3.1 分支1：事件类型参数无效

      - 测试用例：传入 `eEventType = EVENT_TYPE_NONE_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.3.2 分支2：事件类型参数超出范围

      - 测试用例：传入 `eEventType = EVENT_TYPE_MAX_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测参数范围

      2.3.3 分支3：订阅者ID超出范围

      - 测试用例：传入 `eSubscriberId = EVENT_SUBSCRIBER_MAX_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测订阅者ID范围

      2.3.4 分支4：互斥锁获取失败

      - 测试用例：模拟`xSemaphoreTake()`返回失败
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理互斥锁获取失败

      2.3.5 分支5：取消订阅链表头部节点

      - 测试用例：取消订阅链表第一个节点的事件类型
      - 预期结果：返回 `EVENT_OK_E`，正确更新链表头指针
      - 测试结果：通过，头部节点正确移除，内存正确释放

      2.3.6 分支6：取消订阅链表中间节点

      - 测试用例：取消订阅链表中间节点的事件类型
      - 预期结果：返回 `EVENT_OK_E`，正确更新前驱节点指针
      - 测试结果：通过，中间节点正确移除，链表结构保持完整

      2.3.7 分支7：取消订阅不存在的事件类型

      - 测试用例：尝试取消订阅未曾订阅的事件类型
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

   2.4 Event_eUnsubscribeAll()

      2.4.1 分支1：订阅者ID超出范围

      - 测试用例：传入 `eSubscriberId = EVENT_SUBSCRIBER_MAX_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测订阅者ID范围

      2.4.2 分支2：互斥锁获取失败

      - 测试用例：模拟`Event_eLockSubscriberMutex()`返回失败
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理互斥锁获取失败

      2.4.3 分支3：清空空订阅列表

      - 测试用例：订阅者没有任何订阅
      - 预期结果：返回 `EVENT_OK_E`，操作正常完成
      - 测试结果：通过，函数正确处理空列表情况

      2.4.4 分支4：清空单个订阅

      - 测试用例：订阅者只有一个订阅
      - 预期结果：返回 `EVENT_OK_E`，订阅节点正确释放，链表头指针变为NULL
      - 测试结果：通过，单个订阅正确清除

      2.4.5 分支5：清空多个订阅

      - 测试用例：订阅者有多个不同事件类型的订阅
      - 预期结果：返回 `EVENT_OK_E`，所有订阅节点正确释放，链表头指针变为NULL
      - 测试结果：通过，所有订阅正确清除，内存正确释放

      2.4.6 分支6：重复调用清空订阅

      - 测试用例：对同一订阅者连续调用两次`Event_eUnsubscribeAll()`
      - 预期结果：两次调用都返回 `EVENT_OK_E`
      - 测试结果：通过，函数正确处理重复清空操作

   2.5 Event_ePublish()

      2.5.1 分支1：事件类型参数无效

      - 测试用例：传入 `eEventType = EVENT_TYPE_NONE_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.5.2 分支2：事件类型参数超出范围

      - 测试用例：传入 `eEventType = EVENT_TYPE_MAX_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测参数范围

      2.5.3 分支3：数据大小超出限制

      - 测试用例：传入 `u16DataSize > EVENT_MAX_EVENT_DATA_SIZE_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测数据大小限制

      2.5.4 分支4：数据指针和大小不匹配

      - 测试用例：传入 `u16DataSize > 0` 但 `pu8Data = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测参数不匹配

      2.5.5 分支5：发布无订阅者的事件

      - 测试用例：发布没有任何模块订阅的事件类型
      - 预期结果：返回 `EVENT_OK_E`
      - 测试结果：通过，函数正常处理无订阅者情况

      2.5.6 分支6：发布有订阅者的事件（成功）

      - 测试用例：发布EVENT_TYPE_KEY_LEFT_SHORT_PRESS_E事件，有订阅者
      - 预期结果：返回 `EVENT_OK_E`，事件正确加入订阅者队列
      - 测试结果：通过，事件成功分发到所有订阅者

      2.5.7 分支7：发布事件时部分订阅者队列已满

      - 测试用例：某个订阅者的事件队列已满
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确报告部分订阅者接收失败

      2.5.8 分支8：发布带数据的事件

      - 测试用例：发布包含2字节数据的事件
      - 预期结果：返回 `EVENT_OK_E`，数据正确复制到事件结构
      - 测试结果：通过，事件数据正确传递给订阅者

   2.6 Event_eProcessSubscriberEvents()

      2.6.1 分支1：订阅者ID超出范围

      - 测试用例：传入 `eSubscriberId = EVENT_SUBSCRIBER_MAX_E`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测订阅者ID范围

      2.6.2 分支2：互斥锁获取失败

      - 测试用例：模拟`xSemaphoreTake()`返回失败
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理互斥锁获取失败

      2.6.3 分支3：处理空事件队列

      - 测试用例：订阅者事件队列为空
      - 预期结果：返回 `EVENT_OK_E`，无事件处理
      - 测试结果：通过，函数正常处理空队列情况

      2.6.4 分支4：处理单个事件

      - 测试用例：队列中有一个待处理事件
      - 预期结果：返回 `EVENT_OK_E`，事件回调函数被正确调用
      - 测试结果：通过，事件正确处理，回调函数成功执行

      2.6.5 分支5：处理多个事件（FIFO顺序）

      - 测试用例：队列中有多个不同类型的事件
      - 预期结果：返回 `EVENT_OK_E`，事件按FIFO顺序处理
      - 测试结果：通过，事件顺序正确，所有回调函数依次执行

      2.6.6 分支6：处理过程中回调函数为空

      - 测试用例：订阅列表中找不到对应事件的回调函数
      - 预期结果：跳过该事件，继续处理下一个事件
      - 测试结果：通过，函数正确跳过无效回调的事件

   2.7 Event_eLockSubscriberMutex()

      2.7.1 分支1：订阅者指针为空

      - 测试用例：传入 `psSubscriber = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.7.2 分支2：互斥锁句柄为空

      - 测试用例：订阅者的互斥锁句柄为NULL_D
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测无效互斥锁

      2.7.3 分支3：互斥锁获取超时

      - 测试用例：模拟`xSemaphoreTake()`超时返回失败
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理超时情况

      2.7.4 分支4：正常获取互斥锁

      - 测试用例：所有参数有效，互斥锁可用
      - 预期结果：返回 `EVENT_OK_E`
      - 测试结果：通过，互斥锁成功获取

   2.8 Event_eUnlockSubscriberMutex()

      2.8.1 分支1：订阅者指针为空

      - 测试用例：传入 `psSubscriber = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.8.2 分支2：互斥锁句柄为空

      - 测试用例：订阅者的互斥锁句柄为NULL_D
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测无效互斥锁

      2.8.3 分支3：互斥锁释放失败

      - 测试用例：模拟`xSemaphoreGive()`返回失败
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理释放失败

      2.8.4 分支4：正常释放互斥锁

      - 测试用例：所有参数有效，互斥锁已被获取
      - 预期结果：返回 `EVENT_OK_E`
      - 测试结果：通过，互斥锁成功释放

   2.9 Event_bIsSubscriberSubscribedToEvent()

      2.9.1 分支1：订阅者指针为空

      - 测试用例：传入 `psSubscriber = NULL_D`
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，函数正确返回FALSE

      2.9.2 分支2：互斥锁获取失败

      - 测试用例：模拟`Event_eLockSubscriberMutex()`返回失败
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，函数正确处理互斥锁失败

      2.9.3 分支3：订阅者已订阅该事件类型

      - 测试用例：订阅者的订阅列表中包含指定事件类型
      - 预期结果：返回 `TRUE_D`
      - 测试结果：通过，函数正确找到匹配的事件类型

      2.9.4 分支4：订阅者未订阅该事件类型

      - 测试用例：订阅者的订阅列表中不包含指定事件类型
      - 预期结果：返回 `FALSE_D`
      - 测试结果：通过，函数正确返回未订阅状态

   2.10 Event_eEnqueueEventToSubscriber()

      2.10.1 分支1：订阅者指针为空

      - 测试用例：传入 `psSubscriber = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.10.2 分支2：事件数据指针为空

      - 测试用例：传入 `psEventData = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.10.3 分支3：互斥锁获取失败

      - 测试用例：模拟`Event_eLockSubscriberMutex()`返回失败
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理互斥锁获取失败

      2.10.4 分支4：事件队列已满

      - 测试用例：订阅者的事件计数达到最大值
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测队列满状态

      2.10.5 分支5：事件节点分配失败

      - 测试用例：内存池已满，无法分配新的事件节点
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确处理内存分配失败

      2.10.6 分支6：向空队列添加第一个事件

      - 测试用例：订阅者的事件队列为空
      - 预期结果：返回 `EVENT_OK_E`，头尾指针均指向新节点
      - 测试结果：通过，事件正确添加，队列结构正确

      2.10.7 分支7：向非空队列添加事件

      - 测试用例：订阅者的事件队列已有事件
      - 预期结果：返回 `EVENT_OK_E`，新事件添加到队列尾部
      - 测试结果：通过，事件正确添加到尾部，FIFO顺序保持

   2.11 Event_eDequeueEventFromSubscriber()

      2.11.1 分支1：订阅者指针为空

      - 测试用例：传入 `psSubscriber = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.11.2 分支2：事件数据指针为空

      - 测试用例：传入 `psEventData = NULL_D`
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确返回错误状态

      2.11.3 分支3：事件队列为空（头指针为空）

      - 测试用例：订阅者的事件队列头指针为NULL_D
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测空队列

      2.11.4 分支4：事件队列为空（计数为0）

      - 测试用例：订阅者的事件计数为0
      - 预期结果：返回 `EVENT_ERROR_E`
      - 测试结果：通过，函数正确检测空队列

      2.11.5 分支5：从单元素队列取出事件

      - 测试用例：队列中只有一个事件
      - 预期结果：返回 `EVENT_OK_E`，头尾指针均变为NULL_D
      - 测试结果：通过，事件正确取出，队列变为空

      2.11.6 分支6：从多元素队列取出事件

      - 测试用例：队列中有多个事件
      - 预期结果：返回 `EVENT_OK_E`，头指针指向下一个节点
      - 测试结果：通过，事件正确取出，队列结构保持完整

   2.12 Event_psAllocateSubscriptionNode()

      2.12.1 分支1：内存池互斥锁获取失败

      - 测试用例：模拟`xSemaphoreTake()`返回失败
      - 预期结果：返回 `NULL_D`
      - 测试结果：通过，函数正确返回空指针

      2.12.2 分支2：内存池已满，无可用节点

      - 测试用例：所有订阅节点都已被使用
      - 预期结果：返回 `NULL_D`
      - 测试结果：通过，函数正确返回空指针

      2.12.3 分支3：成功分配订阅节点

      - 测试用例：内存池中有可用节点
      - 预期结果：返回有效节点指针，标记为已使用
      - 测试结果：通过，节点成功分配，索引正确更新

      2.12.4 分支4：轮询分配策略测试

      - 测试用例：连续分配多个节点
      - 预期结果：按轮询顺序分配节点
      - 测试结果：通过，分配顺序正确，索引循环更新

   2.13 Event_vFreeSubscriptionNode()

      2.13.1 分支1：节点指针为空

      - 测试用例：传入 `psNode = NULL_D`
      - 预期结果：函数正常返回，无操作
      - 测试结果：通过，函数安全处理空指针

      2.13.2 分支2：内存池互斥锁获取失败

      - 测试用例：模拟`xSemaphoreTake()`返回失败
      - 预期结果：函数正常返回，节点未释放
      - 测试结果：通过，函数正确处理互斥锁失败

      2.13.3 分支3：节点指针超出池范围

      - 测试用例：传入不属于内存池的节点指针
      - 预期结果：函数正常返回，无操作
      - 测试结果：通过，函数正确检测无效指针

      2.13.4 分支4：释放未使用的节点

      - 测试用例：尝试释放已经被标记为未使用的节点
      - 预期结果：函数正常返回，无操作
      - 测试结果：通过，函数正确处理重复释放

      2.13.5 分支5：正常释放节点

      - 测试用例：释放有效的已使用节点
      - 预期结果：节点标记为未使用
      - 测试结果：通过，节点成功释放，可重新分配

   2.14 Event_psAllocateEventNode()

      2.14.1 分支1：内存池互斥锁获取失败

      - 测试用例：模拟`xSemaphoreTake()`返回失败
      - 预期结果：返回 `NULL_D`
      - 测试结果：通过，函数正确返回空指针

      2.14.2 分支2：内存池已满，无可用节点

      - 测试用例：所有事件节点都已被使用
      - 预期结果：返回 `NULL_D`
      - 测试结果：通过，函数正确返回空指针

      2.14.3 分支3：成功分配事件节点

      - 测试用例：内存池中有可用节点
      - 预期结果：返回有效节点指针，标记为已使用
      - 测试结果：通过，节点成功分配，索引正确更新

      2.14.4 分支4：轮询分配策略测试

      - 测试用例：连续分配多个节点
      - 预期结果：按轮询顺序分配节点
      - 测试结果：通过，分配顺序正确，索引循环更新

   2.15 Event_vFreeEventNode()

      2.15.1 分支1：节点指针为空

      - 测试用例：传入 `psNode = NULL_D`
      - 预期结果：函数正常返回，无操作
      - 测试结果：通过，函数安全处理空指针

      2.15.2 分支2：内存池互斥锁获取失败

      - 测试用例：模拟`xSemaphoreTake()`返回失败
      - 预期结果：函数正常返回，节点未释放
      - 测试结果：通过，函数正确处理互斥锁失败

      2.15.3 分支3：节点指针超出池范围

      - 测试用例：传入不属于内存池的节点指针
      - 预期结果：函数正常返回，无操作
      - 测试结果：通过，函数正确检测无效指针

      2.15.4 分支4：释放未使用的节点

      - 测试用例：尝试释放已经被标记为未使用的节点
      - 预期结果：函数正常返回，无操作
      - 测试结果：通过，函数正确处理重复释放

      2.15.5 分支5：正常释放节点

      - 测试用例：释放有效的已使用节点
      - 预期结果：节点标记为未使用
      - 测试结果：通过，节点成功释放，可重新分配

3. 集成测试详细结果

   3.1 LedHandle任务和KeyHandle任务通信测试

      3.1.1 测试目标

      验证不同FreeRTOS任务之间通过Event模块进行松耦合通信情况。

      3.1.2 测试环境

      - 测试任务：StartKeyHandleTask (KeyHandle模块) 和 StartLedHandleTask (LedHandle模块)
      - 测试程序如下：
   
      ```c
      void StartLedHandleTask(void const * argument)
      {
        /* USER CODE BEGIN StartLedHandleTask */
        LedHandle_vInit();
        /* Infinite loop */
        for(;;)
        {
      	  LedHandle_vRun();
          osDelay(1);
        }
        /* USER CODE END StartLedHandleTask */
      }
      
      void KeyHandle_vTriggerSingleKeyLongPressEvent(const KEYHANDLE_SINGLE_KEY_E eSingleKey)
      {
          EVENT_EVENT_TYPE_E eEventType = EVENT_TYPE_NONE_E;
          
          /* Map single key to corresponding long press event type */
          switch (eSingleKey)
          {
              // case KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E: eEventType = EVENT_TYPE_KEY_LEFT_FUNC_LONG_PRESS_E; break;
              // case KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E: eEventType = EVENT_TYPE_KEY_RIGHT_FUNC_LONG_PRESS_E; break;
              // case KEYHANDLE_SINGLE_KEY_UP_E: eEventType = EVENT_TYPE_KEY_UP_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E: eEventType = EVENT_TYPE_4G_COMM_ENABLED_E; break;
              case KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E: eEventType = EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E; break;
              case KEYHANDLE_SINGLE_KEY_UP_E: eEventType = EVENT_TYPE_DEVICE_CRITICAL_FAULT_E; break;
      
              case KEYHANDLE_SINGLE_KEY_DOWN_E: eEventType = EVENT_TYPE_KEY_DOWN_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_LEFT_E: eEventType = EVENT_TYPE_KEY_LEFT_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_RIGHT_E: eEventType = EVENT_TYPE_KEY_RIGHT_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_START_E: eEventType = EVENT_TYPE_KEY_START_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_STOP_E: eEventType = EVENT_TYPE_KEY_STOP_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_HELP_E: eEventType = EVENT_TYPE_KEY_HELP_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_CONTROL_SWITCH_E: eEventType = EVENT_TYPE_KEY_CONTROL_SWITCH_LONG_PRESS_E; break;
              default: return; /* Invalid key type */
          }
          
          /* Publish event without additional data */
          Event_ePublish(eEventType, NULL_D, 0U);
      }
      ```
   
      3.1.3 测试实现
   
      测试程序基于两个独立的FreeRTOS任务实现：
   
      使用KeyHandle任务（事件发布者）扫描按键输入，并使用特定按键长按触发模拟以下事件发布到Event模块中
   - 左功能键长按 → EVENT_TYPE_4G_COMM_ENABLED_E
   
   - 右功能键长按 → EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E
   
   - 上方向键长按 → EVENT_TYPE_DEVICE_CRITICAL_FAULT_E
   
      使用LedHandle订阅LED相关事件，根据接收到的事件控制LED状态。
   
      测试用例1：4G通信状态LED控制
      - 操作：长按左功能键 (1秒以上)
   
      - 预期结果：KeyHandle任务发布EVENT_TYPE_4G_COMM_ENABLED_E事件，LedHandle任务接收事件并点亮4G白色LED
   
      - 测试结果：通过，LED正确响应按键操作。断点调试如下：
   
        ![测试图](Image/4G通信状态LED控制.png)
   
      测试用例2：蓝牙通信状态LED控制 
      - 操作：长按右功能键 (1秒以上)
   
      - 预期结果：KeyHandle任务发布EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E事件，LedHandle任务接收事件并点亮蓝牙白色LED
   
      - 测试结果：通过，LED正确响应按键操作。断点调试如下：
   
        ![测试图](Image/蓝牙通信状态LED控制.png)
   
      测试用例3：设备故障状态LED控制
      - 操作：长按上方向键 (1秒以上)  
   
      - 预期结果：KeyHandle任务发布EVENT_TYPE_DEVICE_CRITICAL_FAULT_E事件，LedHandle任务接收事件并点亮红色警告LED，关闭黄色警告LED
   
      - 测试结果：通过，LED状态正确切换。断点调试如下：
   
        ![测试图](Image/设备故障状态LED控制.png)
   

测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

1. 事件优先级：目前所有事件采用FIFO处理顺序，未来可增加事件优先级机制，让紧急事件优先处理。
5. 异步回调：目前回调函数在事件处理线程中同步执行，未来可考虑异步回调机制以避免阻塞。

### 可重用性声明

1. 该模块依赖于FreeRTOS操作系统和相关的同步原语。
2. 该模块依赖于GlobalTypes类型定义。
3. 该模块依赖于标准C库的内存操作函数（memset、memcpy）。
