//----------------------------------------------------------------------------
/**
* @file HalLed.h
* @remark HalLed public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef HALLED_H_
#define HALLED_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------

/**
* @brief LED hardware index enumeration
*/
typedef enum
{
    HALLED_LED_4G_WHITE_E = 0U,                 /* 4G indicator LED (White) */
    HALLED_LED_BLUETOOTH_WHITE_E,               /* Bluetooth indicator LED (White) */
    HALLED_LED_WIFI_WHITE_E,                    /* WiFi indicator LED (White) */
    HALLED_LED_STATUS_GREEN_E,                  /* Status indicator LED (Green) */
    HALLED_LED_WARNING_RED_E,                   /* Warning indicator LED (Red channel of dual-color LED) */
    HALLED_LED_WARNING_YELLOW_E,                /* Warning indicator LED (Yellow channel of dual-color LED) */
    HALLED_LED_MAX_E                            /* Maximum number of LEDs supported */
} HALLED_LED_INDEX_E;

/**
* @brief LED state enumeration
*/
typedef enum
{
    HALLED_STATE_OFF_E = 0U,                    /* LED is turned off */
    HALLED_STATE_ON_E,                          /* LED is turned on continuously */
    HALLED_STATE_BLINK_E                        /* LED is blinking */
} HALLED_LED_STATE_E;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
void HalLed_eInit(void);
void HalLed_eSetLedState(const HALLED_LED_INDEX_E eLedIndex, const HALLED_LED_STATE_E eState);
void HalLed_eUpdateBlinkLeds(void);

#endif /* HALLED_H_ */

//===========================================================================
// End of file.
//===========================================================================








