# 代码规范检查清单

## 📋 概览
本检查清单用于确保STM32项目代码符合团队编码规范要求。各个模块均需要在上传之前完成以下代码规范检查。

---

---

## 📋 HalKey模块检查清单（Closed）

### 🔤 命名规范
- [x] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [x] **int类型转换**：将int类型转换为U8、U16或U32类型
- [x] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [x] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [x] **宏定义命名**：宏定义命名增加_D后缀
- [x] **枚举命名**：枚举成员命名增加_E后缀
- [x] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范
- [x] **文件头注释**：文件头注释符合规范
- [x] **分割线注释**：分割线注释符合规范  
- [x] **函数头注释**：函数头注释符合规范
- [x] **英文注释**：注释为英文
- [x] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构
- [x] **防卫型声明**：防卫型声明符合规范
- [x] **static关键字**：去除函数定义处的static关键字
- [x] **常量返回值**：避免函数返回恒定值的情况
- [x] **代码换行**：代码中换行正确处理
- [x] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [x] **const关键字**：const关键字应加尽加
- [x] **头文件清理**：移除冗余的头文件引用
- [x] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 
- [x] **分割线空行**：分割线附近空行符合规范

- [x] **换行问题**：长行代码换行注意对齐格式

- [x] **文件结尾**：代码文末增加固定行数空行

### 🧪 测试验证 
- [x] **函数测试**：测试包含所有函数分支

### 📚 文档更新
- [x] **类图**：类图符合规范
- [x] **流程图**：流程图符合规范
- [x] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见
- [x] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 HalUi模块检查清单（Closed）

### 🔤 命名规范
- [x] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [x] **int类型转换**：将int类型转换为U8、U16或U32类型
- [x] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [x] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [x] **宏定义命名**：宏定义命名增加_D后缀
- [x] **枚举命名**：枚举成员命名增加_E后缀
- [x] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范
- [x] **文件头注释**：文件头注释符合规范
- [x] **分割线注释**：分割线注释符合规范  
- [x] **函数头注释**：函数头注释符合规范
- [x] **英文注释**：注释为英文
- [x] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构
- [x] **防卫型声明**：防卫型声明符合规范
- [x] **static关键字**：去除函数定义处的static关键字
- [x] **常量返回值**：避免函数返回恒定值的情况
- [x] **代码换行**：代码中换行正确处理
- [x] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [x] **const关键字**：const关键字应加尽加
- [x] **头文件清理**：移除冗余的头文件引用
- [x] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 
- [x] **分割线空行**：分割线附近空行符合规范

- [x] **换行问题**：长行代码换行注意对齐格式

- [x] **文件结尾**：代码文末增加固定行数空行

### 🧪 测试验证 
- [x] **函数测试**：测试包含所有函数分支

### 📚 文档更新
- [x] **类图**：类图符合规范
- [x] **流程图**：流程图符合规范
- [x] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见
- [x] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 HalWireComm模块检查清单（Closed）

### 🔤 命名规范
- [x] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [x] **int类型转换**：将int类型转换为U8、U16或U32类型
- [x] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [x] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [x] **宏定义命名**：宏定义命名增加_D后缀
- [x] **枚举命名**：枚举成员命名增加_E后缀
- [x] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范
- [x] **文件头注释**：文件头注释符合规范
- [x] **分割线注释**：分割线注释符合规范  
- [x] **函数头注释**：函数头注释符合规范
- [x] **英文注释**：注释为英文
- [x] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构
- [x] **防卫型声明**：防卫型声明符合规范
- [x] **static关键字**：去除函数定义处的static关键字
- [x] **常量返回值**：避免函数返回恒定值的情况
- [x] **代码换行**：代码中换行正确处理
- [x] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [x] **const关键字**：const关键字应加尽加
- [x] **头文件清理**：移除冗余的头文件引用
- [x] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 
- [x] **分割线空行**：分割线附近空行符合规范

- [x] **换行问题**：长行代码换行注意对齐格式

- [x] **文件结尾**：代码文末增加固定行数空行

### 🧪 测试验证 
- [x] **函数测试**：测试包含所有函数分支

### 📚 文档更新
- [x] **类图**：类图符合规范
- [x] **流程图**：流程图符合规范
- [x] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见
- [x] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 HalWirelessComm模块检查清单（Closed）

### 🔤 命名规范
- [x] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [x] **int类型转换**：将int类型转换为U8、U16或U32类型
- [x] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [x] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [x] **宏定义命名**：宏定义命名增加_D后缀
- [x] **枚举命名**：枚举成员命名增加_E后缀
- [x] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范
- [x] **文件头注释**：文件头注释符合规范
- [x] **分割线注释**：分割线注释符合规范  
- [x] **函数头注释**：函数头注释符合规范
- [x] **英文注释**：注释为英文
- [x] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构
- [x] **防卫型声明**：防卫型声明符合规范
- [x] **static关键字**：去除函数定义处的static关键字
- [x] **常量返回值**：避免函数返回恒定值的情况
- [x] **代码换行**：代码中换行正确处理
- [x] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [x] **const关键字**：const关键字应加尽加
- [x] **头文件清理**：移除冗余的头文件引用
- [x] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 
- [x] **分割线空行**：分割线附近空行符合规范

- [x] **换行问题**：长行代码换行注意对齐格式

- [x] **文件结尾**：代码文末增加固定行数空行

### 🧪 测试验证 
- [x] **函数测试**：测试包含所有函数分支

### 📚 文档更新
- [x] **类图**：类图符合规范
- [x] **流程图**：流程图符合规范
- [x] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见
- [x] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 KeyHandle模块检查清单（closed）

### 🔤 命名规范
- [x] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [x] **int类型转换**：将int类型转换为U8、U16或U32类型
- [x] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [x] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [x] **宏定义命名**：宏定义命名增加_D后缀
- [x] **枚举命名**：枚举成员命名增加_E后缀
- [x] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范
- [x] **文件头注释**：文件头注释符合规范
- [x] **分割线注释**：分割线注释符合规范  
- [x] **函数头注释**：函数头注释符合规范
- [x] **英文注释**：注释为英文
- [x] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构
- [x] **防卫型声明**：防卫型声明符合规范
- [x] **static关键字**：去除函数定义处的static关键字
- [x] **常量返回值**：避免函数返回恒定值的情况
- [x] **代码换行**：代码中换行正确处理
- [x] **判断条件**：代码中连续多个判断条件之间使用括号进行分隔
- [x] **const关键字**：const关键字应加尽加
- [x] **头文件清理**：移除冗余的头文件引用
- [x] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 
- [x] **分割线空行**：分割线附近空行符合规范

- [x] **换行问题**：长行代码换行注意对齐格式

- [x] **文件结尾**：代码文末增加固定行数空行

### 🧪 测试验证 
- [x] **函数测试**：测试包含所有函数分支

### 📚 文档更新
- [x] **类图**：类图符合规范
- [x] **流程图**：流程图符合规范
- [x] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见
- [x] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 HalLed模块检查清单

### 🔤 命名规范
- [ ] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [ ] **int类型转换**：将int类型转换为U8、U16或U32类型
- [ ] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [ ] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [ ] **宏定义命名**：宏定义命名增加_D后缀
- [ ] **枚举命名**：枚举成员命名增加_E后缀
- [ ] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范
- [ ] **文件头注释**：文件头注释符合规范
- [ ] **分割线注释**：分割线注释符合规范  
- [ ] **函数头注释**：函数头注释符合规范
- [ ] **英文注释**：注释为英文
- [ ] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构
- [ ] **防卫型声明**：防卫型声明符合规范
- [ ] **static关键字**：去除函数定义处的static关键字
- [ ] **常量返回值**：避免函数返回恒定值的情况
- [ ] **代码换行**：代码中换行正确处理
- [ ] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [ ] **const关键字**：const关键字应加尽加
- [ ] **头文件清理**：移除冗余的头文件引用
- [ ] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 
- [ ] **分割线空行**：分割线附近空行符合规范

- [ ] **换行问题**：长行代码换行注意对齐格式

- [ ] **文件结尾**：代码文末增加固定行数空行

### 🧪 测试验证 
- [ ] **函数测试**：测试包含所有函数分支

### 📚 文档更新
- [ ] **类图**：类图符合规范
- [ ] **流程图**：流程图符合规范
- [ ] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见
- [ ] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 HalFm模块检查清单

### 🔤 命名规范
- [ ] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [ ] **int类型转换**：将int类型转换为U8、U16或U32类型
- [ ] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [ ] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [ ] **宏定义命名**：宏定义命名增加_D后缀
- [ ] **枚举命名**：枚举成员命名增加_E后缀
- [ ] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范
- [ ] **文件头注释**：文件头注释符合规范
- [ ] **分割线注释**：分割线注释符合规范  
- [ ] **函数头注释**：函数头注释符合规范
- [ ] **英文注释**：注释为英文
- [ ] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构
- [ ] **防卫型声明**：防卫型声明符合规范
- [ ] **static关键字**：去除函数定义处的static关键字
- [ ] **常量返回值**：避免函数返回恒定值的情况
- [ ] **代码换行**：代码中换行正确处理
- [ ] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [ ] **const关键字**：const关键字应加尽加
- [ ] **头文件清理**：移除冗余的头文件引用
- [ ] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 
- [ ] **分割线空行**：分割线附近空行符合规范

- [ ] **换行问题**：长行代码换行注意对齐格式

- [ ] **文件结尾**：代码文末增加固定行数空行

### 📚 文档更新
- [ ] **类图**：类图符合规范
- [ ] **流程图**：流程图符合规范
- [ ] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见
- [ ] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 HalFlash模块检查清单

### 🔤 命名规范

- [ ] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [ ] **int类型转换**：将int类型转换为U8、U16或U32类型
- [ ] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [x] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [ ] **宏定义命名**：宏定义命名增加_D后缀
- [ ] **枚举命名**：枚举成员命名增加_E后缀
- [ ] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范

- [x] **文件头注释**：文件头注释符合规范
- [ ] **分割线注释**：分割线注释符合规范  
- [x] **函数头注释**：函数头注释符合规范
- [x] **英文注释**：注释为英文
- [ ] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构

- [ ] **防卫型声明**：防卫型声明符合规范
- [ ] **static关键字**：去除函数定义处的static关键字
- [x] **常量返回值**：避免函数返回恒定值的情况
- [ ] **代码换行**：代码中换行正确处理
- [x] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [x] **const关键字**：const关键字应加尽加
- [x] **头文件清理**：移除冗余的头文件引用
- [ ] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 

- [ ] **分割线空行**：分割线附近空行符合规范

- [ ] **换行问题**：长行代码换行注意对齐格式

- [ ] **文件结尾**：代码文末增加固定行数空行

### 📚 文档更新

- [x] **类图**：类图符合规范
- [x] **流程图**：流程图符合规范
- [x] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见

- [ ] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭

---

---

## 📋 Event模块检查清单

### 🔤 命名规范

- [x] **数据类型转换**：bool、uint8_t、uint16_t、uint32_t类型转换为BOOL、U8、U16、U32类型
- [x] **int类型转换**：将int类型转换为U8、U16或U32类型
- [x] **布尔常量**：true、false转换为TRUE_D、FALSE_D 
- [x] **数字常量**: 整数型常量增加后缀U，浮点型常量增加后缀F
- [x] **宏定义命名**：宏定义命名增加_D后缀
- [x] **枚举命名**：枚举成员命名增加_E后缀
- [x] **NULL转换**：NULL转换为NULL_D

### 📝 注释规范

- [x] **文件头注释**：文件头注释符合规范
- [x] **分割线注释**：分割线注释符合规范  
- [x] **函数头注释**：函数头注释符合规范
- [x] **英文注释**：注释为英文
- [x] **函数声明注释**：去除static函数声明注释

### 🔧 代码结构

- [x] **防卫型声明**：防卫型声明符合规范
- [x] **static关键字**：去除函数定义处的static关键字
- [x] **常量返回值**：避免函数返回恒定值的情况
- [ ] **代码换行**：代码中换行正确处理
- [x] **判断条件**：代码中连续多个判断条件之前使用括号进行分隔
- [x] **const关键字**：const关键字应加尽加
- [x] **头文件清理**：移除冗余的头文件引用
- [x] **私有定义**：私有定义放置在源文件中

### 📐 格式规范 

- [x] **分割线空行**：分割线附近空行符合规范

- [ ] **换行问题**：长行代码换行注意对齐格式

- [x] **文件结尾**：代码文末增加固定行数空行

### 📚 文档更新

- [ ] **类图**：类图符合规范
- [ ] **流程图**：流程图符合规范
- [ ] **设计文档**：设计文档符合规范，缩进格式符合规范，图片排版显示清晰

### 📋 评审意见

- [ ] **评审问题关闭**：所有代码评审意见已处理完成，评审系统中相关问题已关闭