<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>6</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>234</x>
      <y>0</y>
      <w>318</w>
      <h>618</h>
    </coordinates>
    <panel_attributes>KeyHandle Init
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>336</x>
      <y>30</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>300</x>
      <y>60</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>36</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>330</x>
      <y>108</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>84</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>402</x>
      <y>492</y>
      <w>108</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return 
KEYHANDLE_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>348</x>
      <y>108</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>126</y>
      <w>66</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>300</x>
      <y>156</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Initialize HalKey</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>330</x>
      <y>204</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>180</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>348</x>
      <y>204</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[HalKey init failed]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>294</x>
      <y>252</y>
      <w>96</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Set config parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>222</y>
      <w>96</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[HalKey init success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>282</x>
      <y>300</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Initialize key mappings</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>276</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>276</x>
      <y>348</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Initialize combo mappings</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>324</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>300</x>
      <y>396</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Reset state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>372</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>294</x>
      <y>444</y>
      <w>96</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Set initialized flag</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>420</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>294</x>
      <y>492</y>
      <w>96</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return 
KEYHANDLE_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>468</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>336</x>
      <y>564</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>510</y>
      <w>18</w>
      <h>66</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>450</x>
      <y>114</y>
      <w>18</w>
      <h>390</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;630.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>606</x>
      <y>0</y>
      <w>270</w>
      <h>438</h>
    </coordinates>
    <panel_attributes>KeyHandle Run
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>702</x>
      <y>36</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>666</x>
      <y>66</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>42</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>696</x>
      <y>114</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>90</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>714</x>
      <y>114</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>666</x>
      <y>162</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Process scan</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>132</y>
      <w>72</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>660</x>
      <y>210</y>
      <w>96</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Process debounce</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>186</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>648</x>
      <y>258</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Detect single key events</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>234</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>648</x>
      <y>306</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Detect combo key events</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>282</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>702</x>
      <y>390</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>330</y>
      <w>18</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>924</x>
      <y>0</y>
      <w>258</w>
      <h>384</h>
    </coordinates>
    <panel_attributes>KeyHandle ProcessStateScan
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1056</x>
      <y>36</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>996</x>
      <y>66</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Save previous raw matrix</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1056</x>
      <y>42</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>984</x>
      <y>114</y>
      <w>156</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Save previous debounced matrix</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1056</x>
      <y>90</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1014</x>
      <y>162</y>
      <w>96</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Scan key matrix</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1056</x>
      <y>138</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1050</x>
      <y>210</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1056</x>
      <y>186</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>948</x>
      <y>252</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Reset state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>978</x>
      <y>210</y>
      <w>84</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Scan failed]</panel_attributes>
    <additional_attributes>20.0;70.0;20.0;20.0;120.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1056</x>
      <y>342</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1056</x>
      <y>228</y>
      <w>72</w>
      <h>126</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Scan success]</panel_attributes>
    <additional_attributes>10.0;190.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>774</x>
      <y>1854</y>
      <w>306</w>
      <h>414</h>
    </coordinates>
    <panel_attributes>KeyHandle ResetState
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>894</x>
      <y>1890</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>858</x>
      <y>1920</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>894</x>
      <y>1896</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>888</x>
      <y>1968</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>894</x>
      <y>1944</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>834</x>
      <y>2016</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Clear key matrix state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>894</x>
      <y>1986</y>
      <w>66</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>834</x>
      <y>2070</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Clear single key state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>894</x>
      <y>2040</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>894</x>
      <y>2094</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>2124</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Clear combo state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>894</x>
      <y>2208</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>894</x>
      <y>2148</y>
      <w>18</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>894</x>
      <y>1974</y>
      <w>174</w>
      <h>216</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Null pointer]</panel_attributes>
    <additional_attributes>10.0;340.0;190.0;340.0;190.0;10.0;30.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>234</x>
      <y>2364</y>
      <w>234</w>
      <h>210</h>
    </coordinates>
    <panel_attributes>KeyHandle TriggerSingleKeyShortPressEvent
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>342</x>
      <y>2400</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>288</x>
      <y>2430</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check single key type</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>342</x>
      <y>2406</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>342</x>
      <y>2454</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>282</x>
      <y>2484</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Publish event to system</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>342</x>
      <y>2538</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>342</x>
      <y>2508</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1002</x>
      <y>2364</y>
      <w>234</w>
      <h>240</h>
    </coordinates>
    <panel_attributes>KeyHandle GetTickMs
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1110</x>
      <y>2400</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1050</x>
      <y>2430</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Get FreeRTOS tick count</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1110</x>
      <y>2406</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1038</x>
      <y>2478</y>
      <w>156</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Convert to milliseconds
ticks * portTICK_PERIOD_MS</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1110</x>
      <y>2454</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1074</x>
      <y>2526</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Return time in ms</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1110</x>
      <y>2502</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1110</x>
      <y>2580</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1110</x>
      <y>2550</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>336</x>
      <y>510</y>
      <w>132</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;200.0;50.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>702</x>
      <y>120</y>
      <w>132</w>
      <h>252</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;400.0;200.0;400.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>984</x>
      <y>276</y>
      <w>90</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>130.0;50.0;10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>378</x>
      <y>696</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>330</x>
      <y>726</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Get current time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>702</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>288</x>
      <y>774</y>
      <w>192</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>Calculate key states
Currently pressed、Just pressed、Just released</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>750</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>324</x>
      <y>840</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Single Key Loop
i = 0 to MAX_KEYS</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>810</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>948</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>864</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>324</x>
      <y>996</y>
      <w>120</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>Record press time
Clear long press flags
Clear last long press time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>966</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Key just pressed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>948</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Key not just pressed]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>846</y>
      <w>234</w>
      <h>246</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next key]</panel_attributes>
    <additional_attributes>110.0;10.0;310.0;10.0;310.0;390.0;10.0;390.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>324</x>
      <y>1104</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Single Key Loop
i = 0 to MAX_KEYS</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1032</y>
      <w>18</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;120.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>1206</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1128</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>1308</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1224</y>
      <w>66</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Key pressed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>1410</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1326</y>
      <w>90</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not part of combo]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>1458</y>
      <w>144</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>Trigger first long press
Mark as processed
Record trigger time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>330</x>
      <y>1626</y>
      <w>108</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>Trigger repeat long press
Update trigger time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1428</y>
      <w>78</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[First long press]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1494</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1110</y>
      <w>234</w>
      <h>618</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next key]</panel_attributes>
    <additional_attributes>110.0;10.0;310.0;10.0;310.0;1010.0;10.0;1010.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>324</x>
      <y>1746</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Single Key Loop
i = 0 to MAX_KEYS</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1662</y>
      <w>18</w>
      <h>96</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;140.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>1854</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1770</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>1956</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1872</y>
      <w>72</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Key released]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>2058</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1974</y>
      <w>66</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Short press]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>2112</y>
      <w>144</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Trigger short press event</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>2076</y>
      <w>90</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not part of combo]</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>2196</y>
      <w>144</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Clear flags and times</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>2136</y>
      <w>18</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1752</y>
      <w>234</w>
      <h>522</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next key]</panel_attributes>
    <additional_attributes>110.0;10.0;310.0;10.0;310.0;850.0;10.0;850.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>378</x>
      <y>2292</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>2220</y>
      <w>18</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;120.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>1206</y>
      <w>138</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Key not pressed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>1308</y>
      <w>138</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Part of combo]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>1410</y>
      <w>138</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[No long press condition]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>1854</y>
      <w>162</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Key not released]</panel_attributes>
    <additional_attributes>250.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>1956</y>
      <w>144</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Long press]</panel_attributes>
    <additional_attributes>220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>2058</y>
      <w>126</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Part of combo]</panel_attributes>
    <additional_attributes>190.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1212</y>
      <w>150</w>
      <h>492</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;800.0;230.0;800.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>234</x>
      <y>672</y>
      <w>432</w>
      <h>1662</h>
    </coordinates>
    <panel_attributes>KeyHandle ProcessStateDetectSingle
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>954</y>
      <w>132</w>
      <h>120</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;180.0;200.0;180.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>894</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if key is pressed</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>918</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>1152</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if key is pressed</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1176</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>1254</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if single key is part of combo</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1278</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>1356</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check long press condition</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1380</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>1524</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check repeat press condition</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1548</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>372</x>
      <y>1578</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>390</x>
      <y>1578</y>
      <w>138</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[No reapt press condition]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1596</y>
      <w>90</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Repeat long press]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>1800</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if key is released</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1824</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>1902</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if key is short press</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1926</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>2004</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if single key is part of combo</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>2028</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1860</y>
      <w>174</w>
      <h>396</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;640.0;270.0;640.0;270.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>1962</y>
      <w>156</w>
      <h>222</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;350.0;240.0;350.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>378</x>
      <y>2064</y>
      <w>138</w>
      <h>108</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;160.0;210.0;160.0;210.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>822</x>
      <y>708</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>738</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Initialize current keys array
Clear all entries</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>714</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>786</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Set current key count = 0</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>762</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>768</x>
      <y>834</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Single Key Loop
i = 0 to MAX_KEYS</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>810</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>882</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Get matrix key index
Get single key type</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>858</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>816</x>
      <y>978</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>906</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>1026</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Add key to array
Increment key count</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>996</y>
      <w>66</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Key pressed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>834</x>
      <y>978</y>
      <w>132</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Key not pressed]</panel_attributes>
    <additional_attributes>200.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>840</y>
      <w>234</w>
      <h>264</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next key]</panel_attributes>
    <additional_attributes>110.0;10.0;310.0;10.0;310.0;420.0;10.0;420.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>1116</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Combo Key Loop
i = 0 to MAX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1050</y>
      <w>18</w>
      <h>78</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;110.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>816</x>
      <y>1224</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1140</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>816</x>
      <y>1326</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1242</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Combo matches]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>1374</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Trigger combo event
Update active combo</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1344</y>
      <w>66</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[New combo]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>1446</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Break from loop</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1398</y>
      <w>18</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>834</x>
      <y>1326</y>
      <w>108</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Not new combo]</panel_attributes>
    <additional_attributes>160.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>834</x>
      <y>1224</y>
      <w>132</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[No combo match]</panel_attributes>
    <additional_attributes>200.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1122</y>
      <w>246</w>
      <h>402</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next combo]</panel_attributes>
    <additional_attributes>100.0;10.0;310.0;10.0;310.0;650.0;10.0;650.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>816</x>
      <y>1590</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1470</y>
      <w>18</w>
      <h>78</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;110.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>1638</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Clear active combo
Set to MAX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1608</y>
      <w>84</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[No keys pressed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>834</x>
      <y>1590</y>
      <w>186</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Keys still pressed]</panel_attributes>
    <additional_attributes>290.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>822</x>
      <y>1710</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1662</y>
      <w>18</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1230</y>
      <w>144</w>
      <h>276</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;440.0;220.0;440.0;220.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>720</x>
      <y>672</y>
      <w>396</w>
      <h>1092</h>
    </coordinates>
    <panel_attributes>KeyHandle ProcessStateDetectCombo
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>774</x>
      <y>930</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if key is pressed</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>954</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>984</y>
      <w>144</w>
      <h>102</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;150.0;220.0;150.0;220.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>756</x>
      <y>1170</y>
      <w>144</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if combination key matche</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1194</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>756</x>
      <y>1272</y>
      <w>144</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if combination key is new</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1296</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>756</x>
      <y>1536</y>
      <w>144</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if no keys are pressed</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1560</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1332</y>
      <w>120</w>
      <h>102</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;150.0;180.0;150.0;180.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>822</x>
      <y>1596</y>
      <w>198</w>
      <h>102</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;150.0;310.0;150.0;310.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>342</x>
      <y>2508</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>498</x>
      <y>2364</y>
      <w>216</w>
      <h>210</h>
    </coordinates>
    <panel_attributes>KeyHandle TriggerSingleKeyLongPressEvent
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>600</x>
      <y>2400</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>546</x>
      <y>2430</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check single key type</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>600</x>
      <y>2406</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>600</x>
      <y>2454</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>540</x>
      <y>2484</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Publish event to system</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>600</x>
      <y>2538</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>600</x>
      <y>2508</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>600</x>
      <y>2508</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>756</x>
      <y>2364</y>
      <w>228</w>
      <h>210</h>
    </coordinates>
    <panel_attributes>KeyHandle TriggerComboKeyShortPressEvent
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>864</x>
      <y>2400</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>810</x>
      <y>2430</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check single key type</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>864</x>
      <y>2406</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>864</x>
      <y>2454</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>804</x>
      <y>2484</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Publish event to system</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>864</x>
      <y>2538</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>864</x>
      <y>2508</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>864</x>
      <y>2508</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>354</x>
      <y>2676</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>2706</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Get current time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2682</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>264</x>
      <y>2760</y>
      <w>192</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>Calculate change bitmap</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2730</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>300</x>
      <y>2820</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Single Key Loop
i = 0 to MAX_KEYS</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2790</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>348</x>
      <y>2928</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2844</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>300</x>
      <y>2976</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Restart debounce timing</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2946</y>
      <w>90</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Key state changed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>366</x>
      <y>2928</y>
      <w>138</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Key state not changed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2826</y>
      <w>234</w>
      <h>408</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next key]</panel_attributes>
    <additional_attributes>110.0;10.0;310.0;10.0;310.0;660.0;10.0;660.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>3000</y>
      <w>18</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>348</x>
      <y>3102</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>3120</y>
      <w>108</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Debounce time elapsed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>3174</y>
      <w>18</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;120.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>366</x>
      <y>3102</y>
      <w>138</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Debounce time not elapsed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>234</x>
      <y>2652</y>
      <w>378</w>
      <h>630</h>
    </coordinates>
    <panel_attributes>KeyHandle ProcessStateDebounce
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2934</y>
      <w>150</w>
      <h>102</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;150.0;230.0;150.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>282</x>
      <y>2874</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if key state changed</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>2898</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>3072</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>282</x>
      <y>3150</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Set the key state after debouncing</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>282</x>
      <y>3048</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if debounce time has elapsed</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>3108</y>
      <w>150</w>
      <h>108</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;160.0;230.0;160.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>354</x>
      <y>3246</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>744</x>
      <y>2688</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>708</x>
      <y>2718</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>2694</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>738</x>
      <y>2766</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>2742</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>816</x>
      <y>2904</y>
      <w>96</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return 
FALSE_D</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>858</x>
      <y>2772</y>
      <w>18</w>
      <h>144</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;220.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>672</x>
      <y>2814</y>
      <w>174</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Check if key is in the current active key list</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>2784</y>
      <w>72</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>702</x>
      <y>2904</y>
      <w>96</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return 
TRUE_D</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>2832</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>744</x>
      <y>2976</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>2922</y>
      <w>18</w>
      <h>66</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>654</x>
      <y>2652</y>
      <w>294</w>
      <h>360</h>
    </coordinates>
    <panel_attributes>KeyHandle IsComboMatch</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>756</x>
      <y>2766</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>2922</y>
      <w>132</w>
      <h>42</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;200.0;50.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>756</x>
      <y>2856</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Not found]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>738</x>
      <y>2856</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>744</x>
      <y>2874</y>
      <w>48</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Found]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1074</x>
      <y>2682</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1020</x>
      <y>2712</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if no active combo key</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1074</x>
      <y>2688</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1068</x>
      <y>2760</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1074</x>
      <y>2736</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1146</x>
      <y>2898</y>
      <w>96</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return 
FALSE_D</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1188</x>
      <y>2766</y>
      <w>18</w>
      <h>144</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;220.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1002</x>
      <y>2808</y>
      <w>174</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Check if key is part of active combo key</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1074</x>
      <y>2778</y>
      <w>72</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>1032</x>
      <y>2898</y>
      <w>96</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return 
TRUE_D</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1074</x>
      <y>2826</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1074</x>
      <y>2970</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1074</x>
      <y>2916</y>
      <w>18</w>
      <h>66</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>984</x>
      <y>2646</y>
      <w>294</w>
      <h>360</h>
    </coordinates>
    <panel_attributes>KeyHandle IsKeyPartOfActiveCombo</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1086</x>
      <y>2760</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[No active combo key]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1074</x>
      <y>2916</y>
      <w>132</w>
      <h>42</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;200.0;50.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1086</x>
      <y>2850</y>
      <w>120</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Not found]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>1068</x>
      <y>2850</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1074</x>
      <y>2868</y>
      <w>48</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Found]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>750</x>
      <y>3072</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>690</x>
      <y>3102</y>
      <w>138</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check CurrentTime &gt;= StartTime</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>3078</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>744</x>
      <y>3150</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>3126</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>702</x>
      <y>3192</y>
      <w>114</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Normal case calculation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>3168</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Yes]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>834</x>
      <y>3192</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Overflow case calculation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>762</x>
      <y>3150</y>
      <w>144</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[No]</panel_attributes>
    <additional_attributes>220.0;70.0;220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>702</x>
      <y>3270</y>
      <w>108</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return ElapsedTime</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>3216</y>
      <w>18</w>
      <h>66</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>750</x>
      <y>3318</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>3288</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>654</x>
      <y>3036</y>
      <w>318</w>
      <h>312</h>
    </coordinates>
    <panel_attributes>KeyHandle GetElapsedTime
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>750</x>
      <y>3216</y>
      <w>156</w>
      <h>42</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;240.0;50.0;240.0;10.0</additional_attributes>
  </element>
</diagram>
