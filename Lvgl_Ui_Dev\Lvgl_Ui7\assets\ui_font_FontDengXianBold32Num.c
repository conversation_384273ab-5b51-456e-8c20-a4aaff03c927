/*******************************************************************************
 * Size: 32 px
 * Bpp: 4
 * Opts: --bpp 4 --size 32 --font F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/LVGL_UI/STM32CubeIDE_FreeRTOS_LVGL_UI9/assets/Deng.ttf -o F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/LVGL_UI/STM32CubeIDE_FreeRTOS_LVGL_UI9/assets\ui_font_FontDengXianBold32Num.c --format lvgl -r 0x20-0x7f --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_FONTDENGXIANBOLD32NUM
#define UI_FONT_FONTDENGXIANBOLD32NUM 1
#endif

#if UI_FONT_FONTDENGXIANBOLD32NUM

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x8f, 0xa8, 0xfa, 0x8f, 0xa7, 0xf9, 0x7f, 0x97,
    0xf9, 0x6f, 0x86, 0xf8, 0x6f, 0x85, 0xf8, 0x5f,
    0x75, 0xf7, 0x4f, 0x74, 0xf6, 0x4f, 0x63, 0xf6,
    0x1, 0x0, 0x0, 0x0, 0x5, 0x95, 0x9f, 0xa9,
    0xfa,

    /* U+0022 "\"" */
    0xef, 0x30, 0x9, 0xf7, 0xdf, 0x20, 0x8, 0xf6,
    0xcf, 0x10, 0x8, 0xf6, 0xbf, 0x0, 0x7, 0xf5,
    0xaf, 0x0, 0x6, 0xf4, 0x8c, 0x0, 0x4, 0xd3,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0xd, 0x80, 0x0, 0x0, 0x5f,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf4, 0x0, 0x0,
    0x9, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x0,
    0x0, 0x0, 0xd8, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xc0, 0x0, 0x0, 0x1f, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xc9, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0x8d, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x25, 0x55, 0xae, 0x55, 0x55,
    0x55, 0xf9, 0x55, 0x50, 0x0, 0x0, 0xa, 0xb0,
    0x0, 0x0, 0x2f, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0x0, 0x0, 0x5, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0, 0x8c, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf1, 0x0, 0x0, 0xc, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0,
    0xf6, 0x0, 0x0, 0x0, 0x11, 0x1a, 0xc1, 0x11,
    0x11, 0x3f, 0x41, 0x11, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x44,
    0x5f, 0x74, 0x44, 0x44, 0xbd, 0x44, 0x44, 0x0,
    0x0, 0x4, 0xf1, 0x0, 0x0, 0xc, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xb0, 0x0, 0x0,
    0x3f, 0x20, 0x0, 0x0, 0x0, 0x0, 0xd7, 0x0,
    0x0, 0x6, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x40, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xf1, 0x0, 0x0, 0xd, 0x80, 0x0, 0x0,
    0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x18, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xae, 0xff, 0xfe, 0xb4, 0x0, 0x0, 0x7,
    0xff, 0xdb, 0xfc, 0xdf, 0xf9, 0x0, 0x4, 0xfe,
    0x30, 0x2f, 0x30, 0x4e, 0xf5, 0x0, 0xbf, 0x50,
    0x2, 0xf3, 0x0, 0x5f, 0xd0, 0xf, 0xf0, 0x0,
    0x2f, 0x30, 0x0, 0xef, 0x20, 0xff, 0x0, 0x2,
    0xf3, 0x0, 0x1, 0x0, 0xd, 0xf1, 0x0, 0x2f,
    0x30, 0x0, 0x0, 0x0, 0x9f, 0x90, 0x2, 0xf3,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x91, 0x2f, 0x30,
    0x0, 0x0, 0x0, 0x4, 0xef, 0xfb, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x8e, 0xff, 0xfd, 0x71,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfe, 0xff, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x32, 0x9f, 0xf5,
    0x0, 0x0, 0x0, 0x2, 0xf3, 0x0, 0x6f, 0xe0,
    0x0, 0x0, 0x0, 0x2f, 0x30, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0x2, 0xf3, 0x0, 0x8, 0xf6, 0x5a,
    0x20, 0x0, 0x2f, 0x30, 0x0, 0x7f, 0x79, 0xf7,
    0x0, 0x2, 0xf3, 0x0, 0x9, 0xf6, 0x3f, 0xe0,
    0x0, 0x2f, 0x30, 0x0, 0xef, 0x10, 0xaf, 0xc1,
    0x2, 0xf3, 0x1, 0xbf, 0x90, 0x0, 0xbf, 0xfb,
    0xaf, 0xbb, 0xff, 0xa0, 0x0, 0x0, 0x5b, 0xef,
    0xff, 0xeb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0x10,
    0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x9f, 0xc1, 0x0, 0x0, 0x8, 0xd0, 0x0,
    0x5, 0xe3, 0xa9, 0x0, 0x0, 0xe, 0x70, 0x0,
    0xb, 0x70, 0x3f, 0x0, 0x0, 0x5f, 0x10, 0x0,
    0xf, 0x40, 0xf, 0x30, 0x0, 0xba, 0x0, 0x0,
    0x1f, 0x30, 0xf, 0x60, 0x2, 0xf4, 0x0, 0x0,
    0x3f, 0x20, 0xe, 0x70, 0x8, 0xd0, 0x0, 0x0,
    0x3f, 0x20, 0xe, 0x70, 0xe, 0x70, 0x0, 0x0,
    0x3f, 0x20, 0xe, 0x80, 0x5f, 0x10, 0x0, 0x0,
    0x3f, 0x20, 0xe, 0x70, 0xba, 0x1d, 0xfa, 0x0,
    0x2f, 0x30, 0xf, 0x62, 0xf4, 0xab, 0x5e, 0x60,
    0xf, 0x40, 0xf, 0x48, 0xd1, 0xf3, 0x8, 0xb0,
    0xd, 0x70, 0x3f, 0x1e, 0x74, 0xf0, 0x5, 0xf0,
    0x7, 0xd2, 0xab, 0x5f, 0x16, 0xe0, 0x3, 0xf1,
    0x0, 0xdf, 0xe2, 0xba, 0x7, 0xe0, 0x2, 0xf3,
    0x0, 0x3, 0x12, 0xf4, 0x8, 0xd0, 0x2, 0xf3,
    0x0, 0x0, 0x8, 0xd0, 0x8, 0xd0, 0x2, 0xf3,
    0x0, 0x0, 0xe, 0x70, 0x7, 0xe0, 0x3, 0xf2,
    0x0, 0x0, 0x5f, 0x10, 0x6, 0xe0, 0x4, 0xf1,
    0x0, 0x0, 0xba, 0x0, 0x4, 0xf0, 0x5, 0xf0,
    0x0, 0x1, 0xf4, 0x0, 0x1, 0xf3, 0x9, 0xb0,
    0x0, 0x8, 0xd0, 0x0, 0x0, 0xab, 0x3e, 0x50,
    0x0, 0xe, 0x70, 0x0, 0x0, 0x1c, 0xf8, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x29, 0xdf, 0xfc, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xfb,
    0x89, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf4, 0x0, 0x0, 0xbf, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf9, 0x0, 0x0,
    0x3, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x60, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0, 0x3,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xb0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x9f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0x6, 0xef, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xfa, 0x20, 0x0, 0x0,
    0x2, 0x41, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x40, 0x0, 0x2,
    0xdf, 0xb2, 0x1d, 0xf5, 0x0, 0x0, 0x0, 0xe,
    0xf0, 0x0, 0x2, 0xef, 0x50, 0x0, 0x2e, 0xf4,
    0x0, 0x0, 0x4, 0xfa, 0x0, 0x0, 0xbf, 0x70,
    0x0, 0x0, 0x3f, 0xf3, 0x0, 0x0, 0xaf, 0x40,
    0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xe2,
    0x0, 0x3f, 0xd0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe2, 0xc, 0xf4, 0x0, 0x0,
    0x4f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xe9,
    0xfa, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0,
    0x5d, 0xfa, 0x8f, 0xf8, 0x10, 0x0, 0x0, 0x4e,
    0xfe, 0xa8, 0x9b, 0xff, 0xe6, 0x0, 0x5f, 0xff,
    0xdf, 0x10, 0x0, 0x17, 0xce, 0xfe, 0xda, 0x50,
    0x0, 0x0, 0x19, 0xef, 0xd0,

    /* U+0027 "'" */
    0x8f, 0x97, 0xf8, 0x6f, 0x75, 0xf6, 0x4f, 0x63,
    0xd4,

    /* U+0028 "(" */
    0x0, 0x0, 0xa, 0xf3, 0x0, 0x0, 0x5f, 0x80,
    0x0, 0x1, 0xed, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x2f, 0xb0, 0x0, 0x0, 0x9f, 0x30, 0x0,
    0x1, 0xfc, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0xe, 0xe0, 0x0, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0x3f, 0x90, 0x0, 0x0,
    0x5f, 0x70, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x6f, 0x60, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x5f, 0x70, 0x0, 0x0, 0x3f, 0x90, 0x0, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0xe, 0xe0, 0x0, 0x0,
    0xa, 0xf2, 0x0, 0x0, 0x6, 0xf7, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0x9f, 0x30, 0x0,
    0x0, 0x2f, 0xb0, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0x1, 0xed, 0x0, 0x0, 0x0, 0x5f, 0x80,
    0x0, 0x0, 0xa, 0xf3,

    /* U+0029 ")" */
    0xd, 0xe1, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x0,
    0x0, 0x8f, 0x40, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0x0, 0x6, 0xf7, 0x0, 0x0, 0x0, 0xee, 0x0,
    0x0, 0x0, 0x7f, 0x50, 0x0, 0x0, 0x1f, 0xb0,
    0x0, 0x0, 0xc, 0xf0, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x0, 0x6, 0xf6, 0x0, 0x0, 0x4, 0xf8,
    0x0, 0x0, 0x2, 0xfa, 0x0, 0x0, 0x1, 0xfb,
    0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x1, 0xfb,
    0x0, 0x0, 0x2, 0xfa, 0x0, 0x0, 0x3, 0xf8,
    0x0, 0x0, 0x5, 0xf6, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x0, 0xc, 0xf0, 0x0, 0x0, 0x1f, 0xb0,
    0x0, 0x0, 0x7f, 0x50, 0x0, 0x0, 0xee, 0x0,
    0x0, 0x6, 0xf6, 0x0, 0x0, 0xe, 0xd0, 0x0,
    0x0, 0x8f, 0x40, 0x0, 0x3, 0xfa, 0x0, 0x0,
    0xd, 0xe1, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x3, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xf5, 0x0, 0x0, 0x0, 0x9a, 0x40, 0x1f, 0x40,
    0x39, 0xb0, 0xb, 0xff, 0xe8, 0xf9, 0xdf, 0xfc,
    0x0, 0x0, 0x48, 0xdf, 0xe9, 0x40, 0x0, 0x0,
    0x0, 0x1e, 0xdf, 0x20, 0x0, 0x0, 0x0, 0xb,
    0xe1, 0xcd, 0x0, 0x0, 0x0, 0x8, 0xf4, 0x3,
    0xf9, 0x0, 0x0, 0x2, 0xfa, 0x0, 0x9, 0xf3,
    0x0, 0x0, 0x3, 0x10, 0x0, 0x14, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x8a, 0xaa, 0xaa, 0xbf, 0xda, 0xaa, 0xaa, 0xa1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x6b, 0x79, 0xfa, 0x9f, 0xa0, 0x99, 0xc, 0x72,
    0xf2, 0x46, 0x0,

    /* U+002D "-" */
    0x6c, 0xcc, 0xcc, 0xcc, 0xc6, 0x8f, 0xff, 0xff,
    0xff, 0xf8,

    /* U+002E "." */
    0x7b, 0x7a, 0xfa, 0xaf, 0xa0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0x5, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x30, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0,
    0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x5b, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0xaf, 0xfc, 0xac, 0xff, 0xb0, 0x0, 0x0,
    0x9f, 0xc2, 0x0, 0x1, 0xbf, 0x90, 0x0, 0x2f,
    0xe1, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x9, 0xf6,
    0x0, 0x0, 0x0, 0x6, 0xf9, 0x0, 0xef, 0x10,
    0x0, 0x0, 0x0, 0x1f, 0xe0, 0x1f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x24, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf4, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x66, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf7, 0x7f, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x87, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf8, 0x6f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x75, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf6, 0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x41, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf1,
    0xd, 0xf2, 0x0, 0x0, 0x0, 0x2, 0xfd, 0x0,
    0x8f, 0x70, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x1,
    0xfe, 0x10, 0x0, 0x0, 0x1f, 0xf1, 0x0, 0x8,
    0xfd, 0x20, 0x0, 0x2d, 0xf7, 0x0, 0x0, 0x9,
    0xff, 0xcb, 0xcf, 0xf9, 0x0, 0x0, 0x0, 0x4,
    0xbe, 0xfe, 0xa4, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x2c, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xf6,
    0xff, 0x0, 0x0, 0x0, 0xbf, 0xb1, 0xf, 0xf0,
    0x0, 0x0, 0xe, 0x60, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x10, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x6a, 0xaa, 0xaa, 0xff, 0xaa, 0xaa,
    0x89, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,

    /* U+0032 "2" */
    0x0, 0x0, 0x6c, 0xef, 0xec, 0x60, 0x0, 0x0,
    0x2, 0xdf, 0xfc, 0xbc, 0xff, 0xd2, 0x0, 0x0,
    0xdf, 0xb1, 0x0, 0x1, 0xbf, 0xd0, 0x0, 0x7f,
    0xc0, 0x0, 0x0, 0x0, 0xdf, 0x50, 0xd, 0xf5,
    0x0, 0x0, 0x0, 0x7, 0xfa, 0x0, 0x79, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1,

    /* U+0033 "3" */
    0x0, 0x2, 0x9d, 0xff, 0xeb, 0x60, 0x0, 0x0,
    0x6, 0xff, 0xec, 0xbc, 0xff, 0xd1, 0x0, 0x5,
    0xff, 0x50, 0x0, 0x2, 0xcf, 0xc0, 0x0, 0xdf,
    0x50, 0x0, 0x0, 0x0, 0xef, 0x30, 0x2f, 0xe0,
    0x0, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x17, 0xfe, 0x30, 0x0, 0x0, 0x0, 0xdd, 0xef,
    0xd8, 0x10, 0x0, 0x0, 0x0, 0xf, 0xff, 0xfd,
    0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8f,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x8f, 0x70, 0x0, 0x0, 0x0, 0x0, 0xff, 0x6,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0x1f,
    0xf4, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x0, 0x7f,
    0xf5, 0x0, 0x0, 0x1a, 0xfd, 0x0, 0x0, 0x8f,
    0xfe, 0xbb, 0xcf, 0xfd, 0x20, 0x0, 0x0, 0x39,
    0xdf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x7f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf6, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xc0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x20, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x6, 0xf7, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0xbf, 0x30, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x5, 0xf9, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x1e, 0xe0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0xaf, 0x40, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x4, 0xfa, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0xe, 0xe1, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x3a, 0xaa, 0xaa, 0xaa, 0xaa, 0xcf, 0xda, 0xa5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,

    /* U+0035 "5" */
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0xe, 0xfb, 0xbb, 0xbb, 0xbb, 0xbb, 0x10, 0x0,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf8, 0x3a, 0xdf,
    0xfd, 0x81, 0x0, 0x0, 0x6f, 0xdf, 0xfc, 0xbc,
    0xff, 0xe3, 0x0, 0x7, 0xff, 0x80, 0x0, 0x1,
    0xaf, 0xe1, 0x0, 0x24, 0x20, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf2,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xd0, 0xb,
    0xf8, 0x0, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x3f,
    0xf7, 0x0, 0x0, 0x1b, 0xfc, 0x0, 0x0, 0x5f,
    0xfe, 0xcb, 0xcf, 0xfc, 0x10, 0x0, 0x0, 0x29,
    0xdf, 0xfe, 0xb5, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x7, 0xce, 0xfd, 0xa3, 0x0, 0x0,
    0x0, 0x2d, 0xfe, 0xbb, 0xdf, 0xf7, 0x0, 0x0,
    0x1e, 0xf7, 0x0, 0x0, 0x5f, 0xf3, 0x0, 0x9,
    0xf7, 0x0, 0x0, 0x0, 0x7e, 0x60, 0x1, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x16,
    0x88, 0x62, 0x0, 0x0, 0xf, 0xe0, 0x7f, 0xff,
    0xff, 0xf9, 0x0, 0x1, 0xfd, 0x8f, 0x82, 0x1,
    0x6e, 0xfa, 0x0, 0x2f, 0xff, 0x40, 0x0, 0x0,
    0x1e, 0xf5, 0x2, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x5f, 0xc0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x1, 0xff, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf2, 0xf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x30, 0xdf, 0x20, 0x0, 0x0, 0x0, 0xd, 0xf2,
    0x9, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x4f, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xc0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0xc, 0xf6, 0x0, 0x4,
    0xff, 0x50, 0x0, 0x1a, 0xfc, 0x0, 0x0, 0x6,
    0xff, 0xda, 0xbf, 0xfd, 0x10, 0x0, 0x0, 0x2,
    0x9d, 0xfe, 0xc6, 0x0, 0x0,

    /* U+0037 "7" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x24,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x1, 0x8c, 0xff, 0xeb, 0x60, 0x0, 0x0,
    0x4, 0xef, 0xda, 0x9b, 0xef, 0xd2, 0x0, 0x2,
    0xff, 0x60, 0x0, 0x0, 0x8f, 0xd0, 0x0, 0x9f,
    0x80, 0x0, 0x0, 0x0, 0xbf, 0x60, 0xd, 0xf3,
    0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0xdf, 0x20,
    0x0, 0x0, 0x0, 0x5f, 0xb0, 0xb, 0xf3, 0x0,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0x6f, 0xa0, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0x9f, 0xa0, 0x0, 0x0, 0xaf, 0xea, 0x9b,
    0xee, 0x80, 0x0, 0x0, 0x4, 0xbf, 0xff, 0xff,
    0xa3, 0x0, 0x0, 0x9, 0xfc, 0x41, 0x1, 0x5d,
    0xf8, 0x0, 0x7, 0xfa, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x2f,
    0xe0, 0x4f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x25, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf3,
    0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x22,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0xd,
    0xf6, 0x0, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x4f,
    0xf5, 0x0, 0x0, 0x6, 0xff, 0x30, 0x0, 0x6f,
    0xfe, 0xa9, 0xae, 0xff, 0x40, 0x0, 0x0, 0x28,
    0xcf, 0xfe, 0xc8, 0x10, 0x0,

    /* U+0039 "9" */
    0x0, 0x1, 0x8c, 0xff, 0xd9, 0x20, 0x0, 0x0,
    0x4, 0xff, 0xeb, 0xbe, 0xff, 0x50, 0x0, 0x2,
    0xff, 0x70, 0x0, 0x5, 0xff, 0x30, 0x0, 0xbf,
    0x70, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0xb, 0xf3, 0x4, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x70, 0x5f, 0xa0, 0x0,
    0x0, 0x0, 0x3, 0xfb, 0x5, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0x3f, 0xd0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x0, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x9f, 0xf0, 0x8, 0xfb, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x0, 0xd, 0xfc, 0x40, 0x2, 0x8f,
    0x8f, 0xf0, 0x0, 0x1c, 0xff, 0xff, 0xff, 0x70,
    0xff, 0x0, 0x0, 0x3, 0x79, 0x86, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf4, 0x0,
    0x47, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0xd,
    0xf4, 0x0, 0x0, 0x0, 0xbf, 0x70, 0x0, 0x5f,
    0xe3, 0x0, 0x1, 0xaf, 0xc0, 0x0, 0x0, 0x8f,
    0xfd, 0xab, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x4b,
    0xef, 0xec, 0x60, 0x0, 0x0,

    /* U+003A ":" */
    0xaf, 0xaa, 0xfa, 0x69, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x96, 0xaf, 0xaa, 0xfa,

    /* U+003B ";" */
    0x9f, 0xa9, 0xfa, 0x59, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x96, 0x9f, 0xa9, 0xfa,
    0x9, 0x90, 0xc7, 0x2f, 0x24, 0x60,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x17, 0xef, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x29, 0xff, 0xd6, 0x0,
    0x0, 0x0, 0x0, 0x3a, 0xff, 0xc4, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xa3, 0x0, 0x0, 0x0,
    0x0, 0x6d, 0xff, 0x92, 0x0, 0x0, 0x0, 0x0,
    0x7e, 0xfe, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xe7, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xef, 0xf8, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xcf, 0xfa, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xbf, 0xfb, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x9f, 0xfd, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8e, 0xfe, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x51,

    /* U+003D "=" */
    0x8a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa1,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xbf, 0xfb, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x9f, 0xfd, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x8e, 0xfe, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0xff, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xf9, 0x20,
    0x0, 0x0, 0x0, 0x17, 0xef, 0xe7, 0x10, 0x0,
    0x0, 0x0, 0x29, 0xff, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xc4, 0x0, 0x0, 0x0, 0x0,
    0x4c, 0xff, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x1, 0x8d, 0xff, 0xda, 0x30, 0x0, 0x0,
    0x5f, 0xfe, 0xbb, 0xef, 0xf8, 0x0, 0x3, 0xff,
    0x60, 0x0, 0x5, 0xff, 0x50, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x6f, 0xd0, 0x2f, 0xe0, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x5e, 0x90, 0x0, 0x0, 0x0,
    0xd, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x92, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf5, 0x0,
    0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0xad, 0xef,
    0xfe, 0xb8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0xff, 0xeb, 0xa9, 0x9b, 0xdf,
    0xfc, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a,
    0xfe, 0x82, 0x0, 0x0, 0x0, 0x2, 0x8f, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xb0, 0x0,
    0x0, 0x0, 0x3e, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x1,
    0xef, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0xb, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x4f, 0xa0, 0x0, 0x0, 0x5,
    0xcf, 0xff, 0xd5, 0x0, 0xee, 0x0, 0xb, 0xf1,
    0x0, 0xcf, 0x10, 0x0, 0x0, 0xbf, 0xd7, 0x56,
    0xaf, 0x72, 0xfa, 0x0, 0x6, 0xf5, 0x3, 0xf9,
    0x0, 0x0, 0xc, 0xf8, 0x0, 0x0, 0x5, 0xf9,
    0xf6, 0x0, 0x3, 0xf8, 0x9, 0xf3, 0x0, 0x0,
    0x8f, 0x90, 0x0, 0x0, 0x0, 0x9f, 0xf3, 0x0,
    0x0, 0xfa, 0xd, 0xe0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x0, 0x0, 0xfb,
    0xf, 0xb0, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xb0, 0x0, 0x0, 0xfb, 0x3f, 0x80,
    0x0, 0xd, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x80, 0x0, 0x1, 0xf9, 0x4f, 0x70, 0x0, 0xf,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x40, 0x0,
    0x3, 0xf8, 0x5f, 0x60, 0x0, 0x2f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x7, 0xf4,
    0x5f, 0x70, 0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0,
    0x4, 0xfd, 0x0, 0x0, 0xb, 0xf0, 0x4f, 0x80,
    0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0xd, 0xfa,
    0x0, 0x0, 0x2f, 0xa0, 0x1f, 0xa0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0x8d, 0xf8, 0x0, 0x0,
    0xbf, 0x30, 0xd, 0xe0, 0x0, 0x9, 0xf9, 0x0,
    0x0, 0x7, 0xf3, 0xf8, 0x0, 0x7, 0xf9, 0x0,
    0x9, 0xf4, 0x0, 0x1, 0xef, 0xa3, 0x25, 0xcf,
    0x40, 0xed, 0x33, 0xaf, 0xb0, 0x0, 0x2, 0xfc,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xb2, 0x0, 0x5f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xaf, 0x60, 0x0,
    0x0, 0x24, 0x41, 0x0, 0x0, 0x1, 0x44, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xfd, 0x50, 0x0, 0x0, 0x0, 0x1, 0x5a,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0xff, 0xca, 0x99, 0xab, 0xef, 0xfe, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x59, 0xce,
    0xff, 0xed, 0xa7, 0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfe, 0x8f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf9, 0x2f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf3, 0xc, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x6,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x70, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x0, 0xaf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x4f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0,
    0xe, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0x80, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x20, 0x0, 0x0, 0x0, 0xbf,
    0x40, 0x0, 0x0, 0x4, 0xff, 0xbb, 0xbb, 0xbb,
    0xbb, 0xdf, 0xb0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf7, 0x0,
    0x0, 0x7f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfd, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x4, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0xa, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1,
    0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf6, 0x6f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfc,

    /* U+0042 "B" */
    0x1f, 0xff, 0xff, 0xfe, 0xc9, 0x40, 0x0, 0x1,
    0xff, 0xbb, 0xbb, 0xcf, 0xff, 0xc1, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x5, 0xef, 0xc0, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x40, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0xb, 0xf7, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x80, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0xc, 0xf5, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x3, 0xfe, 0x0, 0x1f, 0xf0, 0x0, 0x0,
    0x5, 0xff, 0x40, 0x1, 0xff, 0xaa, 0xaa, 0xcf,
    0xfa, 0x20, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xfe,
    0xb5, 0x0, 0x1, 0xff, 0x0, 0x0, 0x3, 0x7d,
    0xfc, 0x10, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x9,
    0xfd, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf6, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xa1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xb1,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf8, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x31, 0xff,
    0x0, 0x0, 0x0, 0x17, 0xff, 0x80, 0x1f, 0xfa,
    0xaa, 0xab, 0xdf, 0xff, 0x70, 0x1, 0xff, 0xff,
    0xff, 0xfe, 0xb7, 0x10, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x5a, 0xde, 0xfe, 0xc8, 0x10,
    0x0, 0x0, 0x0, 0x3c, 0xff, 0xfd, 0xde, 0xff,
    0xf7, 0x0, 0x0, 0x4, 0xff, 0xc4, 0x0, 0x0,
    0x28, 0xff, 0x90, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf4, 0x0, 0xbf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfa, 0x2, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x30, 0x8, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xa3, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfb, 0x0, 0x1e, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf2, 0x0, 0x4, 0xff, 0xc4, 0x0,
    0x0, 0x29, 0xff, 0x50, 0x0, 0x0, 0x3d, 0xff,
    0xfd, 0xce, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x5a, 0xdf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xff, 0xff, 0xed, 0x96, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xcc, 0xcc, 0xde, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x49, 0xff, 0xd2, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xd1, 0x0, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xa0, 0x1,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x20, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf9, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xd0, 0x1f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x21, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf2, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x11, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfb, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x60,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xf0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xf7, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x3e, 0xfb, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x3, 0xaf, 0xfc, 0x0, 0x0, 0x1f, 0xfc,
    0xcc, 0xcc, 0xdf, 0xff, 0xf7, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xdb, 0x61, 0x0, 0x0,
    0x0,

    /* U+0045 "E" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x51, 0xff,
    0xcc, 0xcc, 0xcc, 0xcc, 0xc4, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x1,
    0xff, 0xbb, 0xbb, 0xbb, 0xbb, 0x40, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xfc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,

    /* U+0046 "F" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x71, 0xff,
    0xcc, 0xcc, 0xcc, 0xcc, 0xc5, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x1,
    0xff, 0xbb, 0xbb, 0xbb, 0xbb, 0x90, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x5a, 0xde, 0xfe, 0xc9, 0x30,
    0x0, 0x0, 0x0, 0x4, 0xdf, 0xff, 0xdd, 0xef,
    0xff, 0xb1, 0x0, 0x0, 0x6, 0xff, 0xb4, 0x0,
    0x0, 0x17, 0xef, 0xd1, 0x0, 0x3, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x2, 0xdf, 0xb0, 0x0, 0xdf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x10,
    0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xf7, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x4b,
    0xbb, 0xbb, 0xdf, 0x70, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf7, 0xd, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x9f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf7,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x70, 0xb, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf7, 0x0, 0x2f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x50, 0x0, 0x4f, 0xfd,
    0x50, 0x0, 0x0, 0x4b, 0xff, 0x60, 0x0, 0x0,
    0x3c, 0xff, 0xfd, 0xcd, 0xff, 0xfc, 0x30, 0x0,
    0x0, 0x0, 0x4, 0x9d, 0xef, 0xec, 0x83, 0x0,
    0x0,

    /* U+0048 "H" */
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0x1f, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdf, 0xf4, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x1f,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf4,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf4, 0x1f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf4,

    /* U+0049 "I" */
    0x1f, 0xf1, 0xff, 0x1f, 0xf1, 0xff, 0x1f, 0xf1,
    0xff, 0x1f, 0xf1, 0xff, 0x1f, 0xf1, 0xff, 0x1f,
    0xf1, 0xff, 0x1f, 0xf1, 0xff, 0x1f, 0xf1, 0xff,
    0x1f, 0xf1, 0xff, 0x1f, 0xf1, 0xff, 0x1f, 0xf1,
    0xff,

    /* U+004A "J" */
    0x0, 0xa, 0xff, 0xff, 0x90, 0x0, 0x7c, 0xce,
    0xf9, 0x0, 0x0, 0x0, 0x8f, 0x90, 0x0, 0x0,
    0x8, 0xf9, 0x0, 0x0, 0x0, 0x8f, 0x90, 0x0,
    0x0, 0x8, 0xf9, 0x0, 0x0, 0x0, 0x8f, 0x90,
    0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0, 0x8f,
    0x90, 0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0,
    0x8f, 0x90, 0x0, 0x0, 0x8, 0xf9, 0x0, 0x0,
    0x0, 0x8f, 0x90, 0x0, 0x0, 0x8, 0xf9, 0x0,
    0x0, 0x0, 0x8f, 0x90, 0x0, 0x0, 0x8, 0xf9,
    0x0, 0x0, 0x0, 0x9f, 0x80, 0x0, 0x0, 0xc,
    0xf5, 0x0, 0x0, 0x2, 0xff, 0x12, 0x0, 0x2,
    0xdf, 0x90, 0xaf, 0xdd, 0xff, 0xc0, 0x7, 0xef,
    0xfd, 0x70, 0x0,

    /* U+004B "K" */
    0xff, 0x10, 0x0, 0x0, 0x0, 0xb, 0xfc, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x10, 0xff,
    0x10, 0x0, 0x0, 0x7, 0xfe, 0x20, 0xf, 0xf1,
    0x0, 0x0, 0x5, 0xff, 0x30, 0x0, 0xff, 0x10,
    0x0, 0x4, 0xff, 0x50, 0x0, 0xf, 0xf1, 0x0,
    0x2, 0xef, 0x60, 0x0, 0x0, 0xff, 0x10, 0x1,
    0xdf, 0x80, 0x0, 0x0, 0xf, 0xf1, 0x0, 0xcf,
    0xa0, 0x0, 0x0, 0x0, 0xff, 0x10, 0xaf, 0xc0,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x9f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x8f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xc5, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xff, 0xc1, 0x9, 0xfe, 0x10, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0xd, 0xfa, 0x0, 0x0,
    0x0, 0xff, 0x10, 0x0, 0x2f, 0xf6, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x7f, 0xf2, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0xbf, 0xd0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x1, 0xef, 0x90, 0x0, 0xff,
    0x10, 0x0, 0x0, 0x4, 0xff, 0x40, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0x9, 0xfe, 0x10, 0xff, 0x10,
    0x0, 0x0, 0x0, 0xd, 0xfb, 0xf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf7,

    /* U+004C "L" */
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4,

    /* U+004D "M" */
    0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xc1, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0x1f,
    0xef, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xef, 0xc1, 0xfd, 0xbf, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf8, 0xfc, 0x1f, 0xd5,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x3f, 0xc1, 0xfd, 0xe, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x93, 0xfc, 0x1f, 0xe0, 0x7f,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x3f,
    0xc1, 0xfe, 0x1, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfc, 0x3, 0xfc, 0x1f, 0xe0, 0xa, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x3f, 0xc1,
    0xfe, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0xf,
    0xe0, 0x3, 0xfc, 0x1f, 0xe0, 0x0, 0xdf, 0x30,
    0x0, 0x0, 0x6, 0xf8, 0x0, 0x3f, 0xc1, 0xfe,
    0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0xdf, 0x20,
    0x3, 0xfc, 0x1f, 0xe0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x3f, 0xb0, 0x0, 0x3f, 0xc1, 0xfe, 0x0,
    0x0, 0x9f, 0x70, 0x0, 0xa, 0xf5, 0x0, 0x3,
    0xfc, 0x1f, 0xe0, 0x0, 0x2, 0xfe, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0x3f, 0xc1, 0xfe, 0x0, 0x0,
    0xc, 0xf5, 0x0, 0x7f, 0x80, 0x0, 0x3, 0xfc,
    0x1f, 0xe0, 0x0, 0x0, 0x5f, 0xb0, 0xd, 0xf1,
    0x0, 0x0, 0x3f, 0xc1, 0xfe, 0x0, 0x0, 0x0,
    0xef, 0x13, 0xfb, 0x0, 0x0, 0x3, 0xfc, 0x1f,
    0xe0, 0x0, 0x0, 0x8, 0xf7, 0xaf, 0x40, 0x0,
    0x0, 0x3f, 0xc1, 0xfe, 0x0, 0x0, 0x0, 0x1f,
    0xdf, 0xe0, 0x0, 0x0, 0x3, 0xfc, 0x1f, 0xe0,
    0x0, 0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x0,
    0x3f, 0xc1, 0xfe, 0x0, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x0, 0x0, 0x3, 0xfc,

    /* U+004E "N" */
    0x1f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0x61, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf6, 0x1f, 0xef, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x61, 0xfc, 0x9f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf6, 0x1f, 0xc0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x61,
    0xfd, 0x3, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x9,
    0xf6, 0x1f, 0xd0, 0x9, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x61, 0xfd, 0x0, 0xd, 0xf6, 0x0,
    0x0, 0x0, 0x9, 0xf6, 0x1f, 0xd0, 0x0, 0x3f,
    0xf1, 0x0, 0x0, 0x0, 0x9f, 0x61, 0xfd, 0x0,
    0x0, 0x8f, 0xb0, 0x0, 0x0, 0x9, 0xf6, 0x1f,
    0xd0, 0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x9f,
    0x61, 0xfd, 0x0, 0x0, 0x3, 0xff, 0x20, 0x0,
    0x9, 0xf6, 0x1f, 0xd0, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x9f, 0x61, 0xfd, 0x0, 0x0, 0x0,
    0xd, 0xf6, 0x0, 0x9, 0xf6, 0x1f, 0xd0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x9f, 0x61, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xc0, 0x9, 0xf6,
    0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70,
    0x8f, 0x61, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0x27, 0xf6, 0x1f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfc, 0x7f, 0x61, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0xf6, 0x1f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x61,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf6,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x27, 0xbd, 0xff, 0xec, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xff, 0xfe,
    0xdd, 0xef, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xe7, 0x20, 0x0, 0x1, 0x6d, 0xff, 0x40,
    0x0, 0x0, 0x1e, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf3, 0x0, 0x0, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x60, 0xa, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0xe, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1,
    0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf4, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x4f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf7, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf7, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf4, 0xd, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf1, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x50, 0x0, 0xaf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfc, 0x0, 0x0, 0x1e,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2,
    0x0, 0x0, 0x2, 0xef, 0xf8, 0x20, 0x0, 0x1,
    0x5d, 0xfe, 0x30, 0x0, 0x0, 0x0, 0x19, 0xff,
    0xfe, 0xdc, 0xef, 0xff, 0xb1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xbe, 0xff, 0xec, 0x82, 0x0,
    0x0, 0x0,

    /* U+0050 "P" */
    0xff, 0xff, 0xff, 0xfd, 0xa5, 0x0, 0x0, 0xff,
    0xbb, 0xbb, 0xce, 0xff, 0xd2, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x3c, 0xfe, 0x10, 0xff, 0x10, 0x0,
    0x0, 0x0, 0xcf, 0xa0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x3f, 0xf0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xe, 0xf3, 0xff, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf4, 0xff, 0x10, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x3f, 0xe0, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xcf, 0x80, 0xff, 0x10,
    0x0, 0x0, 0x3c, 0xfc, 0x0, 0xff, 0xba, 0xaa,
    0xbd, 0xff, 0xb1, 0x0, 0xff, 0xff, 0xff, 0xfd,
    0x94, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x27, 0xbd, 0xff, 0xec, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xff, 0xfe,
    0xdd, 0xef, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xe7, 0x20, 0x0, 0x1, 0x6d, 0xff, 0x40,
    0x0, 0x0, 0x1e, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf3, 0x0, 0x0, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfd, 0x0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x60, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0xe, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf1,
    0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xf4, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xf7, 0x4f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf7, 0x4f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf7, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x1f,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf4, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf1, 0x9, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xc0,
    0x3, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x60, 0x0, 0xbf, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfd, 0x0, 0x0, 0x1e,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf3,
    0x0, 0x0, 0x3, 0xef, 0xe7, 0x10, 0x0, 0x0,
    0x4c, 0xff, 0x40, 0x0, 0x0, 0x0, 0x2b, 0xff,
    0xfd, 0xcb, 0xdf, 0xff, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x39, 0xcf, 0xff, 0xfc, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xbb, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x9e, 0xff, 0xa0, 0x0,

    /* U+0052 "R" */
    0x1f, 0xff, 0xff, 0xff, 0xec, 0x82, 0x0, 0x0,
    0x1f, 0xfb, 0xbb, 0xbb, 0xdf, 0xff, 0x80, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x1, 0x7f, 0xf8, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x20,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xb0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xb0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xa0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x50,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x9, 0xfd, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x15, 0xcf, 0xe2, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x10, 0x0,
    0x1f, 0xfa, 0xaa, 0xaf, 0xf7, 0x10, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x8, 0xfc, 0x0, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xf2, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x9, 0xfc, 0x0,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x70,
    0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf2,

    /* U+0053 "S" */
    0x0, 0x3, 0xad, 0xff, 0xeb, 0x50, 0x0, 0x0,
    0x8, 0xff, 0xec, 0xbd, 0xff, 0xc1, 0x0, 0x5,
    0xfe, 0x50, 0x0, 0x3, 0xdf, 0xb0, 0x0, 0xdf,
    0x50, 0x0, 0x0, 0x1, 0xef, 0x40, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x8, 0xe6, 0x1, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xe9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x9f, 0xff, 0xe9,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x5, 0xaf, 0xff,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x48, 0x30, 0x0, 0x0, 0x0, 0x0, 0xff, 0x8,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xe0, 0x2f,
    0xf2, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x0, 0x9f,
    0xe5, 0x0, 0x0, 0x19, 0xff, 0x20, 0x0, 0xaf,
    0xff, 0xcb, 0xdf, 0xfe, 0x30, 0x0, 0x0, 0x4a,
    0xdf, 0xfe, 0xb7, 0x10, 0x0,

    /* U+0054 "T" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x48, 0xcc, 0xcc, 0xcc, 0xff, 0xdc, 0xcc, 0xcc,
    0xc3, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x40,
    0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xc6, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfc, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xc6, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xfc, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xc6, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfc, 0x6f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xc6, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfc, 0x6f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xc6, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xfc, 0x6f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xc6, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfc, 0x6f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xc6, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfb, 0x5f,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xb4,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf9,
    0x1f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x60, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf1, 0x5, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1d,
    0xf9, 0x0, 0xa, 0xff, 0x60, 0x0, 0x0, 0x5e,
    0xfc, 0x0, 0x0, 0x9, 0xff, 0xfd, 0xcd, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x2, 0x9d, 0xef, 0xec,
    0x93, 0x0, 0x0,

    /* U+0056 "V" */
    0x7f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf1, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xa0, 0xb, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x40, 0x5, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x0,
    0x0, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf8, 0x0, 0x0, 0x9f, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf2, 0x0, 0x0, 0x3f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60, 0x0,
    0x0, 0x7, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x10, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0,
    0x7, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60,
    0x0, 0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xc0, 0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x8f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf7, 0x0, 0x0,
    0xef, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfd,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x30, 0x9, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x90, 0xf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x5f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf4, 0xaf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xf9, 0xee, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2,
    0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x5f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x91, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf4, 0xc, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xef, 0x50, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x0, 0x6, 0xf8, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xb0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0xb, 0xf6,
    0x0, 0xe, 0xf3, 0x0, 0x0, 0x0, 0xf, 0xd0,
    0xbf, 0x30, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0,
    0xaf, 0x70, 0x0, 0x0, 0x4, 0xf9, 0x6, 0xf7,
    0x0, 0x0, 0x0, 0x4f, 0xd0, 0x0, 0x6, 0xfc,
    0x0, 0x0, 0x0, 0x9f, 0x40, 0x2f, 0xc0, 0x0,
    0x0, 0x9, 0xf8, 0x0, 0x0, 0x1f, 0xf0, 0x0,
    0x0, 0xe, 0xf0, 0x0, 0xdf, 0x10, 0x0, 0x0,
    0xdf, 0x30, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x3,
    0xfb, 0x0, 0x9, 0xf6, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x8f, 0x60,
    0x0, 0x4f, 0xa0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x0, 0x4f, 0xd0, 0x0, 0xc, 0xf1, 0x0, 0x0,
    0xff, 0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x1, 0xfc, 0x0, 0x0, 0xa, 0xf4,
    0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0xb, 0xf6,
    0x0, 0x6f, 0x70, 0x0, 0x0, 0x6f, 0x90, 0x4,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xa0, 0xb,
    0xf3, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x8f, 0x70,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0xfe, 0x0,
    0x0, 0x0, 0xc, 0xf2, 0xd, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf2, 0x4f, 0x90, 0x0, 0x0,
    0x0, 0x8f, 0x61, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0x68, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xfa, 0x5f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfa, 0xcf, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe8,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xef,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xef, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf6, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0xd, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xf4, 0x0, 0x2f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf9, 0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0x0, 0x5, 0xfe, 0x0, 0x0, 0x0, 0xcf, 0x70,
    0x0, 0x0, 0x1, 0xef, 0x40, 0x0, 0x0, 0x2,
    0xff, 0x20, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0,
    0x0, 0x6, 0xfd, 0x0, 0x0, 0x5f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x1e, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xf3, 0xa,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xd5, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xc6, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xf2,
    0xb, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf7, 0x0, 0x1f, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfc, 0x0, 0x0, 0x6f, 0xd0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x20, 0x0, 0x0, 0xbf, 0x90,
    0x0, 0x0, 0x0, 0xcf, 0x70, 0x0, 0x0, 0x1,
    0xff, 0x40, 0x0, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf9, 0x0, 0xc, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf4, 0x7,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xe0,

    /* U+0059 "Y" */
    0x8f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf1, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x60, 0x5, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfd, 0x0, 0x0, 0xbf, 0x70, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xa0, 0x0, 0x0, 0x8, 0xfa,
    0x0, 0x0, 0x3, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xef, 0x30, 0x0, 0xc, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xc0, 0x0, 0x5f, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf5, 0x0, 0xdf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x7, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x9f, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xef, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfe, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xc6, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+005B "[" */
    0x1f, 0xff, 0xff, 0xf3, 0x1f, 0xe8, 0x88, 0x81,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x0, 0x1f, 0xe8, 0x88, 0x81,
    0x1f, 0xff, 0xff, 0xf3,

    /* U+005C "\\" */
    0x9f, 0x50, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x10, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf8,

    /* U+005D "]" */
    0x8f, 0xff, 0xff, 0xb4, 0x88, 0x89, 0xfb, 0x0,
    0x0, 0x3f, 0xb0, 0x0, 0x3, 0xfb, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x3, 0xfb, 0x0, 0x0, 0x3f,
    0xb0, 0x0, 0x3, 0xfb, 0x0, 0x0, 0x3f, 0xb0,
    0x0, 0x3, 0xfb, 0x0, 0x0, 0x3f, 0xb0, 0x0,
    0x3, 0xfb, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x3,
    0xfb, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x3, 0xfb,
    0x0, 0x0, 0x3f, 0xb0, 0x0, 0x3, 0xfb, 0x0,
    0x0, 0x3f, 0xb0, 0x0, 0x3, 0xfb, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x3, 0xfb, 0x0, 0x0, 0x3f,
    0xb0, 0x0, 0x3, 0xfb, 0x0, 0x0, 0x3f, 0xb0,
    0x0, 0x3, 0xfb, 0x0, 0x0, 0x3f, 0xb4, 0x88,
    0x89, 0xfb, 0x8f, 0xff, 0xff, 0xb0,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0x4, 0x86, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x6f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x9f, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf6, 0x2, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xce, 0x0, 0xa,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x70, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0xbf, 0x10, 0x0, 0x0, 0x2, 0xf8, 0x0,
    0x0, 0x4, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0xc, 0xe0, 0x0, 0x0, 0x2f, 0xa0,
    0x0, 0x0, 0x0, 0x5f, 0x60, 0x0, 0x9, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x1, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf5, 0x0, 0x8f,
    0x40, 0x0, 0x0, 0x0, 0x0, 0xe, 0xd0, 0xe,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x40,

    /* U+005F "_" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x13,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x10,

    /* U+0060 "`" */
    0x8f, 0xd1, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0,
    0x8f, 0x80, 0x0, 0x0, 0x7f, 0x50, 0x0, 0x0,
    0x6a, 0x0,

    /* U+0061 "a" */
    0x0, 0x6, 0xbe, 0xff, 0xd9, 0x20, 0x0, 0xc,
    0xff, 0xca, 0xbe, 0xff, 0x40, 0x7, 0xfc, 0x10,
    0x0, 0x7, 0xfe, 0x0, 0x48, 0x20, 0x0, 0x0,
    0xb, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x80, 0x0, 0x0, 0x1, 0x12, 0x27, 0xf9, 0x0,
    0x6, 0xbf, 0xff, 0xff, 0xff, 0x90, 0x2d, 0xfe,
    0x97, 0x55, 0x59, 0xfa, 0xd, 0xf9, 0x0, 0x0,
    0x0, 0x5f, 0xa5, 0xfd, 0x0, 0x0, 0x0, 0x6,
    0xfa, 0x8f, 0x80, 0x0, 0x0, 0x0, 0x8f, 0xa9,
    0xf8, 0x0, 0x0, 0x0, 0xd, 0xfa, 0x7f, 0xb0,
    0x0, 0x0, 0x6, 0xff, 0xa2, 0xff, 0x60, 0x0,
    0x7, 0xf7, 0xfa, 0x6, 0xff, 0xdb, 0xbe, 0xf5,
    0x4f, 0xa0, 0x4, 0xbe, 0xfe, 0xa2, 0x4, 0xfa,

    /* U+0062 "b" */
    0x6f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x1, 0x8d,
    0xef, 0xd9, 0x20, 0x0, 0x6f, 0x83, 0xef, 0xca,
    0xbe, 0xff, 0x70, 0x6, 0xf9, 0xeb, 0x10, 0x0,
    0x5, 0xff, 0x50, 0x6f, 0xfb, 0x0, 0x0, 0x0,
    0x5, 0xfe, 0x6, 0xff, 0x20, 0x0, 0x0, 0x0,
    0xc, 0xf5, 0x6f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x86, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfb, 0x6f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xc6, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfc,
    0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xb6,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x6f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x46, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x6f, 0xd0, 0x7f, 0x9e,
    0xa1, 0x0, 0x0, 0x6f, 0xf4, 0x7, 0xf7, 0x4f,
    0xfc, 0xab, 0xef, 0xf5, 0x0, 0x8f, 0x60, 0x29,
    0xdf, 0xfd, 0x81, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x7c, 0xff, 0xeb, 0x50, 0x0, 0x2,
    0xef, 0xeb, 0xad, 0xff, 0xa0, 0x1, 0xef, 0x70,
    0x0, 0x3, 0xff, 0x60, 0x8f, 0x90, 0x0, 0x0,
    0x7, 0xfc, 0xe, 0xf1, 0x0, 0x0, 0x0, 0x15,
    0x32, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x10, 0x0, 0x0, 0x1, 0x74, 0xa, 0xf9,
    0x0, 0x0, 0x0, 0x6f, 0xb0, 0x2f, 0xf7, 0x0,
    0x0, 0x4f, 0xf4, 0x0, 0x4f, 0xfe, 0xbb, 0xdf,
    0xf8, 0x0, 0x0, 0x18, 0xdf, 0xfe, 0xa3, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x6, 0xbe,
    0xfe, 0xb5, 0x3, 0xfc, 0x0, 0x1c, 0xff, 0xca,
    0xbe, 0xfa, 0x3f, 0xc0, 0xd, 0xfa, 0x10, 0x0,
    0x6, 0xfa, 0xfc, 0x7, 0xfb, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xc0, 0xef, 0x30, 0x0, 0x0, 0x0,
    0xc, 0xfc, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xc5, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xfc, 0x6f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xc6, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc,
    0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xc3,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfc, 0xe,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0x8f,
    0xb0, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0x1, 0xef,
    0x90, 0x0, 0x0, 0x6f, 0x7f, 0xc0, 0x2, 0xef,
    0xfb, 0xab, 0xef, 0x71, 0xfd, 0x0, 0x0, 0x7c,
    0xef, 0xea, 0x30, 0xf, 0xe0,

    /* U+0065 "e" */
    0x0, 0x0, 0x5b, 0xef, 0xeb, 0x60, 0x0, 0x0,
    0x1, 0xcf, 0xfb, 0xab, 0xff, 0xd2, 0x0, 0x0,
    0xdf, 0x80, 0x0, 0x0, 0x9f, 0xd0, 0x0, 0x7f,
    0x80, 0x0, 0x0, 0x0, 0xbf, 0x60, 0xe, 0xf1,
    0x0, 0x0, 0x0, 0x3, 0xfc, 0x2, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x5f, 0xd8, 0x88,
    0x88, 0x88, 0x88, 0xff, 0x26, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x6f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x4, 0x0, 0x8, 0xfc, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0xd, 0xfb, 0x10, 0x0, 0x7, 0xff,
    0x20, 0x0, 0x2d, 0xff, 0xca, 0xbe, 0xfe, 0x30,
    0x0, 0x0, 0x6, 0xbe, 0xfe, 0xc7, 0x10, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x7, 0xdf, 0xf8, 0x0, 0x0, 0x9f,
    0xfa, 0xa6, 0x0, 0x2, 0xff, 0x20, 0x0, 0x0,
    0x6, 0xfa, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x8,
    0xf7, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xe0,
    0x2a, 0xad, 0xfc, 0xaa, 0x90, 0x0, 0x8, 0xf7,
    0x0, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x8,
    0xf7, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0,
    0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xf7,
    0x0, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x8, 0xf7, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x8,
    0xf7, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x7c, 0xef, 0xda, 0x30, 0xf, 0xe0,
    0x2, 0xdf, 0xfc, 0xab, 0xef, 0x71, 0xfd, 0x0,
    0xdf, 0xb1, 0x0, 0x0, 0x6f, 0x7f, 0xd0, 0x8f,
    0xc0, 0x0, 0x0, 0x0, 0x5f, 0xfd, 0xe, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xd2, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xfd, 0x5f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xd6, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfd, 0x6f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd5, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfd, 0x3f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xd0, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xc, 0xfd, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xd0, 0x1e, 0xfa, 0x10, 0x0, 0x6, 0xf7,
    0xfd, 0x0, 0x3e, 0xff, 0xca, 0xbe, 0xf6, 0x2f,
    0xd0, 0x0, 0x18, 0xdf, 0xfd, 0x92, 0x2, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xb0,
    0x3, 0x10, 0x0, 0x0, 0x0, 0x7, 0xf7, 0xa,
    0xf9, 0x0, 0x0, 0x0, 0x1, 0xef, 0x10, 0x3f,
    0xf8, 0x10, 0x0, 0x3, 0xdf, 0x70, 0x0, 0x5f,
    0xff, 0xcb, 0xbe, 0xff, 0x70, 0x0, 0x0, 0x18,
    0xcf, 0xff, 0xd9, 0x20, 0x0,

    /* U+0068 "h" */
    0x6f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x80, 0x29, 0xdf, 0xfc, 0x70, 0x0,
    0x6f, 0x83, 0xef, 0xba, 0xcf, 0xfc, 0x0, 0x6f,
    0x9e, 0x90, 0x0, 0x3, 0xef, 0x70, 0x6f, 0xfa,
    0x0, 0x0, 0x0, 0x6f, 0xd0, 0x6f, 0xf1, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x6f, 0xc0, 0x0, 0x0,
    0x0, 0xd, 0xf2, 0x6f, 0x90, 0x0, 0x0, 0x0,
    0xc, 0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xc,
    0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xc, 0xf3,
    0x6f, 0x90, 0x0, 0x0, 0x0, 0xc, 0xf3, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0xc, 0xf3, 0x6f, 0x90,
    0x0, 0x0, 0x0, 0xc, 0xf3, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0xc, 0xf3, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0xc, 0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0,
    0xc, 0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xc,
    0xf3,

    /* U+0069 "i" */
    0x6f, 0x96, 0xf9, 0x26, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xf9, 0x6f, 0x96, 0xf9, 0x6f,
    0x96, 0xf9, 0x6f, 0x96, 0xf9, 0x6f, 0x96, 0xf9,
    0x6f, 0x96, 0xf9, 0x6f, 0x96, 0xf9, 0x6f, 0x96,
    0xf9, 0x6f, 0x90,

    /* U+006A "j" */
    0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0, 0x3,
    0x63, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf8,
    0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7,
    0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0,
    0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8,
    0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7,
    0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0,
    0x7, 0xf8, 0x0, 0x7, 0xf8, 0x0, 0x7, 0xf8,
    0x0, 0x7, 0xf8, 0x0, 0x8, 0xf7, 0x0, 0xc,
    0xf5, 0x4a, 0xdf, 0xe0, 0x5f, 0xfc, 0x30,

    /* U+006B "k" */
    0x8f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x70, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x8f, 0x70, 0x0, 0x0, 0x3f, 0xf4, 0x0, 0x8f,
    0x70, 0x0, 0x3, 0xff, 0x40, 0x0, 0x8f, 0x70,
    0x0, 0x3f, 0xf4, 0x0, 0x0, 0x8f, 0x70, 0x3,
    0xef, 0x40, 0x0, 0x0, 0x8f, 0x70, 0x2e, 0xf4,
    0x0, 0x0, 0x0, 0x8f, 0x72, 0xef, 0x40, 0x0,
    0x0, 0x0, 0x8f, 0xae, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0x4e, 0xf4, 0x0, 0x0, 0x0,
    0x8f, 0xd2, 0x4, 0xfe, 0x20, 0x0, 0x0, 0x8f,
    0x70, 0x0, 0x8f, 0xc0, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0xc, 0xf9, 0x0, 0x0, 0x8f, 0x70, 0x0,
    0x1, 0xef, 0x50, 0x0, 0x8f, 0x70, 0x0, 0x0,
    0x4f, 0xf2, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x8,
    0xfd, 0x0, 0x8f, 0x70, 0x0, 0x0, 0x0, 0xcf,
    0xa0,

    /* U+006C "l" */
    0x6f, 0x96, 0xf9, 0x6f, 0x96, 0xf9, 0x6f, 0x96,
    0xf9, 0x6f, 0x96, 0xf9, 0x6f, 0x96, 0xf9, 0x6f,
    0x96, 0xf9, 0x6f, 0x96, 0xf9, 0x6f, 0x96, 0xf9,
    0x6f, 0x96, 0xf9, 0x6f, 0x96, 0xf9, 0x6f, 0x96,
    0xf9, 0x6f, 0x90,

    /* U+006D "m" */
    0x8f, 0x40, 0x2a, 0xef, 0xda, 0x20, 0x0, 0x3a,
    0xdf, 0xea, 0x20, 0x7, 0xf5, 0x4f, 0xea, 0xbe,
    0xff, 0x20, 0x5f, 0xda, 0xbe, 0xff, 0x30, 0x7f,
    0x7e, 0x80, 0x0, 0xa, 0xfc, 0x2f, 0x60, 0x0,
    0xa, 0xfd, 0x6, 0xff, 0x90, 0x0, 0x0, 0xe,
    0xfd, 0x70, 0x0, 0x0, 0xf, 0xf2, 0x6f, 0xf1,
    0x0, 0x0, 0x0, 0xaf, 0xf0, 0x0, 0x0, 0x0,
    0xbf, 0x66, 0xfb, 0x0, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x0, 0x8, 0xf7, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x7f,
    0x86, 0xf9, 0x0, 0x0, 0x0, 0x7, 0xf8, 0x0,
    0x0, 0x0, 0x7, 0xf8, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x0, 0x7f, 0x86,
    0xf9, 0x0, 0x0, 0x0, 0x7, 0xf8, 0x0, 0x0,
    0x0, 0x7, 0xf8, 0x6f, 0x90, 0x0, 0x0, 0x0,
    0x7f, 0x80, 0x0, 0x0, 0x0, 0x7f, 0x86, 0xf9,
    0x0, 0x0, 0x0, 0x7, 0xf8, 0x0, 0x0, 0x0,
    0x7, 0xf8, 0x6f, 0x90, 0x0, 0x0, 0x0, 0x7f,
    0x80, 0x0, 0x0, 0x0, 0x7f, 0x86, 0xf9, 0x0,
    0x0, 0x0, 0x7, 0xf8, 0x0, 0x0, 0x0, 0x7,
    0xf8, 0x6f, 0x90, 0x0, 0x0, 0x0, 0x7f, 0x80,
    0x0, 0x0, 0x0, 0x7f, 0x86, 0xf9, 0x0, 0x0,
    0x0, 0x7, 0xf8, 0x0, 0x0, 0x0, 0x7, 0xf8,

    /* U+006E "n" */
    0x8f, 0x40, 0x29, 0xdf, 0xfc, 0x70, 0x0, 0x7f,
    0x53, 0xef, 0xba, 0xcf, 0xfc, 0x0, 0x7f, 0x7e,
    0x90, 0x0, 0x3, 0xef, 0x70, 0x6f, 0xfa, 0x0,
    0x0, 0x0, 0x6f, 0xd0, 0x6f, 0xf1, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x6f, 0xc0, 0x0, 0x0, 0x0,
    0xd, 0xf2, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xc,
    0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xc, 0xf3,
    0x6f, 0x90, 0x0, 0x0, 0x0, 0xc, 0xf3, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0xc, 0xf3, 0x6f, 0x90,
    0x0, 0x0, 0x0, 0xc, 0xf3, 0x6f, 0x90, 0x0,
    0x0, 0x0, 0xc, 0xf3, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0xc, 0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0,
    0xc, 0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xc,
    0xf3, 0x6f, 0x90, 0x0, 0x0, 0x0, 0xc, 0xf3,

    /* U+006F "o" */
    0x0, 0x0, 0x39, 0xdf, 0xfe, 0xb7, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xdb, 0xac, 0xff, 0xe3, 0x0,
    0x0, 0xbf, 0xd3, 0x0, 0x0, 0x8, 0xff, 0x30,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0,
    0xd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf4,
    0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf8,
    0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfb,
    0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfc,
    0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc,
    0x5f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x2f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf7,
    0xd, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf3,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0,
    0x0, 0xbf, 0xd3, 0x0, 0x0, 0x19, 0xfe, 0x10,
    0x0, 0xa, 0xff, 0xda, 0xac, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x39, 0xdf, 0xfe, 0xb5, 0x0, 0x0,

    /* U+0070 "p" */
    0x8f, 0x50, 0x17, 0xce, 0xfd, 0xa3, 0x0, 0x7,
    0xf7, 0x3e, 0xfc, 0xab, 0xef, 0xf7, 0x0, 0x7f,
    0x8e, 0xb1, 0x0, 0x0, 0x5f, 0xf5, 0x6, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x5f, 0xe0, 0x6f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x56, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf9, 0x6f, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xb6, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xfc, 0x6f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xc6, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xfb, 0x6f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x86, 0xff, 0x20, 0x0, 0x0, 0x0,
    0xd, 0xf3, 0x6f, 0xfa, 0x0, 0x0, 0x0, 0x6,
    0xfd, 0x6, 0xfa, 0xfa, 0x10, 0x0, 0x6, 0xff,
    0x30, 0x6f, 0x85, 0xff, 0xca, 0xbe, 0xff, 0x40,
    0x6, 0xf8, 0x2, 0x9d, 0xff, 0xd8, 0x10, 0x0,
    0x6f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x6b, 0xef, 0xeb, 0x40, 0xa, 0x90,
    0x1, 0xcf, 0xfc, 0xab, 0xef, 0x91, 0xfd, 0x0,
    0xdf, 0xa1, 0x0, 0x0, 0x6f, 0x8f, 0xd0, 0x7f,
    0xb0, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0xe, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xc2, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfc, 0x5f, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xc6, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xfc, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xc5, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfc, 0x3f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xc0, 0xff, 0x20, 0x0, 0x0, 0x0,
    0xc, 0xfc, 0x9, 0xfb, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xc0, 0x1e, 0xf9, 0x10, 0x0, 0x6, 0xf8,
    0xfc, 0x0, 0x3e, 0xff, 0xba, 0xbe, 0xf8, 0x2f,
    0xc0, 0x0, 0x7, 0xcf, 0xfe, 0xa4, 0x2, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xfc,

    /* U+0072 "r" */
    0x7f, 0x50, 0x3b, 0xef, 0x56, 0xf6, 0x3f, 0xfe,
    0xe4, 0x6f, 0x7d, 0xa1, 0x0, 0x5, 0xfc, 0xc0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x5, 0xfe,
    0x0, 0x0, 0x0, 0x5f, 0xb0, 0x0, 0x0, 0x5,
    0xfa, 0x0, 0x0, 0x0, 0x5f, 0xa0, 0x0, 0x0,
    0x5, 0xfa, 0x0, 0x0, 0x0, 0x5f, 0xa0, 0x0,
    0x0, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x5f, 0xa0,
    0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0, 0x5f,
    0xa0, 0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x6, 0xcf, 0xfe, 0xc6, 0x0, 0x0, 0xb,
    0xfe, 0xa9, 0xae, 0xf9, 0x0, 0x5, 0xfc, 0x0,
    0x0, 0xc, 0xf4, 0x0, 0x9f, 0x50, 0x0, 0x0,
    0x39, 0x40, 0xa, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xd7, 0x20, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xe9, 0x30, 0x0, 0x0, 0x0, 0x15, 0x9e,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xdf,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x3e, 0xa0,
    0x0, 0x0, 0x1, 0xfe, 0x0, 0xef, 0x50, 0x0,
    0x0, 0xaf, 0x90, 0x4, 0xff, 0xda, 0x9a, 0xef,
    0xc1, 0x0, 0x2, 0x9d, 0xff, 0xec, 0x60, 0x0,

    /* U+0074 "t" */
    0x0, 0x7, 0xf1, 0x0, 0x0, 0x0, 0x9, 0xf1,
    0x0, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x0,
    0xd, 0xf1, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf4, 0x2a, 0xaf, 0xfa, 0xaa, 0xa3, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x0, 0x0, 0x0, 0xf,
    0xf1, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0xe, 0xf1, 0x0, 0x0, 0x0, 0xc, 0xf6,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xba, 0xb6, 0x0,
    0x0, 0x6d, 0xff, 0xd5,

    /* U+0075 "u" */
    0x9f, 0x60, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x9f,
    0x60, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x9f, 0x60,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x9f, 0x60, 0x0,
    0x0, 0x0, 0xf, 0xf1, 0x9f, 0x60, 0x0, 0x0,
    0x0, 0xf, 0xf1, 0x9f, 0x60, 0x0, 0x0, 0x0,
    0xf, 0xf1, 0x9f, 0x60, 0x0, 0x0, 0x0, 0xf,
    0xf1, 0x9f, 0x60, 0x0, 0x0, 0x0, 0xf, 0xf1,
    0x9f, 0x60, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x9f,
    0x60, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x8f, 0x70,
    0x0, 0x0, 0x0, 0x2f, 0xf1, 0x7f, 0xa0, 0x0,
    0x0, 0x0, 0x7f, 0xf1, 0x3f, 0xe0, 0x0, 0x0,
    0x1, 0xef, 0xf1, 0xd, 0xfa, 0x0, 0x0, 0x2d,
    0x9c, 0xf1, 0x3, 0xff, 0xfb, 0xac, 0xfb, 0xb,
    0xf1, 0x0, 0x29, 0xef, 0xec, 0x60, 0xa, 0xf2,

    /* U+0076 "v" */
    0xbf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x95,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf3, 0xe,
    0xf2, 0x0, 0x0, 0x0, 0x3, 0xfd, 0x0, 0x8f,
    0x80, 0x0, 0x0, 0x0, 0x9f, 0x60, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0xc, 0xf4,
    0x0, 0x0, 0x5, 0xfa, 0x0, 0x0, 0x6f, 0x90,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x1, 0xff, 0x0,
    0x0, 0x2f, 0xd0, 0x0, 0x0, 0xa, 0xf5, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x4f, 0xb0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x0, 0xef, 0x10, 0x4f,
    0xa0, 0x0, 0x0, 0x0, 0x8, 0xf7, 0xa, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xc0, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x7f, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfb, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x7f, 0x70, 0x0, 0x0, 0x0, 0xdf, 0x20, 0x0,
    0x0, 0x1, 0xfd, 0x2f, 0xc0, 0x0, 0x0, 0x2,
    0xff, 0x70, 0x0, 0x0, 0x6, 0xf8, 0xe, 0xf0,
    0x0, 0x0, 0x7, 0xfc, 0xc0, 0x0, 0x0, 0xa,
    0xf3, 0x9, 0xf5, 0x0, 0x0, 0xc, 0xd8, 0xf1,
    0x0, 0x0, 0xf, 0xe0, 0x4, 0xf9, 0x0, 0x0,
    0x1f, 0x84, 0xf6, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0xfe, 0x0, 0x0, 0x6f, 0x40, 0xfb, 0x0, 0x0,
    0x8f, 0x40, 0x0, 0xbf, 0x20, 0x0, 0xbf, 0x0,
    0xbf, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x7f, 0x70,
    0x0, 0xfa, 0x0, 0x6f, 0x50, 0x1, 0xfb, 0x0,
    0x0, 0x2f, 0xb0, 0x5, 0xf5, 0x0, 0x1f, 0xa0,
    0x6, 0xf6, 0x0, 0x0, 0xd, 0xf0, 0xa, 0xf0,
    0x0, 0xc, 0xf0, 0xb, 0xf1, 0x0, 0x0, 0x9,
    0xf4, 0xf, 0xb0, 0x0, 0x7, 0xf4, 0xf, 0xc0,
    0x0, 0x0, 0x4, 0xf8, 0x4f, 0x60, 0x0, 0x2,
    0xf8, 0x4f, 0x80, 0x0, 0x0, 0x0, 0xfc, 0x9f,
    0x10, 0x0, 0x0, 0xdc, 0x8f, 0x30, 0x0, 0x0,
    0x0, 0xbf, 0xdb, 0x0, 0x0, 0x0, 0x8f, 0xce,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xf6, 0x0, 0x0,
    0x0, 0x3f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf1, 0x0, 0x0, 0x0, 0xe, 0xf4, 0x0, 0x0,

    /* U+0078 "x" */
    0x2f, 0xe1, 0x0, 0x0, 0x0, 0x1e, 0xf3, 0x7,
    0xfa, 0x0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0xcf,
    0x50, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x2f, 0xe1,
    0x0, 0x1e, 0xf2, 0x0, 0x0, 0x6, 0xfa, 0x0,
    0xaf, 0x60, 0x0, 0x0, 0x0, 0xbf, 0x55, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x1e, 0xee, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xbb, 0xf5, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x11, 0xef, 0x10, 0x0, 0x0, 0xa, 0xf6,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x5f, 0xb0, 0x0,
    0xb, 0xf7, 0x0, 0x1, 0xee, 0x10, 0x0, 0x1,
    0xef, 0x20, 0xb, 0xf5, 0x0, 0x0, 0x0, 0x5f,
    0xc0, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xa, 0xf8,

    /* U+0079 "y" */
    0xbf, 0x60, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x95,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf3, 0xe,
    0xf2, 0x0, 0x0, 0x0, 0x4, 0xfc, 0x0, 0x8f,
    0x80, 0x0, 0x0, 0x0, 0xaf, 0x60, 0x2, 0xfe,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0xb, 0xf4,
    0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0x5f, 0xb0,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0x0, 0xef, 0x10,
    0x0, 0x2f, 0xd0, 0x0, 0x0, 0x9, 0xf7, 0x0,
    0x8, 0xf6, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0,
    0xef, 0x10, 0x0, 0x0, 0x0, 0xcf, 0x30, 0x4f,
    0xa0, 0x0, 0x0, 0x0, 0x6, 0xf8, 0xa, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xe1, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xaf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xd, 0xdf,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xd7,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x23, 0xaa,
    0xaa, 0xaa, 0xaa, 0xbf, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xca, 0xaa, 0xaa, 0xaa,
    0xaa, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,

    /* U+007B "{" */
    0x0, 0x0, 0x1a, 0xef, 0xf1, 0x0, 0x0, 0xdf,
    0xd9, 0x80, 0x0, 0x4, 0xfd, 0x0, 0x0, 0x0,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0xa, 0xf4, 0x0, 0x0, 0x0, 0xa,
    0xf4, 0x0, 0x0, 0x0, 0xa, 0xf4, 0x0, 0x0,
    0x0, 0xa, 0xf4, 0x0, 0x0, 0x0, 0xa, 0xf4,
    0x0, 0x0, 0x0, 0xa, 0xf4, 0x0, 0x0, 0x0,
    0xb, 0xf2, 0x0, 0x0, 0x0, 0x1f, 0xe0, 0x0,
    0x0, 0x3, 0xcf, 0x50, 0x0, 0x0, 0xcf, 0xd3,
    0x0, 0x0, 0x0, 0x7c, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x7f, 0xb0, 0x0, 0x0, 0x0, 0xd, 0xf1,
    0x0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0, 0x0,
    0xa, 0xf4, 0x0, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0x0, 0x0, 0xa, 0xf4, 0x0, 0x0, 0x0, 0xa,
    0xf4, 0x0, 0x0, 0x0, 0xa, 0xf4, 0x0, 0x0,
    0x0, 0x9, 0xf4, 0x0, 0x0, 0x0, 0x8, 0xf6,
    0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xd9, 0x80, 0x0, 0x0, 0x1a, 0xef,
    0xf1,

    /* U+007C "|" */
    0x3f, 0x93, 0xf9, 0x3f, 0x93, 0xf9, 0x3f, 0x93,
    0xf9, 0x3f, 0x93, 0xf9, 0x3f, 0x93, 0xf9, 0x3f,
    0x93, 0xf9, 0x3f, 0x93, 0xf9, 0x3f, 0x93, 0xf9,
    0x3f, 0x93, 0xf9, 0x3f, 0x93, 0xf9, 0x3f, 0x93,
    0xf9, 0x3f, 0x93, 0xf9, 0x3f, 0x93, 0xf9, 0x3f,
    0x93, 0xf9, 0x3f, 0x90,

    /* U+007D "}" */
    0xcf, 0xfc, 0x30, 0x0, 0x0, 0x69, 0xcf, 0xf2,
    0x0, 0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x10, 0x0, 0x0, 0x0, 0x9f, 0x60,
    0x0, 0x0, 0x0, 0x2e, 0xe5, 0x10, 0x0, 0x0,
    0x2, 0xaf, 0xf2, 0x0, 0x0, 0x7, 0xfe, 0xa1,
    0x0, 0x0, 0x4f, 0xb0, 0x0, 0x0, 0x0, 0xbf,
    0x30, 0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x0, 0x8, 0xfa, 0x0, 0x0, 0x69,
    0xcf, 0xf2, 0x0, 0x0, 0xcf, 0xfc, 0x40, 0x0,
    0x0,

    /* U+007E "~" */
    0x1, 0x68, 0x85, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x94,
    0xf9, 0x30, 0x14, 0x9e, 0xff, 0xb9, 0xae, 0xf3,
    0x30, 0x0, 0x0, 0x0, 0x5a, 0xef, 0xeb, 0x40
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 140, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 146, .box_w = 3, .box_h = 22, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 33, .adv_w = 186, .box_w = 8, .box_h = 6, .ofs_x = 2, .ofs_y = 15},
    {.bitmap_index = 57, .adv_w = 306, .box_w = 19, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 266, .adv_w = 270, .box_w = 15, .box_h = 27, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 469, .adv_w = 256, .box_w = 16, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 645, .adv_w = 383, .box_w = 23, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 898, .adv_w = 114, .box_w = 3, .box_h = 6, .ofs_x = 2, .ofs_y = 15},
    {.bitmap_index = 907, .adv_w = 150, .box_w = 8, .box_h = 29, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 1023, .adv_w = 150, .box_w = 8, .box_h = 29, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 1139, .adv_w = 211, .box_w = 13, .box_h = 11, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1211, .adv_w = 342, .box_w = 16, .box_h = 16, .ofs_x = 3, .ofs_y = 2},
    {.bitmap_index = 1339, .adv_w = 112, .box_w = 3, .box_h = 7, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 1350, .adv_w = 256, .box_w = 10, .box_h = 2, .ofs_x = 3, .ofs_y = 6},
    {.bitmap_index = 1360, .adv_w = 112, .box_w = 3, .box_h = 3, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1365, .adv_w = 194, .box_w = 12, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1503, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1668, .adv_w = 270, .box_w = 13, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1811, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1976, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2141, .adv_w = 270, .box_w = 16, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2317, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2482, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2647, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2812, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2977, .adv_w = 270, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3142, .adv_w = 112, .box_w = 3, .box_h = 16, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 3166, .adv_w = 112, .box_w = 3, .box_h = 20, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 3196, .adv_w = 342, .box_w = 16, .box_h = 17, .ofs_x = 3, .ofs_y = 1},
    {.bitmap_index = 3332, .adv_w = 342, .box_w = 16, .box_h = 10, .ofs_x = 3, .ofs_y = 5},
    {.bitmap_index = 3412, .adv_w = 342, .box_w = 16, .box_h = 17, .ofs_x = 3, .ofs_y = 1},
    {.bitmap_index = 3548, .adv_w = 225, .box_w = 14, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3702, .adv_w = 486, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 4094, .adv_w = 326, .box_w = 20, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4314, .adv_w = 286, .box_w = 15, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4479, .adv_w = 318, .box_w = 18, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4677, .adv_w = 352, .box_w = 19, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4886, .adv_w = 258, .box_w = 13, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5029, .adv_w = 244, .box_w = 13, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5172, .adv_w = 347, .box_w = 19, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 5381, .adv_w = 355, .box_w = 18, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5579, .adv_w = 127, .box_w = 3, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5612, .adv_w = 174, .box_w = 9, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5711, .adv_w = 283, .box_w = 15, .box_h = 22, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 5876, .adv_w = 238, .box_w = 12, .box_h = 22, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 6008, .adv_w = 443, .box_w = 23, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 6261, .adv_w = 373, .box_w = 19, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 6470, .adv_w = 388, .box_w = 22, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 6712, .adv_w = 283, .box_w = 14, .box_h = 22, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 6866, .adv_w = 388, .box_w = 22, .box_h = 28, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 7174, .adv_w = 295, .box_w = 16, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 7350, .adv_w = 263, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7515, .adv_w = 265, .box_w = 17, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7702, .adv_w = 342, .box_w = 17, .box_h = 22, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 7889, .adv_w = 314, .box_w = 20, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8109, .adv_w = 468, .box_w = 29, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8428, .adv_w = 295, .box_w = 19, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8637, .adv_w = 280, .box_w = 18, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8835, .adv_w = 292, .box_w = 18, .box_h = 22, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9033, .adv_w = 150, .box_w = 8, .box_h = 29, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 9149, .adv_w = 191, .box_w = 12, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9287, .adv_w = 150, .box_w = 7, .box_h = 29, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 9389, .adv_w = 342, .box_w = 17, .box_h = 15, .ofs_x = 2, .ofs_y = 7},
    {.bitmap_index = 9517, .adv_w = 213, .box_w = 15, .box_h = 3, .ofs_x = -1, .ofs_y = -6},
    {.bitmap_index = 9540, .adv_w = 132, .box_w = 7, .box_h = 5, .ofs_x = 1, .ofs_y = 16},
    {.bitmap_index = 9558, .adv_w = 257, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9662, .adv_w = 294, .box_w = 15, .box_h = 23, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 9835, .adv_w = 232, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9939, .adv_w = 294, .box_w = 15, .box_h = 23, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 10112, .adv_w = 263, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 10232, .adv_w = 151, .box_w = 10, .box_h = 23, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10347, .adv_w = 294, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 10512, .adv_w = 282, .box_w = 14, .box_h = 23, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10673, .adv_w = 115, .box_w = 3, .box_h = 23, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10708, .adv_w = 115, .box_w = 6, .box_h = 29, .ofs_x = -1, .ofs_y = -7},
    {.bitmap_index = 10795, .adv_w = 241, .box_w = 14, .box_h = 23, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10956, .adv_w = 115, .box_w = 3, .box_h = 23, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10991, .adv_w = 431, .box_w = 23, .box_h = 16, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 11175, .adv_w = 282, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 11287, .adv_w = 294, .box_w = 16, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 11415, .adv_w = 294, .box_w = 15, .box_h = 22, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 11580, .adv_w = 294, .box_w = 15, .box_h = 22, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 11745, .adv_w = 174, .box_w = 9, .box_h = 16, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 11817, .adv_w = 208, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11921, .adv_w = 163, .box_w = 10, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12021, .adv_w = 282, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 12133, .adv_w = 239, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12253, .adv_w = 358, .box_w = 22, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12429, .adv_w = 226, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12541, .adv_w = 240, .box_w = 15, .box_h = 22, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12706, .adv_w = 235, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 12810, .adv_w = 150, .box_w = 10, .box_h = 29, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12955, .adv_w = 119, .box_w = 3, .box_h = 29, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 12999, .adv_w = 150, .box_w = 10, .box_h = 29, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 13144, .adv_w = 342, .box_w = 16, .box_h = 4, .ofs_x = 3, .ofs_y = 8}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_FontDengXianBold32Num = {
#else
lv_font_t ui_font_FontDengXianBold32Num = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 30,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -6,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_FONTDENGXIANBOLD32NUM*/

