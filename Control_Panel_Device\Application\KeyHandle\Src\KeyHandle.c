//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file KeyHandle.c
*
* @brief Key handling module implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "KeyHandle.h"
#include "Event.h"
#include "HalKey.h"
#include "GlobalTypes.h"
#include "FreeRTOS.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define KEYHANDLE_MAX_COMBO_SIZE_D                   2U       /* Maximum number of keys contained in a single combination key */
#define KEYHANDLE_SINGLE_KEY_DEBOUNCE_TIME_MS_D      50U      /* Key debounce time (milliseconds) */
#define KEYHANDLE_SINGLE_KEY_LONG_PRESS_TIME_MS_D    1000U    /* Long press detection time (milliseconds) */
#define KEYHANDLE_SINGLE_KEY_LONG_PRESS_REPEAT_MS_D  200U     /* Long press repeat trigger period (milliseconds) */

/**
* @brief Single key type enumeration
*/
typedef enum
{
    KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E = 0U,          /* Left function key */
    KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E,              /* Right function key */
    KEYHANDLE_SINGLE_KEY_UP_E,                      /* Up direction key */
    KEYHANDLE_SINGLE_KEY_DOWN_E,                    /* Down direction key */
    KEYHANDLE_SINGLE_KEY_LEFT_E,                    /* Left direction key */
    KEYHANDLE_SINGLE_KEY_RIGHT_E,                   /* Right direction key */
    KEYHANDLE_SINGLE_KEY_START_E,                   /* Start key */
    KEYHANDLE_SINGLE_KEY_STOP_E,                    /* Stop key */
    KEYHANDLE_SINGLE_KEY_HELP_E,                    /* Help key */
    KEYHANDLE_SINGLE_KEY_CONTROL_SWITCH_E,          /* Control authority switching key */
    KEYHANDLE_SINGLE_KEY_MAX_E
} KEYHANDLE_SINGLE_KEY_E;

/**
* @brief Combination key type enumeration
*/
typedef enum
{
    KEYHANDLE_COMBO_KEY_LEFT_RIGHT_E = 0U,          /* Left direction key + Right direction key */
    KEYHANDLE_COMBO_KEY_LEFT_UP_E,                  /* Left direction key + Up direction key */
    KEYHANDLE_COMBO_KEY_LEFT_DOWN_E,                /* Left direction key + Down direction key */
    KEYHANDLE_COMBO_KEY_RIGHT_UP_E,                 /* Right direction key + Up direction key */
    KEYHANDLE_COMBO_KEY_RIGHT_DOWN_E,               /* Right direction key + Down direction key */
    KEYHANDLE_COMBO_KEY_LEFT_HELP_E,                /* Left direction key + Help key */
    KEYHANDLE_COMBO_KEY_MAX_E
} KEYHANDLE_COMBO_KEY_E;

/**
* @brief Single key mapping structure
*/
typedef struct
{
    KEYHANDLE_SINGLE_KEY_E eSingleKey;              /* Single key type (logical key) */
    U8 u8MatrixKey;                                 /* Corresponding matrix key index (physical key position, 0-15) */
} KEYHANDLE_SINGLE_KEY_MAP_T;

/**
* @brief Combination key mapping structure
*/
typedef struct
{
    KEYHANDLE_COMBO_KEY_E eComboKey;                            /* Combination key type */
    U8 u8KeyCount;                                              /* Number of keys contained in the combination */
    KEYHANDLE_SINGLE_KEY_E aeKeys[KEYHANDLE_MAX_COMBO_SIZE_D];  /* List of keys in the combination */
} KEYHANDLE_COMBO_KEY_MAP_T;

/**
* @brief Key handling configuration structure
*/
typedef struct
{
    U32 u32SingleKeyDebounceTimeMs;                                             /* Key debounce time (milliseconds) */
    U32 u32SingleKeyLongPressTimeMs;                                            /* Long press detection time (milliseconds) */
    U32 u32SingleKeyLongPressRepeatMs;                                          /* Long press repeat trigger period (milliseconds) */
    KEYHANDLE_SINGLE_KEY_MAP_T asSingleKeyMaps[KEYHANDLE_SINGLE_KEY_MAX_E];     /* Single key mapping configuration array */
    KEYHANDLE_COMBO_KEY_MAP_T asComboKeyMaps[KEYHANDLE_COMBO_KEY_MAX_E];        /* Combination key mapping array */
} KEYHANDLE_CONFIG_T;

/**
* @brief Key state structure
*/
typedef struct
{
    U16 u16CurrentRawKeyMatrix;                                                 /* Current raw key matrix state (16-bit bitmap) */
    U16 u16PreviousRawKeyMatrix;                                                /* Previous raw key matrix state (for debounce processing) */
    U16 u16CurrentDebouncedKeyMatrix;                                           /* Current debounced key matrix state */
    U16 u16PreviousDebouncedKeyMatrix;                                          /* Previous debounced key matrix state (for edge detection) */
    U32 au32SingleKeyPressTime[KEYHANDLE_SINGLE_KEY_MAX_E];                     /* Press timestamp array for each key */
    U32 au32SingleKeyDebounceTime[KEYHANDLE_SINGLE_KEY_MAX_E];                  /* Debounce start time array for each key */
    U32 au32SingleKeyLastLongPressTime[KEYHANDLE_SINGLE_KEY_MAX_E];             /* Last long press trigger time array for each key */
    BOOL abSingleKeyLongPressProcessed[KEYHANDLE_SINGLE_KEY_MAX_E];             /* Processed flag array for each key */
    KEYHANDLE_COMBO_KEY_E eActiveComboKey;                                      /* Currently active combination key type (KEYHANDLE_COMBO_KEY_MAX_E means not in combo mode) */
} KEYHANDLE_STATE_T;

/**
* @brief Key handling handle structure
*/
typedef struct
{
    KEYHANDLE_CONFIG_T sConfig;             /* Configuration parameter structure */
    KEYHANDLE_STATE_T sState;               /* State information structure */
    HALKEY_HANDLE_T sHalKey;                /* Hardware abstraction layer handle */
} KEYHANDLE_HANDLE_T;

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static void KeyHandle_vProcessStateScan(void);
static void KeyHandle_vProcessStateDebounce(void);
static void KeyHandle_vProcessStateDetectSingle(void);
static void KeyHandle_vProcessStateDetectCombo(void);
static void KeyHandle_vTriggerSingleKeyShortPressEvent(const KEYHANDLE_SINGLE_KEY_E eSingleKey);
static void KeyHandle_vTriggerSingleKeyLongPressEvent(const KEYHANDLE_SINGLE_KEY_E eSingleKey);
static void KeyHandle_vTriggerComboKeyShortPressEvent(const KEYHANDLE_COMBO_KEY_E eComboKey);
static void KeyHandle_vResetState(void);
static BOOL KeyHandle_bIsComboMatch(const KEYHANDLE_COMBO_KEY_MAP_T* const psCombo, const KEYHANDLE_SINGLE_KEY_E* const aeActiveKeys, const U8 u8KeyCount);
static BOOL KeyHandle_bIsKeyPartOfActiveCombo(const KEYHANDLE_SINGLE_KEY_E eSingleKey);
static U32 KeyHandle_u32GetTickMs(void);
static U32 KeyHandle_u32GetElapsedTime(const U32 u32CurrentTime, const U32 u32StartTime);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------
static KEYHANDLE_HANDLE_T sKeyHandle;   /* Static singleton key handling instance */

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize key handling module.
* @remark None.
*
* @return Initialization status.
*/
KEYHANDLE_STATUS_E KeyHandle_eInit(void)
{
    /* Initialize HalKey */
    HALKEY_CONFIG_T sHalConfig;
    if (HalKey_eGetDefaultConfig(&sHalConfig) != HALKEY_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }

    /* Initialize HalKey */
    if (HalKey_eInit(&sKeyHandle.sHalKey, &sHalConfig) != HALKEY_OK_E)
    {
        return KEYHANDLE_ERROR_E;
    }
    
    sKeyHandle.sConfig.u32SingleKeyDebounceTimeMs = KEYHANDLE_SINGLE_KEY_DEBOUNCE_TIME_MS_D;
    sKeyHandle.sConfig.u32SingleKeyLongPressTimeMs = KEYHANDLE_SINGLE_KEY_LONG_PRESS_TIME_MS_D;
    sKeyHandle.sConfig.u32SingleKeyLongPressRepeatMs = KEYHANDLE_SINGLE_KEY_LONG_PRESS_REPEAT_MS_D;

    /* Initialize default key mapping configuration */
    sKeyHandle.sConfig.asSingleKeyMaps[0U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E, 0U};        /* Left function key->Matrix position 0 */
    sKeyHandle.sConfig.asSingleKeyMaps[1U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E, 1U};       /* Right function key->Matrix position 1 */
    sKeyHandle.sConfig.asSingleKeyMaps[2U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_UP_E, 2U};               /* Up direction key->Matrix position 2 */
    sKeyHandle.sConfig.asSingleKeyMaps[3U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_DOWN_E, 3U};             /* Down direction key->Matrix position 3 */
    sKeyHandle.sConfig.asSingleKeyMaps[4U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_LEFT_E, 4U};             /* Left direction key->Matrix position 4 */
    sKeyHandle.sConfig.asSingleKeyMaps[5U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_RIGHT_E, 5U};            /* Right direction key->Matrix position 5 */
    sKeyHandle.sConfig.asSingleKeyMaps[6U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_START_E, 6U};            /* Start key->Matrix position 6 */
    sKeyHandle.sConfig.asSingleKeyMaps[7U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_STOP_E, 7U};             /* Stop key->Matrix position 7 */
    sKeyHandle.sConfig.asSingleKeyMaps[8U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_HELP_E, 8U};             /* Help key->Matrix position 8 */
    sKeyHandle.sConfig.asSingleKeyMaps[9U] = (KEYHANDLE_SINGLE_KEY_MAP_T){KEYHANDLE_SINGLE_KEY_CONTROL_SWITCH_E, 9U};   /* Control authority switching key->Matrix position 9 */
    
    /* Initialize default combination key mapping */
    sKeyHandle.sConfig.asComboKeyMaps[0U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_RIGHT_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_RIGHT_E}};     /* Left+Right direction keys */
    sKeyHandle.sConfig.asComboKeyMaps[1U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_UP_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_UP_E}};           /* Left+Up direction keys */
    sKeyHandle.sConfig.asComboKeyMaps[2U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_DOWN_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_DOWN_E}};       /* Left+Down direction keys */
    sKeyHandle.sConfig.asComboKeyMaps[3U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_RIGHT_UP_E, 2U, {KEYHANDLE_SINGLE_KEY_RIGHT_E, KEYHANDLE_SINGLE_KEY_UP_E}};         /* Right+Up direction keys */
    sKeyHandle.sConfig.asComboKeyMaps[4U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_RIGHT_DOWN_E, 2U, {KEYHANDLE_SINGLE_KEY_RIGHT_E, KEYHANDLE_SINGLE_KEY_DOWN_E}};     /* Right+Down direction keys */
    sKeyHandle.sConfig.asComboKeyMaps[5U] = (KEYHANDLE_COMBO_KEY_MAP_T){KEYHANDLE_COMBO_KEY_LEFT_HELP_E, 2U, {KEYHANDLE_SINGLE_KEY_LEFT_E, KEYHANDLE_SINGLE_KEY_HELP_E}};       /* Left+Help keys */

    /* Reset state */
    KeyHandle_vResetState();

    return KEYHANDLE_OK_E;
}

/**
* @brief Process key scanning and event detection.
* @remark Main processing function that sequentially executes all processing stages.
*
* @return Nothing.
*/
void KeyHandle_vRun(void)
{
    /* Scan key matrix */
    KeyHandle_vProcessStateScan();
    
    /* Process key debounce */
    KeyHandle_vProcessStateDebounce();
    
    /* Detect single key events */
    KeyHandle_vProcessStateDetectSingle();
    
    /* Detect combination key events */
    KeyHandle_vProcessStateDetectCombo();
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Scan key matrix and save previous states for processing.
* @remark None.
*
* @return Nothing.
*/
void KeyHandle_vProcessStateScan(void)
{
    /* Save previous raw state for debounce processing */
    sKeyHandle.sState.u16PreviousRawKeyMatrix = sKeyHandle.sState.u16CurrentRawKeyMatrix;
    
    /* Save previous debounced state for edge detection */
    sKeyHandle.sState.u16PreviousDebouncedKeyMatrix = sKeyHandle.sState.u16CurrentDebouncedKeyMatrix;
    
    /* Scan key matrix, get current raw key state */
    HalKey_eScanMatrix(&sKeyHandle.sHalKey, &sKeyHandle.sState.u16CurrentRawKeyMatrix);
}

/**
* @brief Perform key debounce processing.
* @remark None.
*
* @return Nothing.
*/
void KeyHandle_vProcessStateDebounce(void)
{
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();

    /* Calculate state change bitmap based on raw key matrix states */
    U16 u16Changed = sKeyHandle.sState.u16CurrentRawKeyMatrix ^ sKeyHandle.sState.u16PreviousRawKeyMatrix;
    
    /* Perform debounce processing for each mapped key */
    for (U8 u8KeyMapIndex = 0U; u8KeyMapIndex < KEYHANDLE_SINGLE_KEY_MAX_E; u8KeyMapIndex++)
    {
        U8 u8MatrixKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].u8MatrixKey;
        
        /* Check if key state changed */
        if (u16Changed & (1U << u8MatrixKey))
        {
            /* Key state changed, restart debounce timing */
            sKeyHandle.sState.au32SingleKeyDebounceTime[u8KeyMapIndex] = u32CurrentTime;
        }
        else
        {
            if (KeyHandle_u32GetElapsedTime(u32CurrentTime, sKeyHandle.sState.au32SingleKeyDebounceTime[u8KeyMapIndex]) >= sKeyHandle.sConfig.u32SingleKeyDebounceTimeMs)
            {
                /* Debounce time has elapsed, update debounced key state */
                if (sKeyHandle.sState.u16CurrentRawKeyMatrix & (1U << u8MatrixKey))
                {
                    /* Set key pressed state */
                    sKeyHandle.sState.u16CurrentDebouncedKeyMatrix |= (1U << u8MatrixKey);
                }
                else
                {
                    /* Clear key pressed state */
                    sKeyHandle.sState.u16CurrentDebouncedKeyMatrix &= ~(1U << u8MatrixKey);
                }
            }
        }
    }
}

/**
* @brief Detect single key events.
* @remark None.
*
* @return Nothing.
*/
void KeyHandle_vProcessStateDetectSingle(void)
{
    /* Get current time */
    U32 u32CurrentTime = KeyHandle_u32GetTickMs();
    
    /* Currently pressed keys */
    U16 u16PressedKeys = sKeyHandle.sState.u16CurrentDebouncedKeyMatrix;

    /* Just pressed keys */
    U16 u16NewPressedKeys = sKeyHandle.sState.u16CurrentDebouncedKeyMatrix & (~sKeyHandle.sState.u16PreviousDebouncedKeyMatrix);

    /* Just released keys */
    U16 u16ReleasedKeys = sKeyHandle.sState.u16PreviousDebouncedKeyMatrix & (~sKeyHandle.sState.u16CurrentDebouncedKeyMatrix);
    
    /* Process new key press state for mapped keys */
    for (U8 u8KeyMapIndex = 0U; u8KeyMapIndex < KEYHANDLE_SINGLE_KEY_MAX_E; u8KeyMapIndex++)
    {
        U8 u8MatrixKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].u8MatrixKey;
        
        /* Check if key is pressed */
        if (u16NewPressedKeys & (1U << u8MatrixKey))
        {
            /* Record press time */
            sKeyHandle.sState.au32SingleKeyPressTime[u8KeyMapIndex] = u32CurrentTime;

            /* Clear long press processed flag */
            sKeyHandle.sState.abSingleKeyLongPressProcessed[u8KeyMapIndex] = FALSE_D;

            /* Clear last long press time */
            sKeyHandle.sState.au32SingleKeyLastLongPressTime[u8KeyMapIndex] = 0U;
        }
    }

    /* Process long press event detection for mapped keys */
    for (U8 u8KeyMapIndex = 0U; u8KeyMapIndex < KEYHANDLE_SINGLE_KEY_MAX_E; u8KeyMapIndex++)
    {
        U8 u8MatrixKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].u8MatrixKey;

        KEYHANDLE_SINGLE_KEY_E eSingleKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].eSingleKey;

        /* Check if key is pressed */
        if (u16PressedKeys & (1U << u8MatrixKey))
        {   
            /* Check if this single key is part of active combo */
            if (!KeyHandle_bIsKeyPartOfActiveCombo(eSingleKey))
            {
                /* Check if initial long press conditions are met */
                if ((KeyHandle_u32GetElapsedTime(u32CurrentTime, sKeyHandle.sState.au32SingleKeyPressTime[u8KeyMapIndex]) >= sKeyHandle.sConfig.u32SingleKeyLongPressTimeMs) && (!sKeyHandle.sState.abSingleKeyLongPressProcessed[u8KeyMapIndex]))
                {
                    /* First long press trigger */
                    KeyHandle_vTriggerSingleKeyLongPressEvent(eSingleKey);
                    
                    /* Mark as processed and record first trigger time */
                    sKeyHandle.sState.abSingleKeyLongPressProcessed[u8KeyMapIndex] = TRUE_D;

                    /* Record last long press time */
                    sKeyHandle.sState.au32SingleKeyLastLongPressTime[u8KeyMapIndex] = u32CurrentTime;
                }
                
                /* Check for repeat long press triggers */
                if ((KeyHandle_u32GetElapsedTime(u32CurrentTime, sKeyHandle.sState.au32SingleKeyLastLongPressTime[u8KeyMapIndex]) >= sKeyHandle.sConfig.u32SingleKeyLongPressRepeatMs) && (sKeyHandle.sState.abSingleKeyLongPressProcessed[u8KeyMapIndex]))
                {
                    /* Repeat long press trigger */
                    KeyHandle_vTriggerSingleKeyLongPressEvent(eSingleKey);
                    
                    /* Update last long press time for next repeat */
                    sKeyHandle.sState.au32SingleKeyLastLongPressTime[u8KeyMapIndex] = u32CurrentTime;
                }
            }
        }
    }
    
    /* Process key release state and detect short press events for mapped keys */
    for (U8 u8KeyMapIndex = 0U; u8KeyMapIndex < KEYHANDLE_SINGLE_KEY_MAX_E; u8KeyMapIndex++)
    {
        U8 u8MatrixKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].u8MatrixKey;
        
        /* Check if key is released */
        if (u16ReleasedKeys & (1U << u8MatrixKey))
        {
            /* Check if key is pressed for less than long press time */
            if (KeyHandle_u32GetElapsedTime(u32CurrentTime, sKeyHandle.sState.au32SingleKeyPressTime[u8KeyMapIndex]) < sKeyHandle.sConfig.u32SingleKeyLongPressTimeMs)
            {
                KEYHANDLE_SINGLE_KEY_E eSingleKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].eSingleKey;
                
                /* Check if this single key is part of active combo */
                if (!KeyHandle_bIsKeyPartOfActiveCombo(eSingleKey))
                {
                    /* Trigger single key short press event */
                    KeyHandle_vTriggerSingleKeyShortPressEvent(eSingleKey);
                }
            }

            /* Clear processed flag and long press trigger time */
            sKeyHandle.sState.abSingleKeyLongPressProcessed[u8KeyMapIndex] = FALSE_D;
            
            /* Clear last long press time */
            sKeyHandle.sState.au32SingleKeyLastLongPressTime[u8KeyMapIndex] = 0U;
        }
    }
}

/**
* @brief Detect combination key events.
* @remark None.
*
* @return Nothing.
*/
void KeyHandle_vProcessStateDetectCombo(void)
{
    /* Get list of currently pressed single keys */
    KEYHANDLE_SINGLE_KEY_E aeCurrentKeys[KEYHANDLE_SINGLE_KEY_MAX_E];
    
    for (U8 u8KeyArrayIndex = 0U; u8KeyArrayIndex < KEYHANDLE_SINGLE_KEY_MAX_E; u8KeyArrayIndex++)
    {
    	aeCurrentKeys[u8KeyArrayIndex] = KEYHANDLE_SINGLE_KEY_MAX_E;
    }

    /* Current key count */
    U8 u8CurrentKeyCount = 0U;
    
    /* Get list of currently pressed single keys */
    for (U8 u8KeyMapIndex = 0U; u8KeyMapIndex < KEYHANDLE_SINGLE_KEY_MAX_E; u8KeyMapIndex++)
    {
        U8 u8MatrixKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].u8MatrixKey;

        KEYHANDLE_SINGLE_KEY_E eSingleKey = sKeyHandle.sConfig.asSingleKeyMaps[u8KeyMapIndex].eSingleKey;
        
        /* Check if key is pressed */
        if (sKeyHandle.sState.u16CurrentDebouncedKeyMatrix & (1U << u8MatrixKey))
        {
            /* Add to current key list, increase key count */
            aeCurrentKeys[u8CurrentKeyCount++] = eSingleKey;
        }
    }
    
    /* Traverse all combination key mappings, check if there are matching combinations */
    for (U8 u8ComboMapIndex = 0U; u8ComboMapIndex < KEYHANDLE_COMBO_KEY_MAX_E; u8ComboMapIndex++)
    {
        /* Check if combination key matches the current pressed keys */
        if (KeyHandle_bIsComboMatch(&sKeyHandle.sConfig.asComboKeyMaps[u8ComboMapIndex], aeCurrentKeys, u8CurrentKeyCount))
        {
            /* Check if it's a new combination key (avoid repeated triggering) */ 
            BOOL bNewCombo = (sKeyHandle.sState.eActiveComboKey != sKeyHandle.sConfig.asComboKeyMaps[u8ComboMapIndex].eComboKey);

            /* If combination key is new */
            if (bNewCombo)
            {
                /* Trigger combination key short press event */
                KeyHandle_vTriggerComboKeyShortPressEvent(sKeyHandle.sConfig.asComboKeyMaps[u8ComboMapIndex].eComboKey);
                
                /* Update active combination key state */
                sKeyHandle.sState.eActiveComboKey = sKeyHandle.sConfig.asComboKeyMaps[u8ComboMapIndex].eComboKey;
            }

            break;
        }
    }
    
    /* Clear combination key state when no keys are pressed */
    if (u8CurrentKeyCount == 0U)
    {
        sKeyHandle.sState.eActiveComboKey = KEYHANDLE_COMBO_KEY_MAX_E;
    }
}

/**
* @brief Trigger single key short press event.
* @remark None.
*
* @param eSingleKey [in]: Single key type.
* @return Nothing.
*/
void KeyHandle_vTriggerSingleKeyShortPressEvent(const KEYHANDLE_SINGLE_KEY_E eSingleKey)
{
    EVENT_EVENT_TYPE_E eEventType = EVENT_TYPE_NONE_E;
    
    /* Map single key to corresponding short press event type */
    switch (eSingleKey)
    {
        case KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E: eEventType = EVENT_TYPE_KEY_LEFT_FUNC_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E: eEventType = EVENT_TYPE_KEY_RIGHT_FUNC_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_UP_E: eEventType = EVENT_TYPE_KEY_UP_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_DOWN_E: eEventType = EVENT_TYPE_KEY_DOWN_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_LEFT_E: eEventType = EVENT_TYPE_KEY_LEFT_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_RIGHT_E: eEventType = EVENT_TYPE_KEY_RIGHT_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_START_E: eEventType = EVENT_TYPE_KEY_START_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_STOP_E: eEventType = EVENT_TYPE_KEY_STOP_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_HELP_E: eEventType = EVENT_TYPE_KEY_HELP_SHORT_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_CONTROL_SWITCH_E: eEventType = EVENT_TYPE_KEY_CONTROL_SWITCH_SHORT_PRESS_E; break;
        default: return; /* Invalid key type */
    }
    
    /* Publish event without additional data */
    Event_ePublish(eEventType, NULL_D, 0U);
}

/**
* @brief Trigger single key long press event.
* @remark None.
*
* @param eSingleKey [in]: Single key type.
* @return Nothing.
*/
void KeyHandle_vTriggerSingleKeyLongPressEvent(const KEYHANDLE_SINGLE_KEY_E eSingleKey)
{
    EVENT_EVENT_TYPE_E eEventType = EVENT_TYPE_NONE_E;
    
    /* Map single key to corresponding long press event type */
    switch (eSingleKey)
    {
        case KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E: eEventType = EVENT_TYPE_KEY_LEFT_FUNC_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E: eEventType = EVENT_TYPE_KEY_RIGHT_FUNC_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_UP_E: eEventType = EVENT_TYPE_KEY_UP_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_DOWN_E: eEventType = EVENT_TYPE_KEY_DOWN_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_LEFT_E: eEventType = EVENT_TYPE_KEY_LEFT_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_RIGHT_E: eEventType = EVENT_TYPE_KEY_RIGHT_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_START_E: eEventType = EVENT_TYPE_KEY_START_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_STOP_E: eEventType = EVENT_TYPE_KEY_STOP_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_HELP_E: eEventType = EVENT_TYPE_KEY_HELP_LONG_PRESS_E; break;
        case KEYHANDLE_SINGLE_KEY_CONTROL_SWITCH_E: eEventType = EVENT_TYPE_KEY_CONTROL_SWITCH_LONG_PRESS_E; break;
        default: return; /* Invalid key type */
    }
    
    /* Publish event without additional data */
    Event_ePublish(eEventType, NULL_D, 0U);
}

/**
* @brief Trigger combo key short press event.
* @remark None.
*
* @param eComboKey [in]: Combo key type.
* @return Nothing.
*/
void KeyHandle_vTriggerComboKeyShortPressEvent(const KEYHANDLE_COMBO_KEY_E eComboKey)
{
    EVENT_EVENT_TYPE_E eEventType = EVENT_TYPE_NONE_E;
    
    /* Map combo key to corresponding event type */
    switch (eComboKey)
    {
        case KEYHANDLE_COMBO_KEY_LEFT_RIGHT_E: eEventType = EVENT_TYPE_KEY_LEFT_RIGHT_COMBO_PRESS_E; break;
        case KEYHANDLE_COMBO_KEY_LEFT_UP_E: eEventType = EVENT_TYPE_KEY_LEFT_UP_COMBO_PRESS_E; break;
        case KEYHANDLE_COMBO_KEY_LEFT_DOWN_E: eEventType = EVENT_TYPE_KEY_LEFT_DOWN_COMBO_PRESS_E; break;
        case KEYHANDLE_COMBO_KEY_RIGHT_UP_E: eEventType = EVENT_TYPE_KEY_RIGHT_UP_COMBO_PRESS_E; break;
        case KEYHANDLE_COMBO_KEY_RIGHT_DOWN_E: eEventType = EVENT_TYPE_KEY_RIGHT_DOWN_COMBO_PRESS_E; break;
        case KEYHANDLE_COMBO_KEY_LEFT_HELP_E: eEventType = EVENT_TYPE_KEY_LEFT_HELP_COMBO_PRESS_E; break;
        default: return; /* Invalid combo key type */
    }
    
    /* Publish event without additional data */
    Event_ePublish(eEventType, NULL_D, 0U);
}

/**
* @brief Reset key handling state to recover from error conditions.
* @remark None.
*
* @return Nothing.
*/
void KeyHandle_vResetState(void)
{
    /* Clear key matrix state */
    sKeyHandle.sState.u16CurrentRawKeyMatrix = 0U;
    sKeyHandle.sState.u16PreviousRawKeyMatrix = 0U;
    sKeyHandle.sState.u16CurrentDebouncedKeyMatrix = 0U;
    sKeyHandle.sState.u16PreviousDebouncedKeyMatrix = 0U;

    /* Clear single key state */
    for (U8 u8KeyStateIndex = 0U; u8KeyStateIndex < KEYHANDLE_SINGLE_KEY_MAX_E; u8KeyStateIndex++)
    {
        sKeyHandle.sState.au32SingleKeyPressTime[u8KeyStateIndex] = 0U;
        sKeyHandle.sState.au32SingleKeyDebounceTime[u8KeyStateIndex] = 0U;
        sKeyHandle.sState.au32SingleKeyLastLongPressTime[u8KeyStateIndex] = 0U;
        sKeyHandle.sState.abSingleKeyLongPressProcessed[u8KeyStateIndex] = FALSE_D;
    }

    /* Clear combo key state */
    sKeyHandle.sState.eActiveComboKey = KEYHANDLE_COMBO_KEY_MAX_E;
}

/**
* @brief Check if combination key matches.
* @remark None.
*
* @param psCombo [in]: Combination key mapping pointer.
* @param aeActiveKeys [in]: Current active key list.
* @param u8KeyCount [in]: Number of currently active keys.
* @return Whether matches (TRUE_D-match, FALSE_D-no match).
*/
BOOL KeyHandle_bIsComboMatch(const KEYHANDLE_COMBO_KEY_MAP_T* const psCombo, const KEYHANDLE_SINGLE_KEY_E* const aeActiveKeys, const U8 u8KeyCount)
{
    /* Parameter validity and key count check */
    if ((psCombo == NULL_D) || (aeActiveKeys == NULL_D) || (u8KeyCount != psCombo->u8KeyCount))
    {
        return FALSE_D;
    }
    
    /* Check if all keys in the combination are in the current active key list */
    for (U8 u8ComboKeyIndex = 0U; u8ComboKeyIndex < psCombo->u8KeyCount; u8ComboKeyIndex++)
    {
        BOOL bFound = FALSE_D;

        for (U8 u8ActiveKeyIndex = 0U; u8ActiveKeyIndex < u8KeyCount; u8ActiveKeyIndex++)
        {
            /* Check if key is in the current active key list */
            if (psCombo->aeKeys[u8ComboKeyIndex] == aeActiveKeys[u8ActiveKeyIndex])
            {
                bFound = TRUE_D;

                break;
            }
        }

        /* Check if key is not found */
        if (!bFound)
        {
            return FALSE_D;
        }
    }
    
    return TRUE_D;
}

/**
* @brief Check if single key is part of the currently active combo key.
* @remark None.
*
* @param eSingleKey [in]: Single key type to check.
* @return Whether the key is part of active combo (TRUE_D-yes, FALSE_D-no).
*/
BOOL KeyHandle_bIsKeyPartOfActiveCombo(const KEYHANDLE_SINGLE_KEY_E eSingleKey)
{
    /* If no active combo key, key is not part of any combo */
    if (sKeyHandle.sState.eActiveComboKey == KEYHANDLE_COMBO_KEY_MAX_E)
    {
        return FALSE_D;
    }
    
    /* Find the active combo key mapping */
    for (U8 u8ComboMapIndex = 0U; u8ComboMapIndex < KEYHANDLE_COMBO_KEY_MAX_E; u8ComboMapIndex++)
    {
        if (sKeyHandle.sConfig.asComboKeyMaps[u8ComboMapIndex].eComboKey == sKeyHandle.sState.eActiveComboKey)
        {
            /* Check if the single key is in this combo */
            for (U8 u8ComboKeyIndex = 0U; u8ComboKeyIndex < sKeyHandle.sConfig.asComboKeyMaps[u8ComboMapIndex].u8KeyCount; u8ComboKeyIndex++)
            {
                if (sKeyHandle.sConfig.asComboKeyMaps[u8ComboMapIndex].aeKeys[u8ComboKeyIndex] == eSingleKey)
                {
                    return TRUE_D;
                }
            }

            break;
        }
    }
    
    return FALSE_D;
}

/**
* @brief Get system clock milliseconds.
* @remark None.
*
* @return Current system clock milliseconds.
*/
U32 KeyHandle_u32GetTickMs(void)
{
    return (U32)(xTaskGetTickCount() * portTICK_PERIOD_MS);
}

/**
* @brief Calculate elapsed time handling potential overflow.
* @remark None.
*
* @param u32CurrentTime [in]: Current time in milliseconds.
* @param u32StartTime [in]: Start time in milliseconds.
* @return Elapsed time in milliseconds.
*/
U32 KeyHandle_u32GetElapsedTime(const U32 u32CurrentTime, const U32 u32StartTime)
{
    U32 u32ElapsedTime = 0U;
    
    if (u32CurrentTime >= u32StartTime)
    {
        /* Normal case: no overflow occurred */
        u32ElapsedTime = u32CurrentTime - u32StartTime;
    }
    else
    {
        /* Calculate elapsed time accounting */
        u32ElapsedTime = (0xFFFFFFFFU * portTICK_PERIOD_MS - u32StartTime) + u32CurrentTime + 1U;
    }
    
    return u32ElapsedTime;
}

//===========================================================================
// End of file.
//=========================================================================== 








