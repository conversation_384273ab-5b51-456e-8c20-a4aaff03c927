#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=USART3_RX
Dma.Request1=USART3_TX
Dma.Request2=USART2_RX
Dma.Request3=USART2_TX
Dma.Request4=USART1_RX
Dma.Request5=USART1_TX
Dma.RequestsNb=6
Dma.USART1_RX.4.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.4.Instance=DMA2_Stream2
Dma.USART1_RX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.4.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.4.Mode=DMA_NORMAL
Dma.USART1_RX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.4.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.4.Priority=DMA_PRIORITY_MEDIUM
Dma.USART1_RX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART1_TX.5.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART1_TX.5.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_TX.5.Instance=DMA2_Stream7
Dma.USART1_TX.5.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_TX.5.MemInc=DMA_MINC_ENABLE
Dma.USART1_TX.5.Mode=DMA_NORMAL
Dma.USART1_TX.5.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_TX.5.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_TX.5.Priority=DMA_PRIORITY_MEDIUM
Dma.USART1_TX.5.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.2.Instance=DMA1_Stream5
Dma.USART2_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.2.Mode=DMA_NORMAL
Dma.USART2_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.2.Priority=DMA_PRIORITY_MEDIUM
Dma.USART2_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_TX.3.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART2_TX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_TX.3.Instance=DMA1_Stream6
Dma.USART2_TX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_TX.3.MemInc=DMA_MINC_ENABLE
Dma.USART2_TX.3.Mode=DMA_NORMAL
Dma.USART2_TX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_TX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_TX.3.Priority=DMA_PRIORITY_MEDIUM
Dma.USART2_TX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.0.Instance=DMA1_Stream1
Dma.USART3_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.0.Mode=DMA_NORMAL
Dma.USART3_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.0.Priority=DMA_PRIORITY_MEDIUM
Dma.USART3_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.USART3_TX.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_TX.1.Instance=DMA1_Stream3
Dma.USART3_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_TX.1.MemInc=DMA_MINC_ENABLE
Dma.USART3_TX.1.Mode=DMA_NORMAL
Dma.USART3_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_TX.1.Priority=DMA_PRIORITY_MEDIUM
Dma.USART3_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
FREERTOS.FootprintOK=true
FREERTOS.IPParameters=Tasks01,configUSE_NEWLIB_REENTRANT,FootprintOK
FREERTOS.Tasks01=defaultTask,0,128,StartDefaultTask,Default,NULL,Dynamic,NULL,NULL;wireCommTask,0,512,StartWireCommTask,Default,NULL,Dynamic,NULL,NULL;wirelessComm,0,512,StartWirelessCommTask,Default,NULL,Dynamic,NULL,NULL;KeyHandleTask,0,512,StartKeyHandleTask,Default,NULL,Dynamic,NULL,NULL;LedHandleTask,0,512,StartLedHandleTask,Default,NULL,Dynamic,NULL,NULL
FREERTOS.configUSE_NEWLIB_REENTRANT=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407IGT6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=FREERTOS
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=USART1
Mcu.IP6=USART2
Mcu.IP7=USART3
Mcu.IPNb=8
Mcu.Name=STM32F407I(E-G)Tx
Mcu.Package=LQFP176
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin10=PH12
Mcu.Pin11=PG2
Mcu.Pin12=PG3
Mcu.Pin13=PG4
Mcu.Pin14=PG5
Mcu.Pin15=PG6
Mcu.Pin16=PG7
Mcu.Pin17=PA9
Mcu.Pin18=PA10
Mcu.Pin19=PA13
Mcu.Pin2=PA2
Mcu.Pin20=PH14
Mcu.Pin21=PA14
Mcu.Pin22=PI4
Mcu.Pin23=PI6
Mcu.Pin24=PI7
Mcu.Pin25=VP_FREERTOS_VS_CMSIS_V1
Mcu.Pin26=VP_SYS_VS_tim2
Mcu.Pin3=PA3
Mcu.Pin4=PB2
Mcu.Pin5=PB10
Mcu.Pin6=PB11
Mcu.Pin7=PH9
Mcu.Pin8=PH10
Mcu.Pin9=PH11
Mcu.PinsNb=27
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407IGTx
MxCube.Version=6.8.0
MxDb.Version=DB.6.0.80
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream3_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DMA2_Stream7_IRQn=true\:5\:0\:false\:false\:true\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.PendSV_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:false\:true\:false\:true\:false
NVIC.TIM2_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM2_IRQn
NVIC.TimeBaseIP=TIM2
NVIC.USART1_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:5\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB2.GPIOParameters=GPIO_Speed,GPIO_Label
PB2.GPIO_Label=RS485_DIR
PB2.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB2.Locked=true
PB2.Signal=GPIO_Output
PG2.GPIOParameters=GPIO_Label
PG2.GPIO_Label=LED_4G_WHITE
PG2.Locked=true
PG2.Signal=GPIO_Output
PG3.GPIOParameters=GPIO_Label
PG3.GPIO_Label=LED_BLUETOOTH_WHITE
PG3.Locked=true
PG3.Signal=GPIO_Output
PG4.GPIOParameters=GPIO_Label
PG4.GPIO_Label=LED_WIFI_WHITE
PG4.Locked=true
PG4.Signal=GPIO_Output
PG5.GPIOParameters=GPIO_Label
PG5.GPIO_Label=LED_STATUS_GREEN
PG5.Locked=true
PG5.Signal=GPIO_Output
PG6.GPIOParameters=GPIO_Label
PG6.GPIO_Label=LED_WARNING_RED
PG6.Locked=true
PG6.Signal=GPIO_Output
PG7.GPIOParameters=GPIO_Label
PG7.GPIO_Label=LED_WARNING_YELLOW
PG7.Locked=true
PG7.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PH10.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PH10.GPIO_Label=KEY_OUTPUT_LINE_3
PH10.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH10.Locked=true
PH10.PinState=GPIO_PIN_SET
PH10.Signal=GPIO_Output
PH11.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PH11.GPIO_Label=KEY_OUTPUT_LINE_2
PH11.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH11.Locked=true
PH11.PinState=GPIO_PIN_SET
PH11.Signal=GPIO_Output
PH12.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PH12.GPIO_Label=KEY_OUTPUT_LINE_1
PH12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH12.Locked=true
PH12.PinState=GPIO_PIN_SET
PH12.Signal=GPIO_Output
PH14.GPIOParameters=GPIO_PuPd,GPIO_Label
PH14.GPIO_Label=KEY_INPUT_LINE_1
PH14.GPIO_PuPd=GPIO_PULLUP
PH14.Locked=true
PH14.Signal=GPIO_Input
PH9.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PH9.GPIO_Label=KEY_OUTPUT_LINE_4
PH9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PH9.Locked=true
PH9.PinState=GPIO_PIN_SET
PH9.Signal=GPIO_Output
PI4.GPIOParameters=GPIO_PuPd,GPIO_Label
PI4.GPIO_Label=KEY_INPUT_LINE_2
PI4.GPIO_PuPd=GPIO_PULLUP
PI4.Locked=true
PI4.Signal=GPIO_Input
PI6.GPIOParameters=GPIO_PuPd,GPIO_Label
PI6.GPIO_Label=KEY_INPUT_LINE_3
PI6.GPIO_PuPd=GPIO_PULLUP
PI6.Locked=true
PI6.Signal=GPIO_Input
PI7.GPIOParameters=GPIO_PuPd,GPIO_Label
PI7.GPIO_Label=KEY_INPUT_LINE_4
PI7.GPIO_PuPd=GPIO_PULLUP
PI7.Locked=true
PI7.Signal=GPIO_Input
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407IGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.27.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Control_Panel_Device.ioc
ProjectManager.ProjectName=Control_Panel_Device
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART3_UART_Init-USART3-false-HAL-true,5-MX_USART2_UART_Init-USART2-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=25
RCC.PLLN=336
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=1000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=96000000
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_FREERTOS_VS_CMSIS_V1.Mode=CMSIS_V1
VP_FREERTOS_VS_CMSIS_V1.Signal=FREERTOS_VS_CMSIS_V1
VP_SYS_VS_tim2.Mode=TIM2
VP_SYS_VS_tim2.Signal=SYS_VS_tim2
board=custom
rtos.0.ip=FREERTOS
isbadioc=false
