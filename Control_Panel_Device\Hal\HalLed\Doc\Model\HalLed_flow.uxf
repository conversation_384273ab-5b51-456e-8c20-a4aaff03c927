<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>6</zoom_level>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>300</x>
      <y>24</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>228</x>
      <y>60</y>
      <w>150</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Configure LED pins
4G, Bluetooth, WiFi, Status, Warning</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>120</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>LED Index Loop
u8LedIndex = 0 to MAX-1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>180</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Initialize LED data</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>252</x>
      <y>240</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Set initial LED level</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>300</x>
      <y>324</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>300</x>
      <y>30</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>300</x>
      <y>84</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>300</x>
      <y>144</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>300</x>
      <y>204</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>300</x>
      <y>264</y>
      <w>18</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>204</x>
      <y>282</y>
      <w>114</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Next LED]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>204</x>
      <y>126</y>
      <w>60</w>
      <h>180</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>80.0;10.0;10.0;10.0;10.0;280.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>186</x>
      <y>0</y>
      <w>228</w>
      <h>360</h>
    </coordinates>
    <panel_attributes>HalLed eInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>354</x>
      <y>420</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>456</y>
      <w>90</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Get current time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>312</x>
      <y>516</y>
      <w>90</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Calculate half period</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>300</x>
      <y>576</y>
      <w>114</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>LED Index Loop
Index = 0 to MAX-1</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>348</x>
      <y>684</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>732</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Calculate elapsed time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>348</x>
      <y>840</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>882</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Toggle LED level
Invert current level</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>936</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Update toggle time
Set to current time</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>996</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Set LED physical level</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>354</x>
      <y>1116</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>426</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>480</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>540</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>654</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>702</y>
      <w>66</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[BLINK state]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>756</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>858</y>
      <w>72</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Time to toggle]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>906</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>960</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>1020</y>
      <w>18</w>
      <h>108</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;160.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>234</x>
      <y>1074</y>
      <w>138</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Next LED]</panel_attributes>
    <additional_attributes>10.0;20.0;210.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>234</x>
      <y>582</y>
      <w>78</w>
      <h>516</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>110.0;10.0;10.0;10.0;10.0;840.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>450</x>
      <y>1068</y>
      <w>18</w>
      <h>18</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>186</x>
      <y>390</y>
      <w>342</w>
      <h>768</h>
    </coordinates>
    <panel_attributes>HalLed UpdateBlinkLeds
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>654</x>
      <y>420</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>612</x>
      <y>456</y>
      <w>96</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Determine GPIO level</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>600</x>
      <y>516</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Convert BOOL to PinState</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>600</x>
      <y>576</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Set GPIO output</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>654</x>
      <y>636</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>654</x>
      <y>426</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>654</x>
      <y>480</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>654</x>
      <y>540</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>654</x>
      <y>600</y>
      <w>18</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>576</x>
      <y>390</y>
      <w>168</w>
      <h>276</h>
    </coordinates>
    <panel_attributes>HalLed SetLedLevel
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>630</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if LED blink state</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>600</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>276</x>
      <y>684</y>
      <w>84</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[No bLINK state]</panel_attributes>
    <additional_attributes>120.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>276</x>
      <y>690</y>
      <w>96</w>
      <h>390</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>140.0;630.0;10.0;630.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>306</x>
      <y>786</y>
      <w>108</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check if time to toggle</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>810</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>366</x>
      <y>840</y>
      <w>96</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>[Not time to toggle]</panel_attributes>
    <additional_attributes>140.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>354</x>
      <y>846</y>
      <w>108</w>
      <h>216</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;340.0;160.0;340.0;160.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>438</x>
      <y>0</y>
      <w>234</w>
      <h>240</h>
    </coordinates>
    <panel_attributes>HalLed GetTickMs
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>546</x>
      <y>36</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>486</x>
      <y>66</y>
      <w>132</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Get FreeRTOS tick count</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>546</x>
      <y>42</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>474</x>
      <y>114</y>
      <w>156</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Convert to milliseconds
ticks * portTICK_PERIOD_MS</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>546</x>
      <y>90</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>510</x>
      <y>162</y>
      <w>84</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Return time in ms</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>546</x>
      <y>138</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>546</x>
      <y>216</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>546</x>
      <y>186</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>798</x>
      <y>36</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>738</x>
      <y>66</y>
      <w>138</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Check CurrentTime &gt;= StartTime</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>798</x>
      <y>42</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>792</x>
      <y>114</y>
      <w>24</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>798</x>
      <y>90</y>
      <w>18</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>156</y>
      <w>114</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Normal case calculation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>798</x>
      <y>132</y>
      <w>36</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Yes]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>882</x>
      <y>156</y>
      <w>120</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>Overflow case calculation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>810</x>
      <y>114</y>
      <w>144</w>
      <h>54</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[No]</panel_attributes>
    <additional_attributes>220.0;70.0;220.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>750</x>
      <y>234</y>
      <w>108</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Return ElapsedTime</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>798</x>
      <y>180</y>
      <w>18</w>
      <h>66</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>798</x>
      <y>282</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>798</x>
      <y>252</y>
      <w>18</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>702</x>
      <y>0</y>
      <w>318</w>
      <h>312</h>
    </coordinates>
    <panel_attributes>HalLed GetElapsedTime
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>798</x>
      <y>180</y>
      <w>156</w>
      <h>42</h>
    </coordinates>
    <panel_attributes/>
    <additional_attributes>10.0;50.0;240.0;50.0;240.0;10.0</additional_attributes>
  </element>
</diagram>
