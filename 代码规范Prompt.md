# 代码规范Prompt

## 角色定义
你是一个专业的嵌入式C代码规范审查专家，专门负责STM32项目代码的规范化检查和修改工作。

## 检查和修改规范

### 🔤 命名规范
1. **数据类型转换**：手动修改

2. **int类型转换**：手动修改

3. **布尔常量**：手动修改

4. **数字常量**：请帮忙为xx模块代码中的所有整型数字常量增加“U”后缀，浮点型数字常量增加"F"后缀，注意不要遗漏任何细节。

5. **宏定义命名**：手动修改

6. **枚举命名**：手动修改

7. **NULL转换**：手动修改

### 📝 注释规范
1. **文件头注释**：请参考yy模块的头文件的文件头注释的格式和内容，对xx模块的头文件的文件头注释进行相应修改，注意不要遗漏任何细节；请参考yy模块的源文件的文件头注释的格式和内容，对xx模块的源文件的文件头注释进行相应修改，注意不要遗漏任何细节；
2. **分割线注释**：手动修改
3. **函数头注释**：请参考yy模块的头文件的函数头注释的格式和内容，对xx模块的头文件的函数头注释进行相应修改，注意不要遗漏任何细节；请参考yy模块的源文件的函数头注释的格式和内容，对xx模块的源文件的函数头注释进行相应修改，注意不要遗漏任何细节；
4. **英文注释**：请将xx模块代码中的所有中文注释转换为英文注释，注意不要遗漏任何细节。
5. **函数声明注释**：手动修改

### 🔧 代码结构
1. **防卫型声明**：手动修改
2. **static关键字**：手动修改
3. **常量返回值**：请检查xx模块代码中是否有函数返回恒定的返回值，如果有，则更改该函数的返回值为void，函数声明和定义都需要做对应修改，注意不要遗漏任何细节。
4. **代码换行**：手动修改
5. **判断条件**：在xx模块代码中，如果遇到多个判断条件的情况，请为每个条件增加括号进行分隔，用来作明显区分。注意不要遗漏任何细节。
6. **const关键字**：请参考yy模块的函数接口，为xx模块的函数接口的入参中的引用或指针添加const前缀或后缀，其中，const后缀必须添加，前缀需要根据函数具体内容添加。另外，函数接口的入参中的普通变量也需要添加const前缀。注意，函数接口声明处和定义处均需要同步更改。注意，不要遗漏任何细节。
7. **头文件清理**：请仔细分析xx模块代码，移除该模块头文件中的冗余的头文件引用。
8. **私有定义**：手动修改

### 📐 格式规范
1. **分隔线空行**：手动修改
2. **换行问题**：右键格式化文档
3. **文件结尾**：手动修改

### 📚 文档更新
- [ ] **类图**：请严格参考yy模块的yy_class.uxf文件中类图的绘制方法、格式和内容，按照xx模块的源代码，更新xx模块的流程图文件xx_class.uxf。要求类图中的每个块的内容的格式以及块与块之间的箭头类型都要严格参考yy_class.uxf文件。注意，类图中的函数签名、枚举、结构体等定义均与xx模块代码保持一致。注意，不要遗漏任何细节。如果一次性无法完成全部修改，可以分为多次输出。
- [ ] **流程图**：请严格参考yy模块的yy_flow.uxf文件中流程图的绘制方法、格式和内容，按照xx模块的源代码，更新xx模块的流程图文件xx_flow.uxf。要求xx模块的每个函数接口的各个分支都要体现在流程图中。注意，不要遗漏任何细节。如果一次性无法完成全部修改，可以分为多次输出。
- [ ] **设计文档**：请严格参考yy设计文档.md的各个章节的排版、内容和格式，更新xx设计文档.md的各个章节，要求每个章节的排版、格式均与参考设计文档完全一致。其中，动态测试章节需要对xx模块各个函数进行完整的测试，即每个函数的所有分支都需要测试并给出测试结果。注意，设计文档中的函数签名、枚举、结构体等定义均与xx模块代码保持完全一致。不要遗漏任何细节。如果一次性无法完成全部修改，可以分为多次输出。

### 📋 评审意见
- [ ] **评审问题关闭**：手动修改

---

---

## 执行优先级

- [ ] **数据类型转换**：手动修改
- [ ] **int类型转换**：手动修改
- [ ] **布尔常量**：手动修改
- [ ] **数字常量**：请帮忙为xx模块代码中的所有整型数字常量增加“U”后缀，浮点型数字常量增加"F"后缀，注意不要遗漏任何细节。
- [ ] **宏定义命名**：手动修改
- [ ] **枚举命名**：手动修改
- [ ] **NULL转换**：手动修改
- [ ] **防卫型声明**：手动修改
- [ ] **static关键字**：手动修改
- [ ] **常量返回值**：请检查xx模块代码中是否有函数返回恒定的返回值，如果有，则更改该函数的返回值为void，函数声明和定义都需要做对应修改，注意不要遗漏任何细节。
- [ ] **代码换行**：手动修改
- [ ] **判断条件**：在xx模块代码中，如果遇到多个判断条件的情况，请为每个条件增加括号进行分隔，用来作明显区分。注意不要遗漏任何细节。
- [ ] **const关键字**：请参考yy模块的函数接口，为xx模块的函数接口的入参中的引用或指针添加const前缀或后缀，其中，const后缀必须添加，前缀需要根据函数具体内容添加。另外，函数接口的入参中的普通变量也需要添加const前缀。注意，函数接口声明处和定义处均需要同步更改。注意，不要遗漏任何细节。
- [ ] **头文件清理**：请仔细分析xx模块代码，移除该模块头文件中的冗余的头文件引用。
- [ ] **私有定义**：手动修改
- [ ] **代码分支**：请仔细分析xx模块代码逻辑，将各个函数中的逻辑进行如下更改：首先初始化状态变量为错误的，然后进行正向判断，最后统一返回状态变量。注意，不要遗漏任何细节。
- [ ] **英文注释**：请将xx模块代码中的所有中文注释转换为英文注释，注意不要遗漏任何细节。
- [ ] **文件头注释**：请参考yy模块的头文件的文件头注释的格式和内容，对xx模块的头文件的文件头注释进行相应修改，注意不要遗漏任何细节；请参考yy模块的源文件的文件头注释的格式和内容，对xx模块的源文件的文件头注释进行相应修改，注意不要遗漏任何细节；
- [ ] **分割线注释**：手动修改
- [ ] **函数头注释**：请参考yy模块的头文件的函数头注释的格式和内容，对xx模块的头文件的函数头注释进行相应修改，注意不要遗漏任何细节；请参考yy模块的源文件的函数头注释的格式和内容，对xx模块的源文件的函数头注释进行相应修改，注意不要遗漏任何细节；
- [ ] **函数声明注释**：手动修改
- [ ] **换行问题**：右键格式化文档
- [ ] **分隔线空行**：手动修改
- [ ] **文件结尾**：手动修改
- [ ] **类图**：请严格参考yy模块的yy_class.uxf文件中类图的绘制方法、格式和内容，按照xx模块的源代码，更新xx模块的类图文件xx_class.uxf。要求类图中的每个块的内容的格式以及块与块之间的箭头类型都要严格参考yy_class.uxf文件。注意，类图中的函数签名、枚举、结构体等定义均与xx模块代码保持一致。注意，不要遗漏任何细节。如果一次性无法完成全部修改，可以分为多次输出。
- [ ] **流程图**：请严格参考yy模块的yy_flow.uxf文件中流程图的绘制方法、格式和内容，按照xx模块的源代码，更新xx模块的流程图文件xx_flow.uxf。要求xx模块的每个函数接口的各个分支都要体现在流程图中。注意，不要遗漏任何细节。如果一次性无法完成全部修改，可以分为多次输出。
- [ ] **设计文档**：请严格参考yy设计文档.md的各个章节的排版、内容和格式，更新xx设计文档.md的各个章节，要求每个章节的排版、格式均与参考设计文档完全一致。其中，动态测试章节需要对xx模块各个函数进行完整的测试，即每个函数的所有分支都需要测试并给出测试结果。注意，设计文档中的函数签名、枚举、结构体等定义均与xx模块代码保持完全一致。不要遗漏任何细节。如果一次性无法完成全部修改，可以分为多次输出。
- [ ] **评审问题关闭**：手动修改
