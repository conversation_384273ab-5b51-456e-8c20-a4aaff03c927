/*******************************************************************************
 * Size: 30 px
 * Bpp: 4
 * Opts: --bpp 4 --size 30 --font F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/LVGL_UI/STM32CubeIDE_FreeRTOS_LVGL_UI8/assets/Dengb.ttf -o F:/1-TRiED/1-Project/3-Control_Panel/2-Output/1-Code/LVGL_UI/STM32CubeIDE_FreeRTOS_LVGL_UI8/assets\ui_font_FontDengXianBold48.c --format lvgl -r 0x20-0x7f --symbols 故障号报警内容告 --no-compress --no-prefilter
 ******************************************************************************/

#include "../ui.h"

#ifndef UI_FONT_FONTDENGXIANBOLD48
#define UI_FONT_FONTDENGXIANBOLD48 1
#endif

#if UI_FONT_FONTDENGXIANBOLD48

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x3f, 0xff, 0x53, 0xff, 0xf5, 0x2f, 0xff, 0x42,
    0xff, 0xf4, 0x1f, 0xff, 0x31, 0xff, 0xf3, 0xf,
    0xff, 0x20, 0xff, 0xf2, 0xf, 0xff, 0x10, 0xff,
    0xf1, 0xf, 0xff, 0x0, 0xef, 0xf0, 0xe, 0xff,
    0x0, 0xdf, 0xf0, 0x6, 0x76, 0x0, 0x0, 0x0,
    0x16, 0x66, 0x14, 0xff, 0xf4, 0x4f, 0xff, 0x44,
    0xff, 0xf4,

    /* U+0022 "\"" */
    0xf, 0xff, 0x40, 0x2f, 0xff, 0x10, 0xef, 0xf3,
    0x1, 0xff, 0xf0, 0xe, 0xff, 0x20, 0x1f, 0xff,
    0x0, 0xdf, 0xf1, 0x0, 0xff, 0xf0, 0xc, 0xff,
    0x0, 0xf, 0xfe, 0x0, 0xbf, 0xf0, 0x0, 0xef,
    0xd0, 0x2, 0x43, 0x0, 0x3, 0x43, 0x0,

    /* U+0023 "#" */
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0xd, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x0, 0x6, 0xf8, 0x0,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0x0, 0xb, 0xf4,
    0x0, 0x0, 0xdf, 0x10, 0x0, 0xd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb, 0xdd,
    0xef, 0xfd, 0xdd, 0xde, 0xfe, 0xdd, 0xd0, 0x0,
    0x0, 0x5f, 0x90, 0x0, 0x7, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x60, 0x0, 0xb, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x30, 0x0, 0xe, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x1f,
    0xd0, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x0, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0x8d, 0xde, 0xff, 0xdd,
    0xdd, 0xef, 0xed, 0xdd, 0x30, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0xd,
    0xf1, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xe0, 0x0, 0x2, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xb0, 0x0, 0x6, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x80, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x50, 0x0, 0xc, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x10, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6b, 0xef, 0xff, 0xec, 0x60, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0xaf, 0xfe, 0x7a, 0xfa, 0xaf, 0xff, 0x80,
    0x1, 0xff, 0xf3, 0x7, 0xf7, 0x7, 0xff, 0xf0,
    0x4, 0xff, 0xe0, 0x7, 0xf7, 0x0, 0xff, 0xd3,
    0x4, 0xff, 0xe0, 0x7, 0xf7, 0x0, 0x20, 0x0,
    0x2, 0xff, 0xf3, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfe, 0x57, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xfb, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0xfe, 0x81, 0x0,
    0x0, 0x0, 0x4, 0x9e, 0xff, 0xff, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x7, 0xfa, 0xaf, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x7, 0xf7, 0x2, 0xef, 0xf7,
    0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x7f, 0xfa,
    0x5, 0x8b, 0x10, 0x7, 0xf7, 0x0, 0x5f, 0xfb,
    0xe, 0xff, 0x50, 0x7, 0xf7, 0x0, 0x7f, 0xfa,
    0x9, 0xff, 0xe1, 0x7, 0xf7, 0x1, 0xef, 0xf6,
    0x1, 0xff, 0xfe, 0x8a, 0xfa, 0x8e, 0xff, 0xe0,
    0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x0, 0x1, 0x7c, 0xef, 0xff, 0xec, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x2, 0xcf, 0xa0, 0x0, 0x0, 0xce, 0x0, 0x0,
    0xcf, 0xdf, 0x90, 0x0, 0x2f, 0x80, 0x0, 0x2f,
    0xc0, 0xef, 0x0, 0x8, 0xf2, 0x0, 0x5, 0xf9,
    0xc, 0xf2, 0x0, 0xec, 0x0, 0x0, 0x7f, 0x80,
    0xbf, 0x40, 0x4f, 0x60, 0x0, 0x8, 0xf8, 0xa,
    0xf5, 0xa, 0xf1, 0x0, 0x0, 0x8f, 0x80, 0xaf,
    0x51, 0xfa, 0x0, 0x0, 0x7, 0xf8, 0xb, 0xf4,
    0x6f, 0x48, 0xda, 0x10, 0x5f, 0x90, 0xcf, 0x2c,
    0xe7, 0xff, 0xfa, 0x1, 0xfd, 0x1f, 0xe2, 0xf8,
    0xef, 0x2d, 0xf1, 0xb, 0xff, 0xf8, 0x8f, 0x4f,
    0xd0, 0xaf, 0x40, 0x1c, 0xfa, 0xe, 0xb4, 0xfb,
    0x8, 0xf6, 0x0, 0x0, 0x5, 0xf5, 0x5f, 0xa0,
    0x8f, 0x70, 0x0, 0x0, 0xbf, 0x5, 0xfa, 0x7,
    0xf8, 0x0, 0x0, 0x1f, 0x90, 0x5f, 0xa0, 0x7f,
    0x70, 0x0, 0x7, 0xf3, 0x4, 0xfb, 0x8, 0xf6,
    0x0, 0x0, 0xdd, 0x0, 0x2f, 0xc0, 0x9f, 0x40,
    0x0, 0x3f, 0x70, 0x0, 0xef, 0xc, 0xf1, 0x0,
    0x9, 0xf1, 0x0, 0x7, 0xfd, 0xfa, 0x0, 0x0,
    0xfb, 0x0, 0x0, 0xa, 0xfb, 0x10,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x18, 0xcf, 0xfe, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xb3, 0x26, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf0, 0x0,
    0x9, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfc, 0x0, 0x0, 0x8f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xe0, 0x0, 0xd,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x50, 0x1b, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xfe, 0x8f, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xfe, 0x60, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x19, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x4e, 0xff, 0xfb, 0xff,
    0xf2, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0, 0x2f,
    0xff, 0xa1, 0xa, 0xff, 0xe2, 0x0, 0x2, 0xff,
    0xe0, 0x0, 0xb, 0xff, 0xb0, 0x0, 0xb, 0xff,
    0xe2, 0x0, 0xaf, 0xf8, 0x0, 0x0, 0xff, 0xf5,
    0x0, 0x0, 0xb, 0xff, 0xe3, 0x3f, 0xff, 0x10,
    0x0, 0x1f, 0xff, 0x30, 0x0, 0x0, 0xb, 0xff,
    0xfe, 0xff, 0x70, 0x0, 0x0, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0xc, 0xff, 0xf5, 0x0, 0x0, 0x3, 0xbf, 0xff,
    0xfc, 0x30, 0x0, 0x0, 0x3f, 0xff, 0xfd, 0xba,
    0xbe, 0xff, 0xff, 0xff, 0xff, 0xca, 0xb2, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x33, 0xef,
    0xff, 0xff, 0x20, 0x0, 0x17, 0xce, 0xff, 0xec,
    0x83, 0x0, 0x0, 0x7c, 0xff, 0xd1,

    /* U+0027 "'" */
    0xcf, 0xf8, 0xbf, 0xf7, 0xaf, 0xf6, 0x9f, 0xf5,
    0x8f, 0xf4, 0x7f, 0xf3, 0x14, 0x40,

    /* U+0028 "(" */
    0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0xd, 0xff,
    0x50, 0x0, 0x8, 0xff, 0xb0, 0x0, 0x2, 0xff,
    0xf2, 0x0, 0x0, 0xaf, 0xf9, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x8, 0xff, 0xb0, 0x0, 0x0,
    0xdf, 0xf5, 0x0, 0x0, 0x2f, 0xff, 0x10, 0x0,
    0x5, 0xff, 0xe0, 0x0, 0x0, 0x7f, 0xfb, 0x0,
    0x0, 0x9, 0xff, 0xa0, 0x0, 0x0, 0x9f, 0xf9,
    0x0, 0x0, 0xa, 0xff, 0x80, 0x0, 0x0, 0x9f,
    0xf9, 0x0, 0x0, 0x9, 0xff, 0xa0, 0x0, 0x0,
    0x7f, 0xfb, 0x0, 0x0, 0x5, 0xff, 0xe0, 0x0,
    0x0, 0x2f, 0xff, 0x10, 0x0, 0x0, 0xdf, 0xf5,
    0x0, 0x0, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x2f,
    0xff, 0x10, 0x0, 0x0, 0xaf, 0xf9, 0x0, 0x0,
    0x2, 0xff, 0xf2, 0x0, 0x0, 0x8, 0xff, 0xb0,
    0x0, 0x0, 0xd, 0xff, 0x50, 0x0, 0x0, 0x3f,
    0xff, 0x20,

    /* U+0029 ")" */
    0x2f, 0xff, 0x20, 0x0, 0x0, 0x6f, 0xfc, 0x0,
    0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0, 0x2, 0xff,
    0xf1, 0x0, 0x0, 0xa, 0xff, 0x90, 0x0, 0x0,
    0x2f, 0xff, 0x10, 0x0, 0x0, 0xbf, 0xf7, 0x0,
    0x0, 0x6, 0xff, 0xc0, 0x0, 0x0, 0x2f, 0xff,
    0x10, 0x0, 0x0, 0xef, 0xf4, 0x0, 0x0, 0xc,
    0xff, 0x60, 0x0, 0x0, 0xaf, 0xf8, 0x0, 0x0,
    0x9, 0xff, 0x90, 0x0, 0x0, 0x9f, 0xf9, 0x0,
    0x0, 0x9, 0xff, 0x90, 0x0, 0x0, 0xaf, 0xf8,
    0x0, 0x0, 0xc, 0xff, 0x60, 0x0, 0x0, 0xef,
    0xf4, 0x0, 0x0, 0x1f, 0xff, 0x10, 0x0, 0x6,
    0xff, 0xd0, 0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0,
    0x2f, 0xff, 0x10, 0x0, 0x9, 0xff, 0x90, 0x0,
    0x2, 0xff, 0xf1, 0x0, 0x0, 0xcf, 0xf7, 0x0,
    0x0, 0x6f, 0xfd, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xb, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xfa, 0x0, 0x0, 0x0, 0xda, 0x40, 0x8f, 0x80,
    0x4a, 0xd0, 0x3f, 0xff, 0xed, 0xfd, 0xef, 0xff,
    0x41, 0x6a, 0xef, 0xff, 0xff, 0xea, 0x61, 0x0,
    0x0, 0x7f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x3f,
    0xf7, 0xff, 0x20, 0x0, 0x0, 0x1e, 0xfa, 0xa,
    0xfd, 0x0, 0x0, 0xb, 0xff, 0x10, 0x2f, 0xfa,
    0x0, 0x0, 0x3c, 0x70, 0x0, 0x8b, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1a, 0xaa, 0xaa, 0xaf, 0xfe, 0xaa, 0xaa, 0xaa,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xfc, 0x0, 0x0, 0x0,

    /* U+002C "," */
    0x1c, 0xcc, 0x52, 0xff, 0xf6, 0x2f, 0xff, 0x62,
    0xff, 0xf6, 0x0, 0x9f, 0x40, 0xd, 0xf2, 0x4,
    0xfd, 0x0, 0xdf, 0x50,

    /* U+002D "-" */
    0xbf, 0xff, 0xff, 0xff, 0xbc, 0xff, 0xff, 0xff,
    0xfb, 0xcf, 0xff, 0xff, 0xff, 0xb0,

    /* U+002E "." */
    0x1c, 0xcc, 0x42, 0xff, 0xf6, 0x2f, 0xff, 0x62,
    0xff, 0xf6,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x18, 0x87, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0030 "0" */
    0x0, 0x1, 0x8c, 0xff, 0xeb, 0x50, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xef, 0xff, 0xca, 0xdf, 0xff, 0xa0, 0x0, 0x9f,
    0xfe, 0x30, 0x0, 0x7f, 0xff, 0x40, 0xf, 0xff,
    0x60, 0x0, 0x0, 0xcf, 0xfa, 0x4, 0xff, 0xf1,
    0x0, 0x0, 0x6, 0xff, 0xf0, 0x7f, 0xfd, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x29, 0xff, 0xb0, 0x0,
    0x0, 0x1, 0xff, 0xf4, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x5b, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xff, 0xf6, 0xbf, 0xfa, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x6a, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xff, 0xf5, 0x9f, 0xfc, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x47, 0xff, 0xe0, 0x0, 0x0, 0x3, 0xff,
    0xf1, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x7f, 0xfe,
    0x0, 0xef, 0xf7, 0x0, 0x0, 0xd, 0xff, 0x90,
    0x8, 0xff, 0xf3, 0x0, 0x9, 0xff, 0xf2, 0x0,
    0x1e, 0xff, 0xfc, 0xbe, 0xff, 0xf8, 0x0, 0x0,
    0x2d, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x7, 0xce, 0xfe, 0xa4, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x4d, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x5, 0xef,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x5f, 0xff, 0x89,
    0xff, 0xd0, 0x0, 0x0, 0x6f, 0xc3, 0x8, 0xff,
    0xd0, 0x0, 0x0, 0x56, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0, 0x9a,
    0xaa, 0xad, 0xff, 0xfa, 0xaa, 0xa3, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5,

    /* U+0032 "2" */
    0x0, 0x2, 0x8d, 0xff, 0xec, 0x71, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x4,
    0xff, 0xff, 0xdc, 0xef, 0xff, 0xf2, 0x0, 0xdf,
    0xfe, 0x20, 0x0, 0x7f, 0xff, 0x90, 0x3f, 0xff,
    0x40, 0x0, 0x0, 0xbf, 0xfd, 0x3, 0x99, 0x90,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xfd, 0xcc, 0xcc, 0xcc, 0xcc, 0xc3, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+0033 "3" */
    0x0, 0x3, 0x9d, 0xff, 0xec, 0x71, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x9,
    0xff, 0xff, 0xcb, 0xef, 0xff, 0xf1, 0x2, 0xff,
    0xfa, 0x10, 0x0, 0x7f, 0xff, 0x70, 0x6f, 0xff,
    0x0, 0x0, 0x0, 0xdf, 0xfa, 0x0, 0x12, 0x30,
    0x0, 0x0, 0xb, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xdf, 0xfc, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xe7, 0x20, 0x0, 0x0, 0x0, 0x2c, 0xcd, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf3, 0xac, 0xe8, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x3b, 0xff, 0xd0, 0x0, 0x0, 0x9, 0xff, 0xf1,
    0x6f, 0xff, 0xa1, 0x0, 0x6, 0xff, 0xfd, 0x0,
    0xcf, 0xff, 0xfc, 0xce, 0xff, 0xff, 0x40, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x5a, 0xdf, 0xfe, 0xc8, 0x10, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xfd, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xe5, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x54, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x3f, 0xfb, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0xdf, 0xf1, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0x8, 0xff, 0x60, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0x2f, 0xfc, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0xcf, 0xf2, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x7, 0xff, 0x70, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x2f, 0xfd, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4b, 0xbb, 0xbb, 0xbb, 0xbc, 0xff, 0xfb, 0xbb,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf0, 0x0,

    /* U+0035 "5" */
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x9,
    0xff, 0xdb, 0xbb, 0xbb, 0xbb, 0xb2, 0x0, 0xaf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x43,
    0x8a, 0xa9, 0x40, 0x0, 0x0, 0xef, 0xfc, 0xff,
    0xff, 0xff, 0xd2, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0xff, 0xfe, 0x61, 0x2,
    0xaf, 0xff, 0xa0, 0x6, 0x66, 0x20, 0x0, 0x0,
    0xcf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf5, 0x24, 0x65, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x38, 0xff, 0xf0, 0x0, 0x0, 0xa, 0xff, 0xf0,
    0x3f, 0xff, 0xa0, 0x0, 0x6, 0xff, 0xf9, 0x0,
    0xaf, 0xff, 0xfb, 0xbe, 0xff, 0xfd, 0x10, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0,
    0x4a, 0xef, 0xfe, 0xa5, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x39, 0xdf, 0xfd, 0x93, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x8f, 0xff, 0xeb, 0xbf, 0xff, 0xf4, 0x0, 0x2f,
    0xff, 0x80, 0x0, 0x1c, 0xff, 0xb0, 0xa, 0xff,
    0xa0, 0x0, 0x0, 0x13, 0x10, 0x0, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x0,
    0x36, 0x76, 0x20, 0x0, 0x5, 0xff, 0xd2, 0xcf,
    0xff, 0xff, 0xb1, 0x0, 0x7f, 0xfd, 0xdf, 0xff,
    0xff, 0xff, 0xe1, 0x8, 0xff, 0xff, 0xa2, 0x2,
    0x9f, 0xff, 0xa0, 0x9f, 0xff, 0xa0, 0x0, 0x0,
    0xaf, 0xff, 0x18, 0xff, 0xf3, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x7f, 0xff, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x65, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xff,
    0xf6, 0x1f, 0xff, 0x40, 0x0, 0x0, 0x1f, 0xff,
    0x40, 0xcf, 0xfb, 0x0, 0x0, 0x7, 0xff, 0xf1,
    0x6, 0xff, 0xf7, 0x0, 0x3, 0xff, 0xfb, 0x0,
    0xb, 0xff, 0xfd, 0xac, 0xff, 0xff, 0x20, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xfe, 0x40, 0x0, 0x0,
    0x5, 0xbe, 0xff, 0xc8, 0x10, 0x0,

    /* U+0037 "7" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x38,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x6c,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcf, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x60, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x4, 0xad, 0xff, 0xec, 0x82, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xa,
    0xff, 0xfc, 0x87, 0x9e, 0xff, 0xf4, 0x1, 0xff,
    0xf9, 0x0, 0x0, 0x1e, 0xff, 0xb0, 0x3f, 0xff,
    0x30, 0x0, 0x0, 0xaf, 0xfd, 0x1, 0xff, 0xf4,
    0x0, 0x0, 0xa, 0xff, 0xb0, 0xb, 0xff, 0xa0,
    0x0, 0x2, 0xff, 0xf5, 0x0, 0x2d, 0xff, 0xc8,
    0x79, 0xff, 0xf9, 0x0, 0x0, 0x17, 0xff, 0xff,
    0xff, 0xc4, 0x0, 0x0, 0x6, 0xef, 0xff, 0xff,
    0xff, 0xc3, 0x0, 0x8, 0xff, 0xf6, 0x20, 0x3a,
    0xff, 0xf4, 0x3, 0xff, 0xf5, 0x0, 0x0, 0xa,
    0xff, 0xd0, 0x8f, 0xfe, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x3b, 0xff, 0xb0, 0x0, 0x0, 0x2, 0xff,
    0xf5, 0xbf, 0xfd, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x58, 0xff, 0xf1, 0x0, 0x0, 0x6, 0xff, 0xf3,
    0x3f, 0xff, 0x90, 0x0, 0x1, 0xdf, 0xfe, 0x0,
    0xaf, 0xff, 0xd8, 0x79, 0xef, 0xff, 0x50, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x4a, 0xdf, 0xfe, 0xc8, 0x20, 0x0,

    /* U+0039 "9" */
    0x0, 0x4, 0xad, 0xff, 0xda, 0x30, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xb,
    0xff, 0xfe, 0xba, 0xef, 0xff, 0x70, 0x4, 0xff,
    0xfa, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x9f, 0xfe,
    0x0, 0x0, 0x0, 0xef, 0xf7, 0xb, 0xff, 0xb0,
    0x0, 0x0, 0x8, 0xff, 0xc0, 0xcf, 0xfa, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xb, 0xff, 0xc0, 0x0,
    0x0, 0x6, 0xff, 0xf1, 0x7f, 0xff, 0x30, 0x0,
    0x0, 0xdf, 0xff, 0x22, 0xff, 0xfe, 0x51, 0x14,
    0xcf, 0xff, 0xf3, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0x30, 0x6, 0xff, 0xff, 0xff, 0x93,
    0xff, 0xf2, 0x0, 0x1, 0x57, 0x86, 0x10, 0x4f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfa,
    0x3, 0x9b, 0xb0, 0x0, 0x0, 0x3f, 0xff, 0x40,
    0x3f, 0xff, 0x80, 0x0, 0x2d, 0xff, 0xd0, 0x0,
    0xaf, 0xff, 0xda, 0xbf, 0xff, 0xf3, 0x0, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x6b, 0xef, 0xec, 0x71, 0x0, 0x0,

    /* U+003A ":" */
    0x2f, 0xff, 0x62, 0xff, 0xf6, 0x2f, 0xff, 0x61,
    0x88, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x88, 0x83, 0x2f, 0xff,
    0x62, 0xff, 0xf6, 0x2f, 0xff, 0x60,

    /* U+003B ";" */
    0x2f, 0xff, 0x62, 0xff, 0xf6, 0x2f, 0xff, 0x61,
    0x88, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x88, 0x83, 0x2f, 0xff,
    0x62, 0xff, 0xf6, 0x2f, 0xff, 0x50, 0x9, 0xf4,
    0x0, 0xdf, 0x20, 0x4f, 0xd0, 0xd, 0xf5, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x16, 0xdf,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x1, 0x7d, 0xff, 0xff, 0xe8,
    0x0, 0x0, 0x5, 0xbf, 0xff, 0xff, 0xb5, 0x0,
    0x0, 0x28, 0xef, 0xff, 0xfe, 0x82, 0x0, 0x0,
    0x1c, 0xff, 0xff, 0xfb, 0x50, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xe8, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xfc, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xef, 0xff, 0xfe, 0x92, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xbf, 0xff, 0xff, 0xc6, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7d, 0xff, 0xff, 0xf9, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xcf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,

    /* U+003E ">" */
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xe9, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x16, 0xdf, 0xff, 0xff, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x9f, 0xff, 0xff, 0xd7, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6c, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x3, 0xaf, 0xff, 0xff, 0xd7,
    0x0, 0x0, 0x17, 0xdf, 0xff, 0xff, 0x93, 0x0,
    0x0, 0x4a, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xe8, 0x20, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xb5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x4, 0xae, 0xff, 0xc8, 0x10, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xfe, 0x40, 0x8, 0xff, 0xfe,
    0xcd, 0xff, 0xfe, 0x11, 0xff, 0xfa, 0x0, 0x5,
    0xff, 0xf7, 0x6f, 0xfe, 0x0, 0x0, 0xb, 0xff,
    0xa9, 0xff, 0xa0, 0x0, 0x0, 0x9f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x3, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0xdf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x13, 0x31, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x56, 0x64, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x6a, 0xde, 0xff,
    0xeb, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xb6, 0x31, 0x1, 0x25, 0xaf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xfa, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0x90, 0x0, 0x0, 0x0,
    0xcf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x60, 0x0, 0x0, 0xaf, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfe,
    0x0, 0x0, 0x5f, 0xf5, 0x0, 0x0, 0x29, 0xdf,
    0xea, 0x10, 0xdf, 0xc0, 0x1f, 0xf6, 0x0, 0xd,
    0xfa, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xfe, 0x2f,
    0xf8, 0x0, 0x9f, 0xc0, 0x5, 0xff, 0x20, 0x0,
    0x5f, 0xfc, 0x20, 0x7, 0xfe, 0xff, 0x50, 0x5,
    0xff, 0x0, 0xbf, 0xb0, 0x0, 0x1f, 0xfc, 0x0,
    0x0, 0x8, 0xff, 0xf1, 0x0, 0x2f, 0xf2, 0xf,
    0xf6, 0x0, 0xa, 0xff, 0x20, 0x0, 0x0, 0x3f,
    0xfe, 0x0, 0x1, 0xff, 0x33, 0xff, 0x20, 0x1,
    0xff, 0xa0, 0x0, 0x0, 0x2, 0xff, 0xa0, 0x0,
    0x1f, 0xf3, 0x6f, 0xf0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x0, 0x4f, 0xf7, 0x0, 0x2, 0xff, 0x17,
    0xfe, 0x0, 0x9, 0xff, 0x20, 0x0, 0x0, 0x7,
    0xff, 0x30, 0x0, 0x4f, 0xf0, 0x8f, 0xd0, 0x0,
    0xaf, 0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0,
    0x7, 0xfc, 0x7, 0xfe, 0x0, 0xb, 0xff, 0x0,
    0x0, 0x0, 0x2f, 0xfd, 0x0, 0x0, 0xcf, 0x80,
    0x6f, 0xf0, 0x0, 0xaf, 0xf1, 0x0, 0x0, 0xa,
    0xff, 0xa0, 0x0, 0x4f, 0xf2, 0x3, 0xff, 0x30,
    0x6, 0xff, 0x70, 0x0, 0x6, 0xfc, 0xf8, 0x0,
    0xd, 0xf9, 0x0, 0xe, 0xf7, 0x0, 0xe, 0xfe,
    0x40, 0x29, 0xfa, 0x7f, 0xb0, 0x3c, 0xfd, 0x10,
    0x0, 0x9f, 0xe0, 0x0, 0x3f, 0xff, 0xff, 0xfa,
    0x3, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x2, 0xff,
    0x90, 0x0, 0x29, 0xdd, 0xa4, 0x0, 0x4, 0xcd,
    0xc6, 0x0, 0x0, 0x0, 0x8, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xc2, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf9, 0x41, 0x0, 0x12, 0x47, 0xbf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdf, 0xff,
    0xfe, 0xc9, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xef, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xfb, 0x9f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf6, 0x4f, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf1, 0xe,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xb0, 0x9, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x50, 0x3, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0xdf, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0, 0x0,
    0x7f, 0xfc, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf3,
    0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0, 0x7,
    0xff, 0xe0, 0x0, 0x0, 0xc, 0xff, 0x80, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x9f, 0xff, 0xaa,
    0xaa, 0xaa, 0xaa, 0xef, 0xfb, 0x0, 0x0, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x10,
    0x6, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x70, 0xc, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xd0, 0x2f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf4, 0x8f, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfa,

    /* U+0042 "B" */
    0x7f, 0xff, 0xff, 0xff, 0xdb, 0x71, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x7f, 0xff, 0x98, 0x8a, 0xdf, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x6, 0xff, 0xfa, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0xdf, 0xfc, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0xcf, 0xfb, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x1, 0xff, 0xf7, 0x0,
    0x7f, 0xff, 0x20, 0x2, 0x6e, 0xff, 0xc0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xe8, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xc8, 0x20, 0x0,
    0x7f, 0xff, 0x87, 0x77, 0xae, 0xff, 0xf6, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x9f, 0xff, 0x40,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0xd, 0xff, 0xb0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0xe, 0xff, 0xd0,
    0x7f, 0xff, 0x20, 0x0, 0x1, 0xaf, 0xff, 0x80,
    0x7f, 0xff, 0x98, 0x89, 0xbf, 0xff, 0xfe, 0x10,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xed, 0x94, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xec, 0x71, 0x0,
    0x0, 0x0, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xed, 0xff, 0xff,
    0xf6, 0x0, 0xc, 0xff, 0xfa, 0x20, 0x0, 0x5e,
    0xff, 0xf2, 0x6, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x90, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x9c, 0x72, 0x1f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0, 0xc,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0xa, 0xfc, 0x50,
    0x5f, 0xff, 0xb0, 0x0, 0x0, 0x4, 0xff, 0xf9,
    0x0, 0xbf, 0xff, 0xb2, 0x0, 0x6, 0xff, 0xff,
    0x10, 0x1, 0xdf, 0xff, 0xfe, 0xdf, 0xff, 0xff,
    0x40, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x49, 0xdf, 0xfe, 0xb6,
    0x0, 0x0,

    /* U+0044 "D" */
    0x6f, 0xff, 0xff, 0xff, 0xed, 0xb7, 0x20, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb2, 0x0, 0x0, 0x6f, 0xff, 0xdc, 0xcc, 0xdf,
    0xff, 0xff, 0xf4, 0x0, 0x6, 0xff, 0xf2, 0x0,
    0x0, 0x4, 0xbf, 0xff, 0xf3, 0x0, 0x6f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xc0, 0x6,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x40, 0x6f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf9, 0x6, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xc0, 0x6f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfe, 0x6, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf0, 0x6f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x6, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xe0, 0x6f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xfb, 0x6, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x70, 0x6f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf2, 0x6, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfa, 0x0,
    0x6f, 0xff, 0x20, 0x0, 0x0, 0x4b, 0xff, 0xfe,
    0x10, 0x6, 0xff, 0xfd, 0xcc, 0xcd, 0xff, 0xff,
    0xfe, 0x30, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x10, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xec, 0x82, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x7f, 0xff, 0xdc,
    0xcc, 0xcc, 0xca, 0x7, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xcb, 0xbb,
    0xbb, 0xa0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x7,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfc, 0xcc, 0xcc, 0xcc, 0xc5, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x67, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6,

    /* U+0046 "F" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x17, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x7f, 0xff, 0xdd,
    0xdd, 0xdd, 0xdd, 0x17, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xcb, 0xbb,
    0xbb, 0xb8, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x7,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xfe, 0xb7, 0x10,
    0x0, 0x0, 0x0, 0x1a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xfd,
    0xef, 0xff, 0xff, 0x70, 0x0, 0xc, 0xff, 0xfc,
    0x30, 0x0, 0x6, 0xef, 0xff, 0x30, 0x5, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf8, 0x0,
    0xcf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x5, 0x40,
    0x0, 0x1f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2,
    0x0, 0x0, 0x19, 0x99, 0x99, 0x99, 0x90, 0x7f,
    0xff, 0x20, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0x16, 0xff, 0xf3, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xf1, 0x4f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x10, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf1, 0xb, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x10, 0x4f,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf1,
    0x0, 0xaf, 0xff, 0xd5, 0x0, 0x0, 0x3a, 0xff,
    0xff, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xdd, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x27,
    0xce, 0xff, 0xdb, 0x61, 0x0, 0x0,

    /* U+0048 "H" */
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf7, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf7, 0x7f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf7, 0x7f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x7f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x7f, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xf7, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf7, 0x7f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf7, 0x7f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x7f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf7, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xf7,

    /* U+0049 "I" */
    0x6f, 0xff, 0x26, 0xff, 0xf2, 0x6f, 0xff, 0x26,
    0xff, 0xf2, 0x6f, 0xff, 0x26, 0xff, 0xf2, 0x6f,
    0xff, 0x26, 0xff, 0xf2, 0x6f, 0xff, 0x26, 0xff,
    0xf2, 0x6f, 0xff, 0x26, 0xff, 0xf2, 0x6f, 0xff,
    0x26, 0xff, 0xf2, 0x6f, 0xff, 0x26, 0xff, 0xf2,
    0x6f, 0xff, 0x26, 0xff, 0xf2, 0x6f, 0xff, 0x26,
    0xff, 0xf2,

    /* U+004A "J" */
    0x0, 0x4f, 0xff, 0xff, 0xfb, 0x0, 0x4f, 0xff,
    0xff, 0xfb, 0x0, 0x3d, 0xdd, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0, 0x0, 0xcf,
    0xfb, 0x0, 0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0,
    0x0, 0xcf, 0xfb, 0x0, 0x0, 0x0, 0xcf, 0xfb,
    0x0, 0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0x0, 0xcf, 0xfb, 0x0,
    0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0, 0x0, 0xcf,
    0xfb, 0x0, 0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0,
    0x0, 0xef, 0xfa, 0x0, 0x0, 0x3, 0xff, 0xf8,
    0x20, 0x0, 0x2d, 0xff, 0xf3, 0x8f, 0xde, 0xff,
    0xff, 0xa0, 0x8f, 0xff, 0xff, 0xfc, 0x0, 0x6e,
    0xff, 0xfc, 0x60, 0x0,

    /* U+004B "K" */
    0x6f, 0xff, 0x20, 0x0, 0x0, 0xd, 0xff, 0xf5,
    0x6, 0xff, 0xf2, 0x0, 0x0, 0xa, 0xff, 0xf8,
    0x0, 0x6f, 0xff, 0x20, 0x0, 0x7, 0xff, 0xfb,
    0x0, 0x6, 0xff, 0xf2, 0x0, 0x4, 0xff, 0xfd,
    0x0, 0x0, 0x6f, 0xff, 0x20, 0x2, 0xef, 0xfe,
    0x20, 0x0, 0x6, 0xff, 0xf2, 0x0, 0xdf, 0xff,
    0x40, 0x0, 0x0, 0x6f, 0xff, 0x20, 0xaf, 0xff,
    0x60, 0x0, 0x0, 0x6, 0xff, 0xf2, 0x7f, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x6f, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x67, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x60, 0xc, 0xff, 0xf5, 0x0, 0x0, 0x6, 0xff,
    0xf2, 0x0, 0x2f, 0xff, 0xe1, 0x0, 0x0, 0x6f,
    0xff, 0x20, 0x0, 0x8f, 0xff, 0xb0, 0x0, 0x6,
    0xff, 0xf2, 0x0, 0x0, 0xdf, 0xff, 0x60, 0x0,
    0x6f, 0xff, 0x20, 0x0, 0x3, 0xff, 0xff, 0x10,
    0x6, 0xff, 0xf2, 0x0, 0x0, 0x8, 0xff, 0xfb,
    0x0, 0x6f, 0xff, 0x20, 0x0, 0x0, 0xd, 0xff,
    0xf6, 0x6, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf2,

    /* U+004C "L" */
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfd, 0xcc, 0xcc, 0xcc, 0xc0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1,

    /* U+004D "M" */
    0x7f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xf2, 0x7f, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf2,
    0x7f, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf2, 0x7f, 0xfe, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xfe, 0xff, 0xf2,
    0x7f, 0xfc, 0xcf, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf8, 0xff, 0xf2, 0x7f, 0xfc, 0x6f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf3, 0xff, 0xf2,
    0x7f, 0xfc, 0x1f, 0xff, 0x20, 0x0, 0x0, 0x4,
    0xff, 0xb1, 0xff, 0xf2, 0x7f, 0xfc, 0x9, 0xff,
    0x80, 0x0, 0x0, 0xb, 0xff, 0x51, 0xff, 0xf2,
    0x7f, 0xfc, 0x3, 0xff, 0xe0, 0x0, 0x0, 0x2f,
    0xfe, 0x1, 0xff, 0xf2, 0x7f, 0xfc, 0x0, 0xcf,
    0xf6, 0x0, 0x0, 0x8f, 0xf8, 0x1, 0xff, 0xf2,
    0x7f, 0xfc, 0x0, 0x6f, 0xfd, 0x0, 0x0, 0xef,
    0xf2, 0x1, 0xff, 0xf2, 0x7f, 0xfc, 0x0, 0xe,
    0xff, 0x30, 0x5, 0xff, 0xb0, 0x1, 0xff, 0xf2,
    0x7f, 0xfc, 0x0, 0x8, 0xff, 0xa0, 0xb, 0xff,
    0x40, 0x1, 0xff, 0xf2, 0x7f, 0xfc, 0x0, 0x2,
    0xff, 0xf0, 0x2f, 0xfe, 0x0, 0x1, 0xff, 0xf2,
    0x7f, 0xfc, 0x0, 0x0, 0xbf, 0xf6, 0x8f, 0xf7,
    0x0, 0x1, 0xff, 0xf2, 0x7f, 0xfc, 0x0, 0x0,
    0x4f, 0xfb, 0xef, 0xf1, 0x0, 0x1, 0xff, 0xf2,
    0x7f, 0xfc, 0x0, 0x0, 0xe, 0xff, 0xff, 0xa0,
    0x0, 0x1, 0xff, 0xf2, 0x7f, 0xfc, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x40, 0x0, 0x1, 0xff, 0xf2,
    0x7f, 0xfc, 0x0, 0x0, 0x1, 0xff, 0xfd, 0x0,
    0x0, 0x1, 0xff, 0xf2, 0x7f, 0xfc, 0x0, 0x0,
    0x0, 0xaf, 0xf7, 0x0, 0x0, 0x1, 0xff, 0xf2,

    /* U+004E "N" */
    0x7f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x77, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf7, 0x7f, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x77, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0xcf, 0xf7, 0x7f, 0xfb,
    0xdf, 0xfb, 0x0, 0x0, 0x0, 0xc, 0xff, 0x77,
    0xff, 0xb3, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xcf,
    0xf7, 0x7f, 0xfc, 0x9, 0xff, 0xe1, 0x0, 0x0,
    0xc, 0xff, 0x77, 0xff, 0xc0, 0xe, 0xff, 0xa0,
    0x0, 0x0, 0xcf, 0xf7, 0x7f, 0xfc, 0x0, 0x4f,
    0xff, 0x40, 0x0, 0xc, 0xff, 0x77, 0xff, 0xc0,
    0x0, 0xaf, 0xfe, 0x0, 0x0, 0xcf, 0xf7, 0x7f,
    0xfc, 0x0, 0x1, 0xef, 0xf9, 0x0, 0xc, 0xff,
    0x77, 0xff, 0xc0, 0x0, 0x6, 0xff, 0xf4, 0x0,
    0xcf, 0xf7, 0x7f, 0xfc, 0x0, 0x0, 0xb, 0xff,
    0xd0, 0xc, 0xff, 0x77, 0xff, 0xc0, 0x0, 0x0,
    0x2f, 0xff, 0x80, 0xcf, 0xf7, 0x7f, 0xfc, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0x3b, 0xff, 0x77, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0xcf, 0xfd, 0xbf, 0xf7,
    0x7f, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0x77, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xf7, 0x7f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0x77, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xf7,

    /* U+004F "O" */
    0x0, 0x0, 0x1, 0x6a, 0xde, 0xfe, 0xd9, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xfe, 0xde, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0xb, 0xff, 0xfd, 0x50, 0x0, 0x1, 0x8f, 0xff,
    0xf5, 0x0, 0x5, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xf0, 0x0, 0xcf, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x70, 0x1f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xfc, 0x5, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf0, 0x7f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x28, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf3, 0x8f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x37, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x5f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x1, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xc0, 0xc, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf6, 0x0, 0x4f, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfe, 0x0,
    0x0, 0xaf, 0xff, 0xe5, 0x0, 0x0, 0x17, 0xff,
    0xff, 0x40, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xdd,
    0xef, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xad, 0xff, 0xfd, 0x94, 0x0,
    0x0, 0x0,

    /* U+0050 "P" */
    0x6f, 0xff, 0xff, 0xff, 0xec, 0x71, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x6f,
    0xff, 0x98, 0x89, 0xcf, 0xff, 0xf6, 0x6, 0xff,
    0xf2, 0x0, 0x0, 0x3e, 0xff, 0xf1, 0x6f, 0xff,
    0x20, 0x0, 0x0, 0x6f, 0xff, 0x56, 0xff, 0xf2,
    0x0, 0x0, 0x1, 0xff, 0xf8, 0x6f, 0xff, 0x20,
    0x0, 0x0, 0xf, 0xff, 0x86, 0xff, 0xf2, 0x0,
    0x0, 0x3, 0xff, 0xf6, 0x6f, 0xff, 0x20, 0x0,
    0x0, 0xaf, 0xff, 0x16, 0xff, 0xf2, 0x0, 0x14,
    0xcf, 0xff, 0x90, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x70, 0x0, 0x6f, 0xff, 0x98, 0x87, 0x64, 0x0,
    0x0, 0x6, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x6a, 0xde, 0xfe, 0xd9, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xfe, 0xde, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x9, 0xff, 0xfe, 0x61, 0x0, 0x2, 0x8f, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xe0, 0x0, 0xbf, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x60, 0x1f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xfb, 0x4, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf0, 0x7f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x18, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf3, 0x8f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x38, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf2, 0x6f, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0x13, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0xe, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf9, 0x0, 0x8f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x20,
    0x1, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x90, 0x0, 0x5, 0xff, 0xff, 0xb5, 0x21,
    0x36, 0xdf, 0xff, 0xc0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x2, 0xaf, 0xff, 0xff, 0xff, 0xfd, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x7e, 0xff,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xb9, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xcf, 0xfe, 0xb1,
    0x0,

    /* U+0052 "R" */
    0x7f, 0xff, 0xff, 0xff, 0xfe, 0xb7, 0x10, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x7f, 0xff, 0x98, 0x89, 0xad, 0xff, 0xff,
    0x40, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x5, 0xff,
    0xfd, 0x0, 0x7f, 0xff, 0x20, 0x0, 0x0, 0x8,
    0xff, 0xf2, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x40, 0x7f, 0xff, 0x20, 0x0, 0x0,
    0x6, 0xff, 0xf3, 0x7, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0x0, 0x7f, 0xff, 0x20, 0x0,
    0x15, 0xdf, 0xff, 0x80, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x60, 0x0, 0x7, 0xff, 0xf9,
    0x88, 0xdf, 0xfc, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x20, 0x4, 0xff, 0xf6, 0x0, 0x0, 0x7, 0xff,
    0xf2, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0, 0x7f,
    0xff, 0x20, 0x0, 0x2f, 0xff, 0xb0, 0x0, 0x7,
    0xff, 0xf2, 0x0, 0x0, 0x7f, 0xff, 0x50, 0x0,
    0x7f, 0xff, 0x20, 0x0, 0x0, 0xdf, 0xfe, 0x10,
    0x7, 0xff, 0xf2, 0x0, 0x0, 0x4, 0xff, 0xfa,
    0x0, 0x7f, 0xff, 0x20, 0x0, 0x0, 0xb, 0xff,
    0xf4, 0x7, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xd0,

    /* U+0053 "S" */
    0x0, 0x0, 0x5b, 0xef, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0xcf, 0xff, 0xda, 0xad, 0xff, 0xfd, 0x0,
    0x3, 0xff, 0xf8, 0x0, 0x0, 0x7f, 0xff, 0x70,
    0x7, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xfd, 0x80,
    0x7, 0xff, 0xf0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x5, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x81, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xc7, 0x20, 0x0, 0x0,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xfc, 0x50, 0x0,
    0x0, 0x0, 0x5, 0xae, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xef, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf5,
    0x5, 0x8a, 0x30, 0x0, 0x0, 0x1, 0xff, 0xf5,
    0xf, 0xff, 0xa0, 0x0, 0x0, 0x4, 0xff, 0xf4,
    0x9, 0xff, 0xf8, 0x0, 0x0, 0x2d, 0xff, 0xf0,
    0x1, 0xef, 0xff, 0xec, 0xbd, 0xff, 0xff, 0x60,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x6b, 0xef, 0xfe, 0xc8, 0x20, 0x0,

    /* U+0054 "T" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x2a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x8d, 0xdd, 0xdd, 0xef, 0xff, 0xdd, 0xdd,
    0xdd, 0x20, 0x0, 0x0, 0x8, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf0, 0x0,
    0x0, 0x0,

    /* U+0055 "U" */
    0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xec, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfe, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xec, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xfe, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xec, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfe, 0xcf, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xec, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xfe, 0xcf, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xec, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfe, 0xcf, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xec, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfe, 0xcf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xdb, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfc, 0x9f,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x95,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf5,
    0xd, 0xff, 0xfb, 0x20, 0x0, 0x2a, 0xff, 0xfd,
    0x0, 0x3f, 0xff, 0xff, 0xed, 0xef, 0xff, 0xff,
    0x20, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x0, 0x0, 0x5, 0xad, 0xff, 0xed, 0x94,
    0x0, 0x0,

    /* U+0056 "V" */
    0xaf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xe0, 0x4f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x80, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x20, 0x8, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xfc, 0x0,
    0x2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf6, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf0, 0x0, 0x0, 0x6f, 0xff, 0x10,
    0x0, 0x0, 0xd, 0xff, 0xa0, 0x0, 0x0, 0xf,
    0xff, 0x70, 0x0, 0x0, 0x2f, 0xff, 0x40, 0x0,
    0x0, 0xa, 0xff, 0xc0, 0x0, 0x0, 0x8f, 0xfd,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf2, 0x0, 0x0,
    0xdf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf8,
    0x0, 0x3, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xfd, 0x0, 0x8, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x30, 0xe, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x3f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xd0, 0x8f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf2, 0xdf, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf9, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0xaf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x50, 0x0, 0x0, 0x0, 0xf, 0xff, 0xa6, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf5, 0x1f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x10, 0xdf, 0xfc, 0x0, 0x0,
    0x0, 0x3f, 0xfe, 0xff, 0x30, 0x0, 0x0, 0xc,
    0xff, 0xc0, 0x8, 0xff, 0xf0, 0x0, 0x0, 0x8,
    0xff, 0x7f, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x0, 0x4f, 0xff, 0x40, 0x0, 0x0, 0xdf, 0xf1,
    0xff, 0xc0, 0x0, 0x0, 0x4f, 0xff, 0x30, 0x0,
    0xff, 0xf8, 0x0, 0x0, 0x1f, 0xfc, 0xd, 0xff,
    0x10, 0x0, 0x8, 0xff, 0xe0, 0x0, 0xb, 0xff,
    0xc0, 0x0, 0x6, 0xff, 0x80, 0x9f, 0xf5, 0x0,
    0x0, 0xcf, 0xfa, 0x0, 0x0, 0x7f, 0xff, 0x0,
    0x0, 0xbf, 0xf3, 0x4, 0xff, 0xa0, 0x0, 0x1f,
    0xff, 0x50, 0x0, 0x2, 0xff, 0xf4, 0x0, 0xf,
    0xfe, 0x0, 0xf, 0xfe, 0x0, 0x5, 0xff, 0xf1,
    0x0, 0x0, 0xe, 0xff, 0x80, 0x4, 0xff, 0xa0,
    0x0, 0xbf, 0xf3, 0x0, 0x9f, 0xfc, 0x0, 0x0,
    0x0, 0x9f, 0xfc, 0x0, 0x8f, 0xf5, 0x0, 0x6,
    0xff, 0x80, 0xd, 0xff, 0x70, 0x0, 0x0, 0x5,
    0xff, 0xf0, 0xd, 0xff, 0x10, 0x0, 0x2f, 0xfc,
    0x1, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x41, 0xff, 0xc0, 0x0, 0x0, 0xdf, 0xf0, 0x5f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf8, 0x5f,
    0xf7, 0x0, 0x0, 0x9, 0xff, 0x49, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xb9, 0xff, 0x30,
    0x0, 0x0, 0x4f, 0xf8, 0xdf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xcf, 0xe0, 0x0, 0x0,
    0x0, 0xff, 0xcf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x30,
    0x0, 0x0,

    /* U+0058 "X" */
    0x2, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x6f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0xbf, 0xfe, 0x10, 0x0, 0xa, 0xff, 0xe1,
    0x0, 0x0, 0x6, 0xff, 0xf5, 0x0, 0x0, 0x1,
    0xef, 0xfa, 0x0, 0x0, 0x1f, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0xbf, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x9, 0xff, 0xe1, 0x6,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfa, 0x1f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xdf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xef, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xfb, 0x2f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf1, 0x7,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x60, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xfb, 0x0, 0x0, 0x2f, 0xff, 0x90, 0x0,
    0x0, 0xa, 0xff, 0xf1, 0x0, 0x0, 0x7, 0xff,
    0xf4, 0x0, 0x0, 0x5f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xcf, 0xfe, 0x0, 0x1, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x90, 0xb, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf4,

    /* U+0059 "Y" */
    0xbf, 0xff, 0x20, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xe1, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x60, 0x8, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xef, 0xfb, 0x0, 0x0,
    0x4, 0xff, 0xf3, 0x0, 0x0, 0x5f, 0xff, 0x30,
    0x0, 0xd, 0xff, 0xa0, 0x0, 0x0, 0xc, 0xff,
    0xc0, 0x0, 0x5f, 0xff, 0x10, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x0, 0xdf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xfc, 0x6, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0x6e, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x60, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x20, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x1, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcb, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe,

    /* U+005B "[" */
    0xcf, 0xff, 0xff, 0xfb, 0xcf, 0xff, 0xff, 0xfb,
    0xcf, 0xf9, 0x55, 0x53, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf7, 0x0, 0x0, 0xcf, 0xf7, 0x0, 0x0,
    0xcf, 0xf9, 0x44, 0x43, 0xcf, 0xff, 0xff, 0xfb,
    0xcf, 0xff, 0xff, 0xfb,

    /* U+005C "\\" */
    0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe,
    0x0,

    /* U+005D "]" */
    0x8f, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0x25, 0x55, 0x7f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x4f, 0xff,
    0x24, 0x44, 0x7f, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0xbf, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x6f, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x90, 0xdf, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x6, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0, 0xe,
    0xfd, 0x0, 0x0, 0x0, 0x7, 0xff, 0x30, 0x0,
    0x7f, 0xf4, 0x0, 0x0, 0x0, 0xef, 0xc0, 0x0,
    0x1, 0xff, 0xb0, 0x0, 0x0, 0x6f, 0xf5, 0x0,
    0x0, 0x9, 0xff, 0x30, 0x0, 0xd, 0xfe, 0x0,
    0x0, 0x0, 0x2f, 0xfa, 0x0, 0x5, 0xff, 0x70,
    0x0, 0x0, 0x0, 0xaf, 0xf2, 0x0, 0xcf, 0xf0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x90, 0x3f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x10,

    /* U+005F "_" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x10, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x14, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x42,

    /* U+0060 "`" */
    0x48, 0x87, 0x0, 0x0, 0x2d, 0xff, 0x90, 0x0,
    0x0, 0xbf, 0xf6, 0x0, 0x0, 0x9, 0xff, 0x40,
    0x0, 0x0, 0x6f, 0xf2,

    /* U+0061 "a" */
    0x0, 0x7, 0xce, 0xff, 0xd9, 0x20, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x9, 0xff, 0xf7,
    0x45, 0xdf, 0xfe, 0x0, 0x8c, 0xe7, 0x0, 0x1,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0,
    0x39, 0xce, 0xff, 0xff, 0xff, 0xa0, 0x8f, 0xff,
    0xff, 0xee, 0xff, 0xfa, 0x5f, 0xff, 0xa2, 0x0,
    0xb, 0xff, 0xab, 0xff, 0xd0, 0x0, 0x0, 0xcf,
    0xfa, 0xdf, 0xfa, 0x0, 0x0, 0xf, 0xff, 0xad,
    0xff, 0xd0, 0x0, 0x9, 0xff, 0xfa, 0x8f, 0xff,
    0xb6, 0x7b, 0xfe, 0xff, 0xa1, 0xef, 0xff, 0xff,
    0xf7, 0xaf, 0xfa, 0x1, 0x9d, 0xff, 0xc5, 0xa,
    0xff, 0xa0,

    /* U+0062 "b" */
    0xcf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9, 0x4,
    0xbe, 0xfe, 0xb5, 0x0, 0xc, 0xff, 0x97, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0xcf, 0xfc, 0xfe, 0x97,
    0x9e, 0xff, 0xf8, 0xc, 0xff, 0xfc, 0x0, 0x0,
    0x1c, 0xff, 0xf1, 0xcf, 0xff, 0x20, 0x0, 0x0,
    0x3f, 0xff, 0x6c, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xef, 0xf9, 0xcf, 0xf9, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xbc, 0xff, 0x80, 0x0, 0x0, 0x0, 0xbf,
    0xfc, 0xcf, 0xf9, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xbc, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xef, 0xf9,
    0xcf, 0xff, 0x10, 0x0, 0x0, 0x4f, 0xff, 0x5c,
    0xff, 0xfb, 0x0, 0x0, 0x1d, 0xff, 0xf0, 0xcf,
    0xfc, 0xfe, 0x87, 0x8e, 0xff, 0xf7, 0xc, 0xff,
    0x78, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xdf, 0xf6,
    0x4, 0xbe, 0xfe, 0xa4, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x3, 0xae, 0xff, 0xd9, 0x10, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x6, 0xff, 0xfb,
    0x78, 0xff, 0xfe, 0x0, 0xef, 0xf9, 0x0, 0x3,
    0xff, 0xf6, 0x5f, 0xff, 0x10, 0x0, 0x9, 0xba,
    0x58, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x10, 0x0, 0xc, 0xed, 0x60,
    0xff, 0xfa, 0x0, 0x3, 0xff, 0xf5, 0x7, 0xff,
    0xfb, 0x79, 0xff, 0xfd, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x4, 0xae, 0xff, 0xd8,
    0x10, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x3, 0xae,
    0xfe, 0xc5, 0x8, 0xff, 0xd0, 0x7, 0xff, 0xff,
    0xff, 0xf9, 0x8f, 0xfd, 0x5, 0xff, 0xfe, 0x97,
    0x9e, 0xfd, 0xff, 0xd0, 0xef, 0xfd, 0x10, 0x0,
    0xb, 0xff, 0xfd, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x1f, 0xff, 0xd8, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0xbf, 0xfd, 0xaf, 0xfd, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xdb, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x7f,
    0xfd, 0xaf, 0xfd, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xd9, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xbf, 0xfd,
    0x5f, 0xff, 0x40, 0x0, 0x0, 0x1f, 0xff, 0xd0,
    0xff, 0xfd, 0x10, 0x0, 0xb, 0xff, 0xfd, 0x7,
    0xff, 0xfe, 0x87, 0x8e, 0xfc, 0xff, 0xd0, 0x9,
    0xff, 0xff, 0xff, 0xf7, 0x7f, 0xfe, 0x0, 0x4,
    0xbe, 0xfe, 0xb4, 0x6, 0xff, 0xf0,

    /* U+0065 "e" */
    0x0, 0x1, 0x8c, 0xff, 0xea, 0x40, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x4, 0xff,
    0xfc, 0x66, 0x9f, 0xff, 0x80, 0xe, 0xff, 0xa0,
    0x0, 0x6, 0xff, 0xf1, 0x4f, 0xff, 0x10, 0x0,
    0x0, 0xef, 0xf7, 0x8f, 0xfe, 0x22, 0x22, 0x22,
    0xbf, 0xfa, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xaf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0x40, 0x0, 0x0, 0x37, 0x0, 0xe, 0xff, 0xd1,
    0x0, 0x1, 0xdf, 0xf4, 0x4, 0xff, 0xfe, 0x76,
    0x8e, 0xff, 0xd0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x2, 0x9d, 0xff, 0xeb, 0x60,
    0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x18, 0xdf, 0xfe, 0x80, 0x0, 0xd,
    0xff, 0xff, 0xf9, 0x0, 0x5, 0xff, 0xf9, 0x44,
    0x30, 0x0, 0x9f, 0xfe, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0xaf, 0xfb, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x16, 0x6c, 0xff, 0xd6,
    0x66, 0x0, 0x0, 0xaf, 0xfb, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xaf, 0xfb,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0xaf, 0xfb, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0xaf, 0xfb, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xaf,
    0xfb, 0x0, 0x0, 0x0, 0xa, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xaf, 0xfb, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xb0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x4, 0xae, 0xfe, 0xa3, 0x4, 0xff, 0xe0,
    0x9, 0xff, 0xff, 0xff, 0xf7, 0x5f, 0xfe, 0x7,
    0xff, 0xff, 0x97, 0x8d, 0xfb, 0xff, 0xd0, 0xff,
    0xfe, 0x20, 0x0, 0xb, 0xff, 0xfd, 0x5f, 0xff,
    0x50, 0x0, 0x0, 0x1f, 0xff, 0xd8, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0xaf, 0xfd, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xdb, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x7f, 0xfd, 0xaf, 0xfd, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xd9, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xbf, 0xfd, 0x5f, 0xff, 0x50, 0x0, 0x0,
    0x1f, 0xff, 0xd0, 0xff, 0xfe, 0x10, 0x0, 0xb,
    0xff, 0xfd, 0x8, 0xff, 0xff, 0x97, 0x9e, 0xfa,
    0xff, 0xd0, 0xa, 0xff, 0xff, 0xff, 0xf6, 0x7f,
    0xfd, 0x0, 0x5, 0xbe, 0xfe, 0xa3, 0x7, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfc,
    0x3, 0x68, 0x40, 0x0, 0x0, 0xe, 0xff, 0x80,
    0xdf, 0xfd, 0x10, 0x0, 0x9, 0xff, 0xf2, 0x6,
    0xff, 0xfe, 0x97, 0x8d, 0xff, 0xf8, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x3,
    0x9d, 0xff, 0xfd, 0x92, 0x0, 0x0,

    /* U+0068 "h" */
    0xcf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf9, 0x3, 0xae, 0xfe, 0xa2,
    0x0, 0xcf, 0xf8, 0x5f, 0xff, 0xff, 0xff, 0x30,
    0xcf, 0xfa, 0xff, 0xed, 0xff, 0xff, 0xd0, 0xcf,
    0xff, 0xe3, 0x0, 0x1c, 0xff, 0xf3, 0xcf, 0xff,
    0x40, 0x0, 0x3, 0xff, 0xf5, 0xcf, 0xfd, 0x0,
    0x0, 0x0, 0xff, 0xf7, 0xcf, 0xfa, 0x0, 0x0,
    0x0, 0xef, 0xf7, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0xcf, 0xf9, 0x0, 0x0, 0x0, 0xef,
    0xf7, 0xcf, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0xcf, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xf7, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0xef, 0xf7, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0xef, 0xf7, 0xcf, 0xf9, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0xcf, 0xf9, 0x0, 0x0,
    0x0, 0xef, 0xf7,

    /* U+0069 "i" */
    0xcf, 0xf9, 0xcf, 0xf9, 0xad, 0xd7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9,

    /* U+006A "j" */
    0x0, 0xc, 0xff, 0x90, 0x0, 0xcf, 0xf9, 0x0,
    0xa, 0xdd, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x90, 0x0, 0xcf, 0xf9, 0x0, 0xc, 0xff, 0x90,
    0x0, 0xcf, 0xf9, 0x0, 0xc, 0xff, 0x90, 0x0,
    0xcf, 0xf9, 0x0, 0xc, 0xff, 0x90, 0x0, 0xcf,
    0xf9, 0x0, 0xc, 0xff, 0x90, 0x0, 0xcf, 0xf9,
    0x0, 0xc, 0xff, 0x90, 0x0, 0xcf, 0xf9, 0x0,
    0xc, 0xff, 0x90, 0x0, 0xcf, 0xf9, 0x0, 0xc,
    0xff, 0x90, 0x0, 0xcf, 0xf9, 0x0, 0xd, 0xff,
    0x90, 0x0, 0xff, 0xf7, 0x36, 0xbf, 0xff, 0x48,
    0xff, 0xff, 0xd0, 0x7f, 0xfe, 0xa1, 0x0,

    /* U+006B "k" */
    0xcf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0, 0xc, 0xff,
    0xe1, 0xcf, 0xf9, 0x0, 0x0, 0xbf, 0xfe, 0x20,
    0xcf, 0xf9, 0x0, 0x9, 0xff, 0xf3, 0x0, 0xcf,
    0xf9, 0x0, 0x8f, 0xff, 0x30, 0x0, 0xcf, 0xf9,
    0x6, 0xff, 0xf4, 0x0, 0x0, 0xcf, 0xf9, 0x4f,
    0xff, 0x50, 0x0, 0x0, 0xcf, 0xfc, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0xcf, 0xff, 0xdb, 0xff, 0xf3, 0x0,
    0x0, 0xcf, 0xfc, 0x11, 0xef, 0xfd, 0x0, 0x0,
    0xcf, 0xf9, 0x0, 0x4f, 0xff, 0xa0, 0x0, 0xcf,
    0xf9, 0x0, 0x9, 0xff, 0xf5, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0xdf, 0xfe, 0x10, 0xcf, 0xf9, 0x0,
    0x0, 0x3f, 0xff, 0xb0, 0xcf, 0xf9, 0x0, 0x0,
    0x7, 0xff, 0xf7,

    /* U+006C "l" */
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9, 0xcf, 0xf9,
    0xcf, 0xf9,

    /* U+006D "m" */
    0xef, 0xf5, 0x3, 0xbe, 0xfd, 0x81, 0x0, 0x18,
    0xdf, 0xeb, 0x40, 0xd, 0xff, 0x65, 0xff, 0xff,
    0xff, 0xc0, 0x1d, 0xff, 0xff, 0xff, 0x50, 0xcf,
    0xf8, 0xfe, 0xab, 0xff, 0xff, 0x7b, 0xfc, 0xad,
    0xff, 0xfe, 0xc, 0xff, 0xfc, 0x10, 0x2, 0xff,
    0xff, 0xf5, 0x0, 0xa, 0xff, 0xf4, 0xcf, 0xff,
    0x30, 0x0, 0x9, 0xff, 0xfa, 0x0, 0x0, 0x1f,
    0xff, 0x7c, 0xff, 0xd0, 0x0, 0x0, 0x6f, 0xff,
    0x40, 0x0, 0x0, 0xef, 0xf8, 0xcf, 0xfa, 0x0,
    0x0, 0x5, 0xff, 0xf2, 0x0, 0x0, 0xd, 0xff,
    0x8c, 0xff, 0x90, 0x0, 0x0, 0x5f, 0xff, 0x10,
    0x0, 0x0, 0xdf, 0xf8, 0xcf, 0xf9, 0x0, 0x0,
    0x5, 0xff, 0xf1, 0x0, 0x0, 0xd, 0xff, 0x8c,
    0xff, 0x90, 0x0, 0x0, 0x5f, 0xff, 0x10, 0x0,
    0x0, 0xdf, 0xf8, 0xcf, 0xf9, 0x0, 0x0, 0x5,
    0xff, 0xf1, 0x0, 0x0, 0xd, 0xff, 0x8c, 0xff,
    0x90, 0x0, 0x0, 0x5f, 0xff, 0x10, 0x0, 0x0,
    0xdf, 0xf8, 0xcf, 0xf9, 0x0, 0x0, 0x5, 0xff,
    0xf1, 0x0, 0x0, 0xd, 0xff, 0x8c, 0xff, 0x90,
    0x0, 0x0, 0x5f, 0xff, 0x10, 0x0, 0x0, 0xdf,
    0xf8, 0xcf, 0xf9, 0x0, 0x0, 0x5, 0xff, 0xf1,
    0x0, 0x0, 0xd, 0xff, 0x80,

    /* U+006E "n" */
    0xef, 0xf5, 0x3, 0xae, 0xfe, 0xa2, 0x0, 0xdf,
    0xf6, 0x4f, 0xff, 0xff, 0xff, 0x40, 0xcf, 0xf8,
    0xff, 0xbb, 0xef, 0xff, 0xd0, 0xcf, 0xff, 0xd1,
    0x0, 0xb, 0xff, 0xf3, 0xcf, 0xff, 0x30, 0x0,
    0x3, 0xff, 0xf6, 0xcf, 0xfd, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0xcf, 0xfa, 0x0, 0x0, 0x0, 0xef,
    0xf7, 0xcf, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xf7,
    0xcf, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xf7, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0xef, 0xf7, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0xef, 0xf7, 0xcf, 0xf9, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0xcf, 0xf9, 0x0, 0x0,
    0x0, 0xef, 0xf7, 0xcf, 0xf9, 0x0, 0x0, 0x0,
    0xef, 0xf7, 0xcf, 0xf9, 0x0, 0x0, 0x0, 0xef,
    0xf7,

    /* U+006F "o" */
    0x0, 0x0, 0x6b, 0xef, 0xfe, 0xb6, 0x0, 0x0,
    0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x3, 0xff, 0xff, 0xb8, 0x8b, 0xff, 0xff, 0x30,
    0xd, 0xff, 0xe3, 0x0, 0x0, 0x3f, 0xff, 0xc0,
    0x4f, 0xff, 0x60, 0x0, 0x0, 0x7, 0xff, 0xf3,
    0x8f, 0xff, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf7,
    0xaf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf9,
    0xbf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfa,
    0xaf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf9,
    0x8f, 0xff, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf6,
    0x3f, 0xff, 0x60, 0x0, 0x0, 0x7, 0xff, 0xf2,
    0xc, 0xff, 0xe3, 0x0, 0x0, 0x4f, 0xff, 0xb0,
    0x2, 0xff, 0xff, 0xa7, 0x8b, 0xff, 0xfe, 0x10,
    0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0x0, 0x6b, 0xef, 0xfd, 0xa5, 0x0, 0x0,

    /* U+0070 "p" */
    0xef, 0xf6, 0x3, 0xae, 0xfe, 0xb5, 0x0, 0xd,
    0xff, 0x76, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xcf,
    0xfb, 0xfe, 0x97, 0x9e, 0xff, 0xf8, 0xc, 0xff,
    0xfc, 0x10, 0x0, 0x1d, 0xff, 0xf1, 0xcf, 0xff,
    0x20, 0x0, 0x0, 0x4f, 0xff, 0x6c, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0xef, 0xf9, 0xcf, 0xf9, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xbc, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xbf, 0xfc, 0xcf, 0xf9, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xbc, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xff, 0xf9, 0xcf, 0xff, 0x10, 0x0, 0x0,
    0x4f, 0xff, 0x5c, 0xff, 0xfc, 0x0, 0x0, 0x1d,
    0xff, 0xe0, 0xcf, 0xfd, 0xfe, 0x97, 0x9e, 0xff,
    0xf6, 0xc, 0xff, 0x89, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xcf, 0xf9, 0x5, 0xbe, 0xfe, 0xa3, 0x0,
    0xc, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x3, 0xae, 0xfe, 0xb5, 0x5, 0xff, 0xf0,
    0x8, 0xff, 0xff, 0xff, 0xf8, 0x6f, 0xfe, 0x6,
    0xff, 0xff, 0x97, 0x9e, 0xfc, 0xff, 0xd0, 0xef,
    0xfd, 0x10, 0x0, 0xb, 0xff, 0xfd, 0x5f, 0xff,
    0x50, 0x0, 0x0, 0x1f, 0xff, 0xd8, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0xaf, 0xfd, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xdb, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x7f, 0xfd, 0xaf, 0xfd, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xd9, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xbf, 0xfd, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x1f, 0xff, 0xd1, 0xff, 0xfd, 0x10, 0x0, 0xc,
    0xff, 0xfd, 0x7, 0xff, 0xfe, 0x97, 0x9e, 0xfc,
    0xff, 0xd0, 0xa, 0xff, 0xff, 0xff, 0xf8, 0x8f,
    0xfd, 0x0, 0x5, 0xbe, 0xfe, 0xb4, 0x8, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xd0,

    /* U+0072 "r" */
    0xef, 0xf5, 0x6, 0xdf, 0xbd, 0xff, 0x56, 0xff,
    0xfb, 0xcf, 0xf7, 0xff, 0xdc, 0x9c, 0xff, 0xdf,
    0x40, 0x0, 0xcf, 0xff, 0x60, 0x0, 0xc, 0xff,
    0xe0, 0x0, 0x0, 0xcf, 0xfb, 0x0, 0x0, 0xc,
    0xff, 0x90, 0x0, 0x0, 0xcf, 0xf9, 0x0, 0x0,
    0xc, 0xff, 0x90, 0x0, 0x0, 0xcf, 0xf9, 0x0,
    0x0, 0xc, 0xff, 0x90, 0x0, 0x0, 0xcf, 0xf9,
    0x0, 0x0, 0xc, 0xff, 0x90, 0x0, 0x0, 0xcf,
    0xf9, 0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x6, 0xcf, 0xfe, 0xc6, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x6, 0xff, 0xe5,
    0x36, 0xef, 0xf3, 0x0, 0xaf, 0xf7, 0x0, 0x5,
    0x98, 0x30, 0xb, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0x71, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xfc, 0x82, 0x0, 0x0, 0x2, 0xbf,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x16, 0xaf,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x2,
    0xad, 0xb0, 0x0, 0x4, 0xff, 0xe0, 0xe, 0xff,
    0xa4, 0x36, 0xef, 0xf9, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x3a, 0xdf, 0xfe, 0xc6,
    0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0x88, 0x10, 0x0, 0x0, 0x0, 0x5f,
    0xf3, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xef, 0xf3, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xfd, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x26, 0x7f, 0xff, 0x86, 0x65, 0x0, 0x2,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x30,
    0x0, 0x0, 0x2, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x30, 0x0, 0x0, 0x2, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x30, 0x0, 0x0,
    0x2, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x30, 0x0, 0x0, 0x1, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xf9, 0x89, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x6d, 0xff, 0xda,
    0x0,

    /* U+0075 "u" */
    0xff, 0xf6, 0x0, 0x0, 0x1, 0xff, 0xf4, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xff, 0xf4, 0xff, 0xf6,
    0x0, 0x0, 0x1, 0xff, 0xf4, 0xff, 0xf6, 0x0,
    0x0, 0x1, 0xff, 0xf4, 0xff, 0xf6, 0x0, 0x0,
    0x1, 0xff, 0xf4, 0xff, 0xf6, 0x0, 0x0, 0x1,
    0xff, 0xf4, 0xff, 0xf6, 0x0, 0x0, 0x1, 0xff,
    0xf4, 0xff, 0xf6, 0x0, 0x0, 0x1, 0xff, 0xf4,
    0xff, 0xf6, 0x0, 0x0, 0x2, 0xff, 0xf4, 0xff,
    0xf7, 0x0, 0x0, 0x5, 0xff, 0xf4, 0xef, 0xfb,
    0x0, 0x0, 0xb, 0xff, 0xf4, 0xbf, 0xff, 0x40,
    0x0, 0x6f, 0xff, 0xf4, 0x5f, 0xff, 0xfc, 0xad,
    0xf9, 0xef, 0xf5, 0xb, 0xff, 0xff, 0xff, 0xc0,
    0xdf, 0xf5, 0x0, 0x7d, 0xff, 0xd7, 0x0, 0xdf,
    0xf6,

    /* U+0076 "v" */
    0xcf, 0xfc, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf1,
    0x6f, 0xff, 0x10, 0x0, 0x0, 0xd, 0xff, 0xa0,
    0x1f, 0xff, 0x60, 0x0, 0x0, 0x2f, 0xff, 0x40,
    0xb, 0xff, 0xb0, 0x0, 0x0, 0x8f, 0xfe, 0x0,
    0x5, 0xff, 0xf1, 0x0, 0x0, 0xdf, 0xf8, 0x0,
    0x0, 0xef, 0xf6, 0x0, 0x2, 0xff, 0xf2, 0x0,
    0x0, 0x9f, 0xfb, 0x0, 0x8, 0xff, 0xc0, 0x0,
    0x0, 0x3f, 0xff, 0x10, 0xd, 0xff, 0x60, 0x0,
    0x0, 0xd, 0xff, 0x60, 0x3f, 0xff, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xb0, 0x8f, 0xf9, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf0, 0xdf, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xfb, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0xcf, 0xf7, 0x0, 0x0, 0x9, 0xff, 0xf2, 0x0,
    0x0, 0xd, 0xff, 0x57, 0xff, 0xc0, 0x0, 0x0,
    0xef, 0xff, 0x70, 0x0, 0x1, 0xff, 0xf1, 0x3f,
    0xff, 0x0, 0x0, 0x2f, 0xff, 0xfb, 0x0, 0x0,
    0x5f, 0xfc, 0x0, 0xef, 0xf4, 0x0, 0x7, 0xff,
    0xbf, 0xf0, 0x0, 0x9, 0xff, 0x80, 0xa, 0xff,
    0x80, 0x0, 0xbf, 0xf6, 0xff, 0x40, 0x0, 0xdf,
    0xf3, 0x0, 0x6f, 0xfc, 0x0, 0xf, 0xfb, 0x2f,
    0xf9, 0x0, 0x1f, 0xfe, 0x0, 0x1, 0xff, 0xf0,
    0x4, 0xff, 0x70, 0xdf, 0xd0, 0x5, 0xff, 0xa0,
    0x0, 0xd, 0xff, 0x40, 0x8f, 0xf2, 0x9, 0xff,
    0x10, 0x9f, 0xf5, 0x0, 0x0, 0x9f, 0xf8, 0xd,
    0xfe, 0x0, 0x5f, 0xf6, 0xd, 0xff, 0x10, 0x0,
    0x4, 0xff, 0xb1, 0xff, 0x90, 0x0, 0xff, 0xa1,
    0xff, 0xc0, 0x0, 0x0, 0xf, 0xff, 0x5f, 0xf5,
    0x0, 0xc, 0xfe, 0x5f, 0xf8, 0x0, 0x0, 0x0,
    0xbf, 0xfb, 0xff, 0x0, 0x0, 0x8f, 0xfb, 0xff,
    0x30, 0x0, 0x0, 0x7, 0xff, 0xff, 0xc0, 0x0,
    0x3, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf7, 0x0, 0x0, 0xf, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x30, 0x0, 0x0,
    0xbf, 0xff, 0x60, 0x0, 0x0,

    /* U+0078 "x" */
    0x4f, 0xff, 0x50, 0x0, 0x0, 0x6f, 0xff, 0x30,
    0x9f, 0xfe, 0x0, 0x0, 0x1f, 0xff, 0x80, 0x0,
    0xdf, 0xf9, 0x0, 0xa, 0xff, 0xc0, 0x0, 0x2,
    0xff, 0xf3, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x6,
    0xff, 0xd1, 0xef, 0xf5, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xef, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xaf,
    0xfe, 0x10, 0x0, 0x0, 0xb, 0xff, 0xa0, 0xbf,
    0xfb, 0x0, 0x0, 0x6, 0xff, 0xe1, 0x1, 0xff,
    0xf6, 0x0, 0x2, 0xff, 0xf5, 0x0, 0x6, 0xff,
    0xf2, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0xb, 0xff,
    0xc0, 0x8f, 0xfe, 0x10, 0x0, 0x0, 0x2f, 0xff,
    0x80,

    /* U+0079 "y" */
    0xd, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x10, 0x7f, 0xff, 0x10, 0x0, 0x0, 0xc, 0xff,
    0xb0, 0x1, 0xff, 0xf6, 0x0, 0x0, 0x1, 0xff,
    0xf5, 0x0, 0xa, 0xff, 0xc0, 0x0, 0x0, 0x6f,
    0xfe, 0x0, 0x0, 0x4f, 0xff, 0x20, 0x0, 0xc,
    0xff, 0x90, 0x0, 0x0, 0xdf, 0xf7, 0x0, 0x1,
    0xff, 0xf3, 0x0, 0x0, 0x7, 0xff, 0xd0, 0x0,
    0x6f, 0xfc, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x20,
    0xb, 0xff, 0x60, 0x0, 0x0, 0x0, 0xbf, 0xf8,
    0x1, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xd0, 0x6f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x2b, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0xcf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0xfd, 0x81, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x4, 0x77,
    0x77, 0x77, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xd7, 0x77,
    0x77, 0x77, 0x72, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5,

    /* U+007B "{" */
    0x0, 0x0, 0x6, 0xcf, 0xfa, 0x0, 0x0, 0x9f,
    0xff, 0xfa, 0x0, 0x2, 0xff, 0xfd, 0x63, 0x0,
    0x6, 0xff, 0xf1, 0x0, 0x0, 0x8, 0xff, 0xb0,
    0x0, 0x0, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x8,
    0xff, 0xa0, 0x0, 0x0, 0x8, 0xff, 0xa0, 0x0,
    0x0, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x9, 0xff,
    0xa0, 0x0, 0x0, 0xb, 0xff, 0x80, 0x0, 0x0,
    0x3f, 0xff, 0x40, 0x0, 0x59, 0xff, 0xfb, 0x0,
    0x0, 0xcf, 0xff, 0x60, 0x0, 0x0, 0xcf, 0xff,
    0xd3, 0x0, 0x0, 0x3, 0xbf, 0xfe, 0x10, 0x0,
    0x0, 0xe, 0xff, 0x70, 0x0, 0x0, 0xa, 0xff,
    0x90, 0x0, 0x0, 0x8, 0xff, 0xa0, 0x0, 0x0,
    0x8, 0xff, 0xa0, 0x0, 0x0, 0x8, 0xff, 0xa0,
    0x0, 0x0, 0x8, 0xff, 0xa0, 0x0, 0x0, 0x8,
    0xff, 0xb0, 0x0, 0x0, 0x7, 0xff, 0xf0, 0x0,
    0x0, 0x2, 0xff, 0xfd, 0x63, 0x0, 0x0, 0x9f,
    0xff, 0xfa, 0x0, 0x0, 0x7, 0xdf, 0xfa,

    /* U+007C "|" */
    0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc,
    0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc,
    0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc,
    0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc,
    0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc,
    0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc,
    0x6f, 0xfc, 0x6f, 0xfc, 0x6f, 0xfc,

    /* U+007D "}" */
    0xbf, 0xfc, 0x50, 0x0, 0x0, 0xbf, 0xff, 0xf6,
    0x0, 0x0, 0x37, 0xef, 0xff, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0x40, 0x0, 0x0, 0xd, 0xff, 0x60,
    0x0, 0x0, 0xb, 0xff, 0x60, 0x0, 0x0, 0xb,
    0xff, 0x60, 0x0, 0x0, 0xb, 0xff, 0x60, 0x0,
    0x0, 0xb, 0xff, 0x60, 0x0, 0x0, 0xb, 0xff,
    0x70, 0x0, 0x0, 0xa, 0xff, 0x90, 0x0, 0x0,
    0x5, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xcf, 0xfe,
    0x84, 0x0, 0x0, 0x8, 0xff, 0xfa, 0x0, 0x0,
    0x4e, 0xff, 0xfa, 0x0, 0x2, 0xff, 0xfa, 0x20,
    0x0, 0x9, 0xff, 0xd0, 0x0, 0x0, 0xb, 0xff,
    0x80, 0x0, 0x0, 0xb, 0xff, 0x60, 0x0, 0x0,
    0xb, 0xff, 0x60, 0x0, 0x0, 0xb, 0xff, 0x60,
    0x0, 0x0, 0xb, 0xff, 0x60, 0x0, 0x0, 0xd,
    0xff, 0x60, 0x0, 0x0, 0x2f, 0xff, 0x50, 0x0,
    0x36, 0xdf, 0xff, 0x10, 0x0, 0xbf, 0xff, 0xf8,
    0x0, 0x0, 0xbf, 0xfc, 0x60, 0x0, 0x0,

    /* U+007E "~" */
    0x4, 0xae, 0xfe, 0xd9, 0x51, 0x0, 0x0, 0x5,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x98, 0x9d,
    0xf1, 0x3f, 0xd9, 0x89, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0x2, 0x40, 0x0, 0x0, 0x4, 0x9d, 0xff,
    0xea, 0x40,

    /* U+5185 "内" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9a, 0x96, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x39, 0x87, 0x66, 0x66,
    0x66, 0xdf, 0xf8, 0x66, 0x66, 0x66, 0x78, 0x92,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0,
    0x0, 0x0, 0xdf, 0xf0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0xbf, 0xf0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0xef, 0xd0, 0x0,
    0x0, 0x0, 0xdf, 0xf0, 0x1f, 0xfb, 0x0, 0x0,
    0x1, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x1f, 0xfb, 0x0, 0x0, 0x6, 0xff, 0xff, 0x40,
    0x0, 0x0, 0xdf, 0xf0, 0x1f, 0xfb, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xf3, 0x0, 0x0, 0xdf, 0xf0,
    0x1f, 0xfb, 0x0, 0x0, 0x3f, 0xfb, 0xdf, 0xfe,
    0x20, 0x0, 0xdf, 0xf0, 0x1f, 0xfb, 0x0, 0x0,
    0xcf, 0xf4, 0x2f, 0xff, 0xd0, 0x0, 0xdf, 0xf0,
    0x1f, 0xfb, 0x0, 0x7, 0xff, 0xc0, 0x5, 0xff,
    0xfb, 0x0, 0xdf, 0xf0, 0x1f, 0xfb, 0x0, 0x6f,
    0xff, 0x20, 0x0, 0x9f, 0xff, 0x90, 0xdf, 0xf0,
    0x1f, 0xfb, 0x8, 0xff, 0xf7, 0x0, 0x0, 0xd,
    0xff, 0xf5, 0xdf, 0xf0, 0x1f, 0xfb, 0xcf, 0xff,
    0x90, 0x0, 0x0, 0x3, 0xff, 0xb1, 0xdf, 0xf0,
    0x1f, 0xfb, 0x3e, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x77, 0x0, 0xdf, 0xf0, 0x1f, 0xfb, 0x2, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x1f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf0, 0x1f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf0,
    0x1f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xee, 0xef, 0xff, 0xd0, 0x2f, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0x60,
    0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xfe, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x43, 0x10, 0x0, 0x0,

    /* U+53F7 "号" */
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xe5, 0x55,
    0x55, 0x55, 0x55, 0x56, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x87, 0x65, 0x44, 0x44, 0x44, 0x44, 0x45,
    0x67, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x76, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x56, 0x75,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x1f, 0xff,
    0xfe, 0xee, 0xff, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xff, 0xf9, 0x1, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfe, 0xee, 0xee,
    0xee, 0xee, 0xef, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38,
    0x76, 0x55, 0x55, 0x55, 0x55, 0x55, 0x6f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x29, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xec, 0xbb,
    0xce, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xfe, 0xdb, 0x85, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+544A "告" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xec, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x28,
    0x20, 0x0, 0x3f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfc, 0x30,
    0x2f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfc, 0x20, 0x1f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf1, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xc5, 0x55, 0x6f, 0xfb, 0x55, 0x55, 0x55, 0x67,
    0x10, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xa0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4b, 0xfe, 0x10, 0x0, 0x0,
    0x1f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x24, 0x32, 0x35, 0x22, 0x22, 0x22, 0x3f, 0xfa,
    0x22, 0x22, 0x22, 0x22, 0x23, 0x42, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x23, 0x21, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x12, 0x41,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x65, 0x55,
    0x55, 0x55, 0x55, 0x56, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x64, 0x44, 0x44, 0x44, 0x44, 0x46, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x33, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5BB9 "容" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2c, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x65, 0x44,
    0x44, 0x44, 0x44, 0xcf, 0xf9, 0x44, 0x44, 0x44,
    0x56, 0x50, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0xa, 0xff, 0x10, 0x1, 0xd6, 0x0,
    0x0, 0x3, 0xe5, 0x0, 0x4, 0xff, 0x70, 0x0,
    0x0, 0xa, 0xff, 0x10, 0xb, 0xff, 0xd2, 0x0,
    0x2e, 0xff, 0x90, 0x5, 0xff, 0x70, 0x0, 0x0,
    0xb, 0xff, 0x20, 0x8f, 0xff, 0xa2, 0x62, 0x7,
    0xff, 0xfd, 0x27, 0xff, 0x90, 0x0, 0x0, 0x8,
    0xaa, 0x27, 0xff, 0xf5, 0x5, 0xff, 0xb3, 0x3d,
    0xff, 0xe6, 0x55, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xaf, 0xff, 0x50, 0x3f, 0xff, 0xf6, 0x0, 0xbf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf4, 0x2, 0xef, 0xff, 0xf6, 0x0, 0xa, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6d, 0x20,
    0x3e, 0xff, 0xee, 0xff, 0xa0, 0x0, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xfe, 0x22, 0xcf, 0xfe, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xb1,
    0x0, 0xa, 0xff, 0xfe, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x5d, 0xff, 0xff, 0xc7, 0x30, 0x0, 0x0,
    0x26, 0xbf, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0xff, 0xff, 0xff, 0xd5, 0x1d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xa0, 0x3, 0xff, 0xfd,
    0x6b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x39, 0xee, 0x0, 0x0, 0x5a, 0x40, 0xa,
    0xff, 0x54, 0x44, 0x44, 0x44, 0x44, 0xdf, 0xf0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x54, 0x44, 0x44, 0x44, 0x44,
    0xdf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf1, 0x0, 0x0, 0x0,

    /* U+62A5 "报" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xe2, 0x0, 0x27, 0x54, 0x33, 0x33,
    0x33, 0x34, 0x57, 0x20, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0x1f, 0xfb, 0x0, 0x0, 0x0, 0xd, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x0,
    0xf, 0xfb, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x0,
    0x0, 0x8, 0xca, 0xaa, 0xff, 0xda, 0xbd, 0x4f,
    0xfb, 0x0, 0x0, 0x0, 0x2f, 0xfa, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xfb,
    0x2, 0x63, 0x22, 0xaf, 0xf7, 0x0, 0x0, 0x8,
    0xb9, 0x99, 0xff, 0xd9, 0xab, 0x4f, 0xfb, 0x0,
    0xdf, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xa0, 0x0, 0xf, 0xfb, 0x0, 0x6f,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xa0, 0x0, 0xf, 0xfb, 0x0, 0x1a, 0x98,
    0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x18, 0xf, 0xfb, 0x11, 0x11, 0x11, 0x11,
    0x23, 0x30, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa6,
    0xec, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xfb,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x28, 0xff, 0xff, 0xe6, 0xf,
    0xfb, 0xef, 0xf2, 0x11, 0x1d, 0xff, 0x0, 0x0,
    0x2, 0x7c, 0xff, 0xff, 0xe5, 0x0, 0xf, 0xfb,
    0x8f, 0xf6, 0x0, 0x1f, 0xfb, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0xf, 0xfb, 0x1f,
    0xfd, 0x0, 0x6f, 0xf6, 0x0, 0x0, 0x8, 0xff,
    0xc5, 0xff, 0xa0, 0x0, 0xf, 0xfb, 0x9, 0xff,
    0x60, 0xcf, 0xf0, 0x0, 0x0, 0x0, 0xb5, 0x1,
    0xff, 0xa0, 0x0, 0xf, 0xfb, 0x1, 0xff, 0xf5,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xa0, 0x0, 0xf, 0xfb, 0x0, 0x7f, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0xf, 0xfb, 0x0, 0xc, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x0,
    0xf, 0xfb, 0x0, 0x5, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0xf,
    0xfb, 0x0, 0x4f, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0xf, 0xfb,
    0x9, 0xff, 0xf8, 0xef, 0xff, 0xa3, 0x0, 0x0,
    0xcd, 0xce, 0xff, 0x80, 0x0, 0x1f, 0xfe, 0xef,
    0xff, 0x60, 0x1b, 0xff, 0xff, 0xd1, 0x0, 0x4f,
    0xff, 0xff, 0x20, 0x0, 0x3f, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x6f, 0xff, 0x40, 0x0, 0xe, 0xfc,
    0x92, 0x0, 0x0, 0x4f, 0xfc, 0x4c, 0x20, 0x0,
    0x0, 0x1, 0x98, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+6545 "故" */
    0x0, 0x0, 0x2, 0x42, 0x0, 0x0, 0x0, 0x0,
    0xc9, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x60, 0x0, 0x0, 0xf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xc0, 0x0, 0x0, 0x2, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf9,
    0x0, 0x0, 0x0, 0x6f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0,
    0x0, 0x9, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0,
    0xdf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x1, 0xff, 0x90, 0x0, 0x1, 0x1f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xfe, 0xee,
    0xef, 0xff, 0xee, 0xef, 0xf7, 0xff, 0xeb, 0xbb,
    0xbb, 0xbc, 0xce, 0x60, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x7, 0x76, 0x55, 0x6f, 0xfb, 0x55,
    0x67, 0xaf, 0xfe, 0x88, 0x88, 0xdf, 0xfa, 0x8a,
    0x40, 0x0, 0x0, 0x1, 0xff, 0x90, 0x0, 0xa,
    0xff, 0x70, 0x0, 0xa, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf9, 0x0, 0x2, 0xff, 0xf1,
    0x20, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x90, 0x0, 0xcf, 0xfe, 0xec, 0x0,
    0xe, 0xff, 0x0, 0x0, 0x0, 0x25, 0x21, 0x2f,
    0xfa, 0x12, 0x5d, 0xee, 0xff, 0xf0, 0x1, 0xff,
    0xc0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x4b, 0xff, 0x30, 0x4f, 0xf8, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x5f, 0xf8, 0x9, 0xff, 0x50, 0x0, 0x0,
    0x1, 0xff, 0xa2, 0x22, 0x23, 0xff, 0xa0, 0x0,
    0xff, 0xe0, 0xef, 0xf0, 0x0, 0x0, 0x0, 0x1f,
    0xfa, 0x0, 0x0, 0x1f, 0xf9, 0x0, 0x9, 0xff,
    0xbf, 0xfa, 0x0, 0x0, 0x0, 0x1, 0xff, 0xa0,
    0x0, 0x1, 0xff, 0x90, 0x0, 0x1f, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x1f, 0xfa, 0x0, 0x0,
    0x1f, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x1, 0xff,
    0x90, 0x0, 0x4, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xfb, 0x44, 0x44, 0x5f, 0xf9, 0x0,
    0x2, 0xef, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x2, 0xef,
    0xfe, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x3, 0xef, 0xfd, 0x18,
    0xff, 0xfa, 0x10, 0x0, 0x2, 0xff, 0x90, 0x0,
    0x2, 0xff, 0xb6, 0xff, 0xfc, 0x10, 0x8, 0xff,
    0xfe, 0x60, 0x0, 0x3f, 0xfa, 0x0, 0x0, 0x3a,
    0xcf, 0xff, 0xfb, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xe5, 0x3, 0xbb, 0x90, 0x0, 0x1, 0x9f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x4, 0xef, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xc5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8B66 "警" */
    0x0, 0x0, 0x0, 0x43, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x67, 0xff, 0x90, 0x0, 0xf,
    0xfd, 0x30, 0x0, 0x0, 0x0, 0x0, 0x53, 0x22,
    0xaf, 0xf2, 0x7f, 0xf5, 0x34, 0x24, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xaf, 0xfb, 0x99, 0x99,
    0x9a, 0x70, 0x0, 0xec, 0xcd, 0xef, 0xfb, 0xdf,
    0xfc, 0xce, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0xa, 0xfe, 0xdd, 0x6, 0xdd, 0x30,
    0xc, 0xff, 0x97, 0x7e, 0xfd, 0x79, 0x60, 0x0,
    0x4, 0xff, 0xfd, 0xdd, 0xdd, 0xdd, 0xfe, 0xff,
    0xee, 0x40, 0xff, 0x80, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xed, 0xfd,
    0x5f, 0xf4, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x22,
    0x23, 0x51, 0x4f, 0xf4, 0x23, 0x4f, 0xff, 0xfe,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x14, 0xff, 0x30, 0x0, 0x9f, 0xff, 0x60, 0x0,
    0x0, 0x1, 0xb6, 0xdf, 0xdb, 0xcf, 0xf0, 0x5f,
    0xf2, 0x0, 0x18, 0xff, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0xd, 0xfd, 0xac, 0xff, 0x19, 0xff, 0x26,
    0xbf, 0xff, 0xdf, 0xff, 0xfb, 0x60, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x8f, 0xff,
    0x80, 0x2b, 0xff, 0xf3, 0x0, 0x0, 0x3, 0x10,
    0x0, 0x12, 0xce, 0xef, 0xf7, 0x8a, 0x20, 0x0,
    0x2, 0x87, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x2, 0x2, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x13, 0x20, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x2, 0xff, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xef, 0x60, 0x1,
    0x0, 0x7, 0x65, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x73, 0x0, 0x1, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x8, 0x54,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x74,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xa9, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0xa6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcb, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xac, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xd2, 0x22, 0x22, 0x22, 0x22, 0x22, 0x28, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x33, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+969C "障" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x33, 0x33, 0x34,
    0x53, 0x0, 0x0, 0x6f, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xf6, 0x21,
    0x0, 0x1, 0xcf, 0xfb, 0x0, 0x0, 0x21, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x1f, 0xfa,
    0x1, 0xff, 0xb7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x1f, 0xfa, 0x4, 0xff,
    0x73, 0x54, 0xdf, 0xd3, 0x33, 0x4f, 0xff, 0x94,
    0x53, 0x0, 0x1f, 0xfa, 0x8, 0xff, 0x20, 0x0,
    0xaf, 0xfa, 0x0, 0x5f, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xfa, 0xc, 0xfd, 0x76, 0x44, 0x4e, 0xff,
    0x84, 0xdf, 0xf5, 0x44, 0x45, 0x71, 0x1f, 0xfa,
    0x1f, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x1f, 0xfa, 0x6f, 0xf3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x1f, 0xfa, 0xbf, 0xe0, 0x20, 0x43,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x34, 0x40, 0x10,
    0x1f, 0xfa, 0xef, 0xf2, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1f, 0xfa,
    0x3f, 0xfc, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x1f, 0xfa, 0x7, 0xff,
    0x50, 0xbf, 0xf0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x90, 0x0, 0x1f, 0xfa, 0x0, 0xef, 0xb0, 0xbf,
    0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0xff, 0x90, 0x0,
    0x1f, 0xfa, 0x0, 0xbf, 0xf0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x1f, 0xfa,
    0x0, 0xaf, 0xf3, 0xbf, 0xf5, 0x55, 0x55, 0x55,
    0x56, 0xff, 0x90, 0x0, 0x1f, 0xfe, 0xbc, 0xff,
    0xf1, 0xbf, 0xfb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff,
    0x90, 0x0, 0x1f, 0xfc, 0xff, 0xff, 0xe0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x1f, 0xfa, 0xaf, 0xfd, 0x30, 0xdd, 0xcb, 0xbb,
    0xef, 0xfb, 0xbb, 0xcd, 0xb0, 0x0, 0x1f, 0xfa,
    0x58, 0x30, 0x20, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x0, 0x0, 0x0, 0x11, 0x1f, 0xfa, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x1f, 0xfa, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x1f, 0xfa, 0x0, 0x0, 0x65, 0x44, 0x44, 0x44,
    0xbf, 0xf4, 0x44, 0x44, 0x45, 0x62, 0x1f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x3d, 0xdb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x10, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 132, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 146, .box_w = 5, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 50, .adv_w = 210, .box_w = 11, .box_h = 7, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 89, .adv_w = 284, .box_w = 18, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 269, .adv_w = 266, .box_w = 16, .box_h = 25, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 469, .adv_w = 240, .box_w = 15, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 619, .adv_w = 395, .box_w = 23, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 849, .adv_w = 124, .box_w = 4, .box_h = 7, .ofs_x = 2, .ofs_y = 12},
    {.bitmap_index = 863, .adv_w = 159, .box_w = 9, .box_h = 27, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 985, .adv_w = 159, .box_w = 9, .box_h = 27, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 1107, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1185, .adv_w = 333, .box_w = 16, .box_h = 16, .ofs_x = 2, .ofs_y = 1},
    {.bitmap_index = 1313, .adv_w = 116, .box_w = 5, .box_h = 8, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 1333, .adv_w = 240, .box_w = 9, .box_h = 3, .ofs_x = 3, .ofs_y = 5},
    {.bitmap_index = 1347, .adv_w = 116, .box_w = 5, .box_h = 4, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1357, .adv_w = 199, .box_w = 14, .box_h = 23, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 1518, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1668, .adv_w = 266, .box_w = 14, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1808, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1958, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2108, .adv_w = 266, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2268, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2418, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2568, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2718, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2868, .adv_w = 266, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3018, .adv_w = 116, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 3056, .adv_w = 116, .box_w = 5, .box_h = 19, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 3104, .adv_w = 333, .box_w = 16, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3240, .adv_w = 333, .box_w = 16, .box_h = 11, .ofs_x = 2, .ofs_y = 3},
    {.bitmap_index = 3328, .adv_w = 333, .box_w = 16, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3464, .adv_w = 213, .box_w = 13, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3594, .adv_w = 458, .box_w = 27, .box_h = 27, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 3959, .adv_w = 322, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4159, .adv_w = 290, .box_w = 16, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4319, .adv_w = 298, .box_w = 17, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4489, .adv_w = 344, .box_w = 19, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4679, .adv_w = 248, .box_w = 13, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4809, .adv_w = 241, .box_w = 13, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 4939, .adv_w = 335, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 5129, .adv_w = 353, .box_w = 18, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5309, .adv_w = 140, .box_w = 5, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5359, .adv_w = 190, .box_w = 10, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5459, .adv_w = 293, .box_w = 17, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5629, .adv_w = 235, .box_w = 13, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5759, .adv_w = 444, .box_w = 24, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 5999, .adv_w = 368, .box_w = 19, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 6189, .adv_w = 363, .box_w = 21, .box_h = 20, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 6399, .adv_w = 281, .box_w = 15, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 6549, .adv_w = 363, .box_w = 21, .box_h = 26, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 6822, .adv_w = 299, .box_w = 17, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 6992, .adv_w = 261, .box_w = 16, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7152, .adv_w = 265, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7322, .adv_w = 338, .box_w = 17, .box_h = 20, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 7492, .adv_w = 308, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7692, .adv_w = 464, .box_w = 29, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7982, .adv_w = 297, .box_w = 20, .box_h = 20, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 8182, .adv_w = 277, .box_w = 18, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8362, .adv_w = 282, .box_w = 17, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8532, .adv_w = 159, .box_w = 8, .box_h = 27, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 8640, .adv_w = 194, .box_w = 13, .box_h = 21, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8777, .adv_w = 159, .box_w = 8, .box_h = 27, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 8885, .adv_w = 333, .box_w = 17, .box_h = 13, .ofs_x = 2, .ofs_y = 6},
    {.bitmap_index = 8996, .adv_w = 199, .box_w = 14, .box_h = 3, .ofs_x = -1, .ofs_y = -6},
    {.bitmap_index = 9017, .adv_w = 139, .box_w = 8, .box_h = 5, .ofs_x = 0, .ofs_y = 15},
    {.bitmap_index = 9037, .adv_w = 251, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9135, .adv_w = 289, .box_w = 15, .box_h = 21, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 9293, .adv_w = 226, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9391, .adv_w = 289, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9549, .adv_w = 255, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 9654, .adv_w = 165, .box_w = 11, .box_h = 21, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9770, .adv_w = 289, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 9928, .adv_w = 279, .box_w = 14, .box_h = 21, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10075, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10117, .adv_w = 125, .box_w = 7, .box_h = 27, .ofs_x = -1, .ofs_y = -7},
    {.bitmap_index = 10212, .adv_w = 252, .box_w = 14, .box_h = 21, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10359, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10401, .adv_w = 425, .box_w = 23, .box_h = 15, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10574, .adv_w = 280, .box_w = 14, .box_h = 15, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 10679, .adv_w = 287, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 10799, .adv_w = 289, .box_w = 15, .box_h = 21, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 10957, .adv_w = 289, .box_w = 15, .box_h = 21, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 11115, .adv_w = 178, .box_w = 9, .box_h = 15, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 11183, .adv_w = 207, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11281, .adv_w = 173, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11386, .adv_w = 280, .box_w = 14, .box_h = 15, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 11491, .adv_w = 244, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11611, .adv_w = 363, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11784, .adv_w = 240, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11897, .adv_w = 244, .box_w = 17, .box_h = 21, .ofs_x = -1, .ofs_y = -7},
    {.bitmap_index = 12076, .adv_w = 223, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12181, .adv_w = 159, .box_w = 10, .box_h = 27, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12316, .adv_w = 134, .box_w = 4, .box_h = 27, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 12370, .adv_w = 159, .box_w = 10, .box_h = 27, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12505, .adv_w = 333, .box_w = 17, .box_h = 4, .ofs_x = 2, .ofs_y = 7},
    {.bitmap_index = 12539, .adv_w = 480, .box_w = 24, .box_h = 28, .ofs_x = 3, .ofs_y = -5},
    {.bitmap_index = 12875, .adv_w = 480, .box_w = 28, .box_h = 28, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 13267, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 13673, .adv_w = 480, .box_w = 30, .box_h = 28, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14093, .adv_w = 480, .box_w = 30, .box_h = 29, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 14528, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 14949, .adv_w = 480, .box_w = 29, .box_h = 29, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 15370, .adv_w = 480, .box_w = 28, .box_h = 29, .ofs_x = 2, .ofs_y = -5}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x272, 0x2c5, 0xa34, 0x1120, 0x13c0, 0x39e1, 0x4517
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20869, .range_length = 17688, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 8, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t ui_font_FontDengXianBold48 = {
#else
lv_font_t ui_font_FontDengXianBold48 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 31,          /*The maximum line height required by the font*/
    .base_line = 7,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -6,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if UI_FONT_FONTDENGXIANBOLD48*/

