- [HalFlash](#<PERSON>F<PERSON>)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# HalFlash

## 描述

`HalFlash`作为Flash存储器的硬件抽象层。该模块主要负责外部SPI Flash存储器(GD25Q32ETIG)的硬件操作，包括SPI通信的初始化与控制、Flash读写算法的实现，为上层应用提供稳定可靠的数据存储服务。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0037    |   控制盘参数备份   |
| MNT0038 | 控制盘参数全部还原 |
| MNT0039 | 控制盘参数部分还原 |
| MNT0040 | 控制盘恢复出厂值 |

### 软件需求

1) 应能够实现对片外Flash的读操作功能的抽象，包括单字节、多字节、跨页读
2) 应能够实现对片外Flash的写操作功能的抽象，包括单字节、多字节、跨页写
3) 应能够实现写操作结果的返回

### 假设

1) 硬件上连接的是GD25Q32ETIG型号的SPI Flash存储器。
2) SPI引脚已在`main.h`中正确定义并完成时钟配置。
3) CS片选引脚配置为推挽输出模式，默认为高电平。
4) SPI接口配置为主模式，时钟极性和相位符合Flash器件要求。

***

## 平台资源

接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HAL_SPI_Transmit()` | 通过SPI接口发送数据到Flash器件。 |
| `HAL_SPI_Receive()` | 通过SPI接口从Flash器件接收数据。 |
| `HAL_GPIO_WritePin()` | 控制CS片选引脚的电平状态。 |
| `osDelay()` | 提供毫秒级延时功能，用于操作超时控制。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用标准的SPI Flash操作协议。该方案通过SPI总线与外部Flash存储器进行通信，实现数据的读写操作。具体步骤如下：

**读操作流程：**
1. 检查Flash器件是否准备就绪（WIP位为0）。
2. 拉低CS片选信号，选中Flash器件。
3. 发送读命令（0x03）和24位地址。
4. 连续读取指定长度的数据。
5. 拉高CS片选信号，结束操作。

**写操作流程：**
1. 检查Flash器件是否准备就绪（WIP位为0）。
2. 发送写使能命令（0x06），设置WEL位。
3. 拉低CS片选信号，选中Flash器件。
4. 发送页编程命令（0x02）和24位地址。
5. 发送数据（最多256字节/页）。
6. 拉高CS片选信号，启动编程操作。
7. 等待编程完成（WIP位为0）。
8. 对于跨页数据，重复步骤2-7。

### 静态设计

模块的静态设计如下所示：

![类图](Image/HalFlash_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/HalFlash_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：
   
   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `HalFlash_eInit()` | 1       |
   | `HalFlash_eReadData()` | 6 |
   | `HalFlash_eWriteData()` | 10 |
   | `HalFlash_eWriteEnable()` | 5 |
   | `HalFlash_eIsAddressValid()` | 3 |
   | `HalFlash_eReadStatusRegister()` | 4 |
   | `HalFlash_eWaitForReady()` | 4 |
   
2. 其他测试项：目前无免费工具，暂不列出。  

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用STM32CubeIDE搭建测试工程。

   1.2 使用FreeRTOS新建StartFlashTask任务，在其中实现HalFlash模块的SPI Flash读写测试方案。任务调度时间为10ms。

2. 函数测试详细结果

   2.1 HalFlash_eInit()

      2.1.1 分支1：正常初始化

      - 测试用例：调用初始化函数
      - 预期结果：返回 `void`，配置参数正确设置
      - 测试结果：

   2.2 HalFlash_eReadData()

      2.2.1 分支1：空指针检测（缓冲区）

      - 测试用例：传入 `pu8Buffer = NULL_D`
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.2.2 分支2：长度参数检测

      - 测试用例：传入 `u16Length = 0`
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.2.3 分支3：地址范围检测

      - 测试用例：地址超出Flash容量范围
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.2.4 分支4：Flash未准备就绪

      - 测试用例：Flash器件忙碌状态（WIP=1）
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.2.5 分支5：SPI传输失败

      - 测试用例：SPI发送命令失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.2.6 分支6：SPI接收失败

      - 测试用例：SPI接收数据失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.2.7 分支7：正常读取

      - 测试用例：有效参数，Flash准备就绪
      - 预期结果：返回 `HALFLASH_OK_E`，数据正确读取
      - 测试结果：

   2.3 HalFlash_eWriteData()

      2.3.1 分支1：空指针检测（缓冲区）

      - 测试用例：传入 `pu8Buffer = NULL_D`
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.2 分支2：长度参数检测

      - 测试用例：传入 `u16Length = 0`
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.3 分支3：地址范围检测

      - 测试用例：地址超出Flash容量范围
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.4 分支4：Flash未准备就绪

      - 测试用例：Flash器件忙碌状态（WIP=1）
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.5 分支5：写使能失败

      - 测试用例：写使能命令执行失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.6 分支6：SPI传输命令失败

      - 测试用例：SPI发送页编程命令失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.7 分支7：SPI传输数据失败

      - 测试用例：SPI发送数据失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.8 分支8：等待编程完成失败

      - 测试用例：页编程操作超时
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.3.9 分支9：正常写入（单页）

      - 测试用例：256字节以内数据，页内写入
      - 预期结果：返回 `HALFLASH_OK_E`，数据正确写入
      - 测试结果：

      2.3.10 分支10：正常写入（跨页）

      - 测试用例：跨越页边界的数据写入
      - 预期结果：返回 `HALFLASH_OK_E`，数据正确写入
      - 测试结果：

   2.4 HalFlash_eWriteEnable()

      2.4.1 分支1：SPI传输失败

      - 测试用例：SPI发送写使能命令失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.4.2 分支2：状态寄存器读取失败

      - 测试用例：读取状态寄存器失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.4.3 分支3：WEL位验证超时

      - 测试用例：WEL位在超时时间内未设置
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.4.4 分支4：正常写使能

      - 测试用例：写使能命令成功，WEL位正确设置
      - 预期结果：返回 `HALFLASH_OK_E`
      - 测试结果：

   2.5 HalFlash_eIsAddressValid()

      2.5.1 分支1：地址超出范围

      - 测试用例：`u32Address > HALFLASH_MAX_ADDRESS_D`
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.5.2 分支2：长度为0

      - 测试用例：`u16Length = 0`
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.5.3 分支3：地址+长度溢出

      - 测试用例：地址和长度组合超出Flash容量
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.5.4 分支4：地址和长度有效

      - 测试用例：有效的地址和长度参数
      - 预期结果：返回 `HALFLASH_OK_E`
      - 测试结果：

   2.6 HalFlash_eReadStatusRegister()

      2.6.1 分支1：空指针检测

      - 测试用例：传入 `pu8Status = NULL_D`
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.6.2 分支2：SPI传输命令失败

      - 测试用例：SPI发送读状态寄存器命令失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.6.3 分支3：SPI接收失败

      - 测试用例：SPI接收状态寄存器值失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.6.4 分支4：正常读取状态寄存器

      - 测试用例：正常读取操作
      - 预期结果：返回 `HALFLASH_OK_E`，状态值正确读取
      - 测试结果：

   2.7 HalFlash_eWaitForReady()

      2.7.1 分支1：读取状态寄存器失败

      - 测试用例：状态寄存器读取操作失败
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.7.2 分支2：等待超时

      - 测试用例：WIP位在超时时间内未清零
      - 预期结果：返回 `HALFLASH_ERROR_E`
      - 测试结果：

      2.7.3 分支3：Flash准备就绪

      - 测试用例：WIP位为0，Flash准备就绪
      - 预期结果：返回 `HALFLASH_OK_E`
      - 测试结果：

测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

1. 扇区擦除功能：目前只实现了读写功能，未来可增加扇区擦除和整片擦除功能，以支持更完整的Flash操作。

### 可重用性声明

1. 该模块依赖于STM32 HAL库和GlobalTypes类型定义。
2. 该模块依赖于FreeRTOS提供的osDelay延时功能。