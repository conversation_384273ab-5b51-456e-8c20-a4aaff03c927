<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>10</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>0</x>
      <y>180</y>
      <w>730</w>
      <h>200</h>
    </coordinates>
    <panel_attributes>HalLed
--
+HalLed_eInit(void):void
+HalLed_eSetLedState(eLedIndex:const HALLED_LED_INDEX_E, eState:const HALLED_LED_STATE_E):void
+HalLed_eUpdateBlinkLeds(void):void
-HalLed_eSetLedLevel(eLedIndex:const HALLED_LED_INDEX_E, bLevel:const BOOL):void
-HalLed_u32GetTickMs(void):U32
-HalLed_u32GetElapsedTime(u32CurrentTime:const U32, u32StartTime:const U32):U32</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>50</x>
      <y>60</y>
      <w>620</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>HAL_GPIO
--
+HAL_GPIO_WritePin(GPIOx:GPIO_TypeDef*, GPIO_Pin:U16, PinState:GPIO_PinState):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>350</x>
      <y>110</y>
      <w>70</w>
      <h>90</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>130</x>
      <y>430</y>
      <w>460</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>FreeRTOS
--
+xTaskGetTickCount(void):TickType_t</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>350</x>
      <y>370</y>
      <w>70</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>790</x>
      <y>130</y>
      <w>270</w>
      <h>160</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALLED_LED_INDEX_E
--
HALLED_LED_4G_WHITE_E = 0
HALLED_LED_BLUETOOTH_WHITE_E
HALLED_LED_WIFI_WHITE_E
HALLED_LED_STATUS_GREEN_E
HALLED_LED_WARNING_RED_E
HALLED_LED_WARNING_YELLOW_E
HALLED_LED_MAX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>790</x>
      <y>300</y>
      <w>270</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALLED_LED_STATE_E
--
HALLED_STATE_OFF_E = 0
HALLED_STATE_ON_E
HALLED_STATE_BLINK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>720</x>
      <y>210</y>
      <w>90</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>720</x>
      <y>310</y>
      <w>90</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
</diagram>
