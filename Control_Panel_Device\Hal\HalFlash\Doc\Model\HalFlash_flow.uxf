<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>3</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>615</x>
      <y>588</y>
      <w>87</w>
      <h>81</h>
    </coordinates>
    <panel_attributes>HalFlash Init
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>654</x>
      <y>603</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>630</x>
      <y>618</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Configure SPI Handle
Configure GPIO Settings</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>654</x>
      <y>606</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>654</x>
      <y>648</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>654</x>
      <y>630</y>
      <w>9</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>579</x>
      <y>9</y>
      <w>162</w>
      <h>366</h>
    </coordinates>
    <panel_attributes>HalFlash ReadData
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>630</x>
      <y>24</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>612</x>
      <y>39</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>27</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>627</x>
      <y>63</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>51</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>609</x>
      <y>84</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Check address validity</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>72</y>
      <w>42</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid parameters]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>627</x>
      <y>108</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>96</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>609</x>
      <y>129</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Wait for ready</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>117</y>
      <w>36</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid address]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>627</x>
      <y>153</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>141</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>609</x>
      <y>174</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Prepare read command
Setup address bytes</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>162</y>
      <w>36</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Device ready]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>606</x>
      <y>198</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Select device (CS low)
Send command &amp; address</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>186</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>627</x>
      <y>222</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>210</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>609</x>
      <y>243</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Receive data from SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>231</y>
      <w>39</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Command sent]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>627</x>
      <y>267</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>255</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>606</x>
      <y>318</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Return
HALFLASH_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>276</y>
      <w>36</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Data received]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>630</x>
      <y>360</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>330</y>
      <w>9</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>672</x>
      <y>318</y>
      <w>60</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Return
HALFLASH_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>636</x>
      <y>63</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Invalid parameters]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>636</x>
      <y>108</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Invalid address]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>636</x>
      <y>153</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Device not ready]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>636</x>
      <y>222</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Command failed]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>636</x>
      <y>267</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Receive failed]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>699</x>
      <y>66</y>
      <w>9</w>
      <h>258</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;840.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>765</x>
      <y>9</y>
      <w>204</w>
      <h>504</h>
    </coordinates>
    <panel_attributes>HalFlash WriteData
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>861</x>
      <y>24</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>843</x>
      <y>39</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>27</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>858</x>
      <y>63</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>51</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>84</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Check address validity</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>72</y>
      <w>42</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid parameters]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>858</x>
      <y>108</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>96</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>129</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Wait for ready</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>117</y>
      <w>36</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid address]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>858</x>
      <y>153</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>141</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>174</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Initialize variables
Setup page loop</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>162</y>
      <w>36</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Device ready]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>186</y>
      <w>9</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>201</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Calculate bytes to write
Limit to page boundary</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>225</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Enable write
Call HalFlash_eWriteEnable</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>213</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>858</x>
      <y>249</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>237</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>837</x>
      <y>270</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Setup write command
Select device &amp; send cmd</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>258</y>
      <w>36</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Write enabled]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>858</x>
      <y>294</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>282</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>315</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Send data to SPI</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>303</y>
      <w>39</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Command sent]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>858</x>
      <y>339</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>327</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>360</y>
      <w>48</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Deselect device
Wait for write complete</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>348</y>
      <w>30</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Data sent]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>858</x>
      <y>384</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>372</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>405</y>
      <w>48</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Update counters
Next page iteration</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>393</y>
      <w>39</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Write complete]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>783</x>
      <y>177</y>
      <w>87</w>
      <h>255</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Continue loop]</panel_attributes>
    <additional_attributes>190.0;10.0;10.0;10.0;10.0;830.0;270.0;830.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>840</x>
      <y>435</y>
      <w>48</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>414</y>
      <w>9</w>
      <h>27</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>906</x>
      <y>435</y>
      <w>54</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>867</x>
      <y>63</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Invalid parameters]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>867</x>
      <y>108</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Invalid address]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>867</x>
      <y>153</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Device not ready]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>867</x>
      <y>249</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Write enable failed]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>867</x>
      <y>294</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Command failed]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>867</x>
      <y>339</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Data send failed]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>867</x>
      <y>384</y>
      <w>72</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Write not complete]</panel_attributes>
    <additional_attributes>10.0;20.0;220.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>930</x>
      <y>66</y>
      <w>9</w>
      <h>375</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;1230.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>390</x>
      <y>9</y>
      <w>165</w>
      <h>210</h>
    </coordinates>
    <panel_attributes>HalFlash WriteEnable
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>468</x>
      <y>24</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>447</x>
      <y>39</y>
      <w>51</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Select device (CS low)
Send WREN command</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>27</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>465</x>
      <y>63</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>51</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>444</x>
      <y>84</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Deselect device (CS high)
Start WEL verification</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>72</y>
      <w>39</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Command sent]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>465</x>
      <y>108</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>96</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>129</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Read status register
Check WEL bit</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>117</y>
      <w>33</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Timeout &gt; 0]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>465</x>
      <y>153</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>141</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>450</x>
      <y>174</y>
      <w>42</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>162</y>
      <w>27</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[WEL set]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>405</x>
      <y>129</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Delay 1ms
Decrement timeout</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>423</x>
      <y>153</y>
      <w>48</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[WEL not set]</panel_attributes>
    <additional_attributes>140.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>423</x>
      <y>111</y>
      <w>48</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>140.0;10.0;10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>423</x>
      <y>141</y>
      <w>9</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>498</x>
      <y>174</y>
      <w>48</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>474</x>
      <y>63</y>
      <w>54</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Command failed]</panel_attributes>
    <additional_attributes>10.0;20.0;160.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>474</x>
      <y>108</y>
      <w>54</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Timeout = 0]</panel_attributes>
    <additional_attributes>10.0;20.0;160.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>519</x>
      <y>66</y>
      <w>9</w>
      <h>114</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;360.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>402</x>
      <y>237</y>
      <w>144</w>
      <h>171</h>
    </coordinates>
    <panel_attributes>HalFlash IsAddressValid
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>447</x>
      <y>252</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>423</x>
      <y>267</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Check basic range
Address &amp; length validity</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>447</x>
      <y>255</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>444</x>
      <y>291</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>447</x>
      <y>279</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>423</x>
      <y>312</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Check overflow
Prevent address overflow</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>447</x>
      <y>300</y>
      <w>30</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Basic valid]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>444</x>
      <y>336</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>447</x>
      <y>324</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>429</x>
      <y>357</y>
      <w>42</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>447</x>
      <y>345</y>
      <w>33</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[No overflow]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>480</x>
      <y>357</y>
      <w>51</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>291</y>
      <w>57</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Invalid range]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>336</y>
      <w>57</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Overflow detected]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>501</x>
      <y>294</y>
      <w>9</w>
      <h>69</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;210.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>399</x>
      <y>429</y>
      <w>150</w>
      <h>261</h>
    </coordinates>
    <panel_attributes>HalFlash ReadStatusRegister
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>453</x>
      <y>444</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>435</x>
      <y>459</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Check entry point</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>447</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>450</x>
      <y>483</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>471</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>429</x>
      <y>504</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Select device (CS low)
Send RDSR command</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>492</y>
      <w>33</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid pointer]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>450</x>
      <y>528</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>516</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>435</x>
      <y>549</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Read status register
Receive 1 byte</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>537</y>
      <w>39</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Command sent]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>450</x>
      <y>573</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>561</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>429</x>
      <y>594</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Deselect device (CS high)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>582</y>
      <w>36</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Data received]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>489</x>
      <y>621</y>
      <w>48</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>459</x>
      <y>483</y>
      <w>60</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Null pointer]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>459</x>
      <y>528</y>
      <w>60</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Command failed]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>459</x>
      <y>573</y>
      <w>60</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Receive failed]</panel_attributes>
    <additional_attributes>10.0;20.0;180.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>510</x>
      <y>486</y>
      <w>9</w>
      <h>141</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;450.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>579</x>
      <y>390</y>
      <w>162</w>
      <h>180</h>
    </coordinates>
    <panel_attributes>HalFlash WaitForReady
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>651</x>
      <y>405</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>633</x>
      <y>420</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Initialize timeout
Start polling loop</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>651</x>
      <y>408</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>648</x>
      <y>444</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>651</x>
      <y>432</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>633</x>
      <y>465</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Read status register</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>651</x>
      <y>453</y>
      <w>33</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Timeout &gt; 0]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>651</x>
      <y>477</y>
      <w>9</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>648</x>
      <y>489</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>633</x>
      <y>510</y>
      <w>42</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>651</x>
      <y>498</y>
      <w>27</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[WIP = 0]</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>585</x>
      <y>465</y>
      <w>42</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Delay 1ms
Decrement timeout</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>603</x>
      <y>489</y>
      <w>51</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[WIP = 1]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>603</x>
      <y>447</y>
      <w>51</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>150.0;10.0;10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>603</x>
      <y>477</y>
      <w>9</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;60.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>684</x>
      <y>510</y>
      <w>48</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return 
HALFLASH_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>657</x>
      <y>444</y>
      <w>57</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>[Timeout = 0]</panel_attributes>
    <additional_attributes>10.0;20.0;170.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>705</x>
      <y>447</y>
      <w>9</w>
      <h>69</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;210.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>606</x>
      <y>291</y>
      <w>54</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>Deselect device (CS high)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>303</y>
      <w>9</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>630</x>
      <y>330</y>
      <w>78</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;60.0;240.0;60.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>861</x>
      <y>468</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>444</y>
      <w>9</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>861</x>
      <y>444</y>
      <w>78</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;240.0;40.0;240.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>630</y>
      <w>9</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>630</y>
      <w>66</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;200.0;40.0;200.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>453</x>
      <y>654</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>429</x>
      <y>621</y>
      <w>54</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>Return
HALFLASH_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>453</x>
      <y>606</y>
      <w>9</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>183</y>
      <w>9</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>468</x>
      <y>183</y>
      <w>60</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;180.0;40.0;180.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>468</x>
      <y>207</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>447</x>
      <y>366</y>
      <w>9</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>447</x>
      <y>366</y>
      <w>63</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;190.0;40.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>447</x>
      <y>390</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>651</x>
      <y>519</y>
      <w>9</w>
      <h>30</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>651</x>
      <y>519</y>
      <w>63</w>
      <h>18</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;190.0;40.0;190.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>651</x>
      <y>543</y>
      <w>6</w>
      <h>6</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
</diagram>
