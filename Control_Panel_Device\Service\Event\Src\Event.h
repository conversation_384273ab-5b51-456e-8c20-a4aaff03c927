//----------------------------------------------------------------------------
/**
* @file Event.h
* @remark Event module public function declaration - Publisher-Subscriber pattern implementation.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef EVENT_H_
#define EVENT_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "GlobalTypes.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------
#define EVENT_MAX_EVENT_DATA_SIZE_D     2U              /* Maximum size of event data in bytes */

/**
* @brief Event module status enumeration
*/
typedef enum
{
    EVENT_OK_E = 0U,                                    /* Operation successful */
    EVENT_ERROR_E                                       /* General error */
} EVENT_STATUS_E;

/**
* @brief Event subscriber enumeration - All possible event subscribers in the system
*/
typedef enum
{
    EVENT_SUBSCRIBER_LED_HANDLE_E,                      /* LedHandle module - LED control */
    EVENT_SUBSCRIBER_UI_HANDLE_E,                       /* UiHandle module - User interface */

    /* ... */

    EVENT_SUBSCRIBER_MAX_E                              /* Maximum subscriber type (boundary) */
} EVENT_SUBSCRIBER_E;

/**
* @brief Event type enumeration - All possible events in the system
*/
typedef enum
{
    EVENT_TYPE_NONE_E = 0U,                             /* No event / Invalid event (default) */
    
    /* KeyHandle - Single key short press events */
    EVENT_TYPE_KEY_LEFT_FUNC_SHORT_PRESS_E,             /* Left function key short press */
    EVENT_TYPE_KEY_RIGHT_FUNC_SHORT_PRESS_E,            /* Right function key short press */
    EVENT_TYPE_KEY_UP_SHORT_PRESS_E,                    /* Up direction key short press */
    EVENT_TYPE_KEY_DOWN_SHORT_PRESS_E,                  /* Down direction key short press */
    EVENT_TYPE_KEY_LEFT_SHORT_PRESS_E,                  /* Left direction key short press */
    EVENT_TYPE_KEY_RIGHT_SHORT_PRESS_E,                 /* Right direction key short press */
    EVENT_TYPE_KEY_START_SHORT_PRESS_E,                 /* Start key short press */
    EVENT_TYPE_KEY_STOP_SHORT_PRESS_E,                  /* Stop key short press */
    EVENT_TYPE_KEY_HELP_SHORT_PRESS_E,                  /* Help key short press */
    EVENT_TYPE_KEY_CONTROL_SWITCH_SHORT_PRESS_E,        /* Control authority switching key short press */
    
    /* KeyHandle - Single key long press events */
    EVENT_TYPE_KEY_LEFT_FUNC_LONG_PRESS_E,              /* Left function key long press */
    EVENT_TYPE_KEY_RIGHT_FUNC_LONG_PRESS_E,             /* Right function key long press */
    EVENT_TYPE_KEY_UP_LONG_PRESS_E,                     /* Up direction key long press */
    EVENT_TYPE_KEY_DOWN_LONG_PRESS_E,                   /* Down direction key long press */
    EVENT_TYPE_KEY_LEFT_LONG_PRESS_E,                   /* Left direction key long press */
    EVENT_TYPE_KEY_RIGHT_LONG_PRESS_E,                  /* Right direction key long press */
    EVENT_TYPE_KEY_START_LONG_PRESS_E,                  /* Start key long press */
    EVENT_TYPE_KEY_STOP_LONG_PRESS_E,                   /* Stop key long press */
    EVENT_TYPE_KEY_HELP_LONG_PRESS_E,                   /* Help key long press */
    EVENT_TYPE_KEY_CONTROL_SWITCH_LONG_PRESS_E,         /* Control authority switching key long press */
    
    /* KeyHandle - Combo key short press events */
    EVENT_TYPE_KEY_LEFT_RIGHT_COMBO_PRESS_E,            /* Left+Right direction keys combo press */
    EVENT_TYPE_KEY_LEFT_UP_COMBO_PRESS_E,               /* Left+Up direction keys combo press */
    EVENT_TYPE_KEY_LEFT_DOWN_COMBO_PRESS_E,             /* Left+Down direction keys combo press */
    EVENT_TYPE_KEY_RIGHT_UP_COMBO_PRESS_E,              /* Right+Up direction keys combo press */
    EVENT_TYPE_KEY_RIGHT_DOWN_COMBO_PRESS_E,            /* Right+Down direction keys combo press */
    EVENT_TYPE_KEY_LEFT_HELP_COMBO_PRESS_E,             /* Left+Help keys combo press */
    
    /* 4G Communication */
    EVENT_TYPE_4G_COMM_ENABLED_E,                       /* 4G communication enabled event */
    EVENT_TYPE_4G_COMM_DISABLED_E,                      /* 4G communication disabled event */
    EVENT_TYPE_4G_COMM_ERROR_E,                         /* 4G communication error event */
    
    /* Bluetooth Communication */
    EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E,                /* Bluetooth communication enabled event */
    EVENT_TYPE_BLUETOOTH_COMM_DISABLED_E,               /* Bluetooth communication disabled event */
    EVENT_TYPE_BLUETOOTH_COMM_ERROR_E,                  /* Bluetooth communication error event */
    
    /* WiFi Communication */
    EVENT_TYPE_WIFI_COMM_ENABLED_E,                     /* WiFi communication enabled event */
    EVENT_TYPE_WIFI_COMM_DISABLED_E,                    /* WiFi communication disabled event */
    EVENT_TYPE_WIFI_COMM_ERROR_E,                       /* WiFi communication error event */
    
    /* Device status */
    EVENT_TYPE_DEVICE_SHUTDOWN_E,                       /* Device shutdown event */
    EVENT_TYPE_DEVICE_STANDBY_E,                        /* Device standby event */
    EVENT_TYPE_DEVICE_RUNNING_E,                        /* Device running event */

    /* Device fault and warning */
    EVENT_TYPE_DEVICE_NO_FAULT_WARNING_E,               /* Device has no fault and warning */
    EVENT_TYPE_DEVICE_CRITICAL_FAULT_E,                 /* Device has critical fault */
    EVENT_TYPE_DEVICE_MINOR_FAULT_E,                    /* Device has minor fault */
    EVENT_TYPE_DEVICE_WARNING_E,                        /* Device has warning */

    /* ... */

    EVENT_TYPE_MAX_E                                    /* Maximum event type (boundary) */
} EVENT_EVENT_TYPE_E;

/**
* @brief Event data structure - Contains all event information
*/
typedef struct
{
    EVENT_EVENT_TYPE_E eEventType;                      /* Event type */
    U8 u8DataSize;                                      /* Size of attached data in bytes */
    U8 au8Data[EVENT_MAX_EVENT_DATA_SIZE_D];            /* Event attached data buffer */
} EVENT_EVENT_DATA_T;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
EVENT_STATUS_E Event_eInit(void);
EVENT_STATUS_E Event_eSubscribe(const EVENT_EVENT_TYPE_E eEventType, void (*pvCallback)(const EVENT_EVENT_DATA_T* const psEventData), const EVENT_SUBSCRIBER_E eSubscriberId);
EVENT_STATUS_E Event_eUnsubscribe(const EVENT_EVENT_TYPE_E eEventType, const EVENT_SUBSCRIBER_E eSubscriberId);
EVENT_STATUS_E Event_eUnsubscribeAll(const EVENT_SUBSCRIBER_E eSubscriberId);
EVENT_STATUS_E Event_ePublish(const EVENT_EVENT_TYPE_E eEventType, const U8* const pu8Data, const U8 u8DataSize);
EVENT_STATUS_E Event_eProcessSubscriberEvents(const EVENT_SUBSCRIBER_E eSubscriberId);

#endif /* EVENT_H_ */

//===========================================================================
// End of file.
//===========================================================================








