<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>5</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>345</x>
      <y>285</y>
      <w>625</w>
      <h>200</h>
    </coordinates>
    <panel_attributes>Event
--
+Event_eInit(void):EVENT_STATUS_E
+Event_eSubscribe(eEventType:const EVENT_EVENT_TYPE_E, pvCallback:void (*)(const EVENT_EVENT_DATA_T* const), eSubscriberId:const EVENT_SUBSCRIBER_E):EVENT_STATUS_E
+Event_eUnsubscribe(eEventType:const EVENT_EVENT_TYPE_E, eSubscriberId:const EVENT_SUBSCRIBER_E):EVENT_STATUS_E
+Event_eUnsubscribeAll(eSubscriberId:const EVENT_SUBSCRIBER_E):EVENT_STATUS_E
+Event_ePublish(eEventType:const EVENT_EVENT_TYPE_E, pu8Data:const U8* const, u8DataSize:const U8):EVENT_STATUS_E
+Event_eProcessSubscriberEvents(eSubscriberId:const EVENT_SUBSCRIBER_E):EVENT_STATUS_E
-Event_eLockSubscriberMutex(psSubscriber:EVENT_SUBSCRIBER_T* const):EVENT_STATUS_E
-Event_eUnlockSubscriberMutex(psSubscriber:EVENT_SUBSCRIBER_T* const):EVENT_STATUS_E
-Event_bIsSubscriberSubscribedToEvent(psSubscriber:EVENT_SUBSCRIBER_T* const, eEventType:const EVENT_EVENT_TYPE_E):BOOL
-Event_eEnqueueEventToSubscriber(psSubscriber:EVENT_SUBSCRIBER_T* const, psEventData:const EVENT_EVENT_DATA_T* const):EVENT_STATUS_E
-Event_eDequeueEventFromSubscriber(psSubscriber:EVENT_SUBSCRIBER_T* const, psEventData:EVENT_EVENT_DATA_T* const):EVENT_STATUS_E
-Event_psAllocateSubscriptionNode(void):EVENT_SUBSCRIPTION_NODE_T*
-Event_vFreeSubscriptionNode(psNode:EVENT_SUBSCRIPTION_NODE_T* const):void
-Event_psAllocateEventNode(void):EVENT_EVENT_NODE_T*
-Event_vFreeEventNode(psNode:EVENT_EVENT_NODE_T* const):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1000</x>
      <y>350</y>
      <w>200</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
EVENT_EVENT_DATA_T
--
+eEventType:EVENT_EVENT_TYPE_E
+u8DataSize:U8
+au8Data[EVENT_MAX_EVENT_DATA_SIZE_D]:U8</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1000</x>
      <y>420</y>
      <w>200</w>
      <h>60</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
EVENT_SUBSCRIBER_E
--
EVENT_SUBSCRIBER_LED_HANDLE_E
EVENT_SUBSCRIBER_UI_HANDLE_E
EVENT_SUBSCRIBER_MAX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>105</x>
      <y>180</y>
      <w>215</w>
      <h>410</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
EVENT_EVENT_TYPE_E
--
EVENT_TYPE_NONE_E = 0U
EVENT_TYPE_KEY_LEFT_FUNC_SHORT_PRESS_E
EVENT_TYPE_KEY_RIGHT_FUNC_SHORT_PRESS_E
EVENT_TYPE_KEY_UP_SHORT_PRESS_E
EVENT_TYPE_KEY_DOWN_SHORT_PRESS_E
EVENT_TYPE_KEY_LEFT_SHORT_PRESS_E
EVENT_TYPE_KEY_RIGHT_SHORT_PRESS_E
EVENT_TYPE_KEY_START_SHORT_PRESS_E
EVENT_TYPE_KEY_STOP_SHORT_PRESS_E
EVENT_TYPE_KEY_HELP_SHORT_PRESS_E
EVENT_TYPE_KEY_CONTROL_SWITCH_SHORT_PRESS_E
EVENT_TYPE_KEY_LEFT_FUNC_LONG_PRESS_E
EVENT_TYPE_KEY_RIGHT_FUNC_LONG_PRESS_E
EVENT_TYPE_KEY_UP_LONG_PRESS_E
EVENT_TYPE_KEY_DOWN_LONG_PRESS_E
EVENT_TYPE_KEY_LEFT_LONG_PRESS_E
EVENT_TYPE_KEY_RIGHT_LONG_PRESS_E
EVENT_TYPE_KEY_START_LONG_PRESS_E
EVENT_TYPE_KEY_STOP_LONG_PRESS_E
EVENT_TYPE_KEY_HELP_LONG_PRESS_E
EVENT_TYPE_KEY_CONTROL_SWITCH_LONG_PRESS_E
EVENT_TYPE_KEY_LEFT_RIGHT_COMBO_PRESS_E
EVENT_TYPE_KEY_LEFT_UP_COMBO_PRESS_E
EVENT_TYPE_KEY_LEFT_DOWN_COMBO_PRESS_E
EVENT_TYPE_KEY_RIGHT_UP_COMBO_PRESS_E
EVENT_TYPE_KEY_RIGHT_DOWN_COMBO_PRESS_E
EVENT_TYPE_KEY_LEFT_HELP_COMBO_PRESS_E
EVENT_TYPE_4G_COMM_ENABLED_E
EVENT_TYPE_4G_COMM_DISABLED_E
EVENT_TYPE_4G_COMM_CONNECTED_E
EVENT_TYPE_4G_COMM_ERROR_E
EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E
EVENT_TYPE_BLUETOOTH_COMM_DISABLED_E
EVENT_TYPE_BLUETOOTH_COMM_CONNECTED_E
EVENT_TYPE_BLUETOOTH_COMM_ERROR_E
EVENT_TYPE_WIFI_COMM_ENABLED_E
EVENT_TYPE_WIFI_COMM_DISABLED_E
EVENT_TYPE_WIFI_COMM_CONNECTED_E
EVENT_TYPE_WIFI_COMM_ERROR_E
EVENT_TYPE_DEVICE_STARTUP_E
EVENT_TYPE_DEVICE_STANDBY_E
EVENT_TYPE_DEVICE_RUNNING_E
EVENT_TYPE_DEVICE_SHUTDOWN_E
EVENT_TYPE_DEVICE_NO_FAULT_WARNING_E
EVENT_TYPE_DEVICE_CRITICAL_FAULT_E
EVENT_TYPE_DEVICE_MINOR_FAULT_E
EVENT_TYPE_DEVICE_WARNING_E
EVENT_TYPE_MAX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1000</x>
      <y>285</y>
      <w>135</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
EVENT_STATUS_E
--
EVENT_OK_E = 0U
EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>345</x>
      <y>180</y>
      <w>310</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>FreeRTOS
--
+xSemaphoreCreateMutex(void):SemaphoreHandle_t
+xSemaphoreTake(xSemaphore:SemaphoreHandle_t, xTicksToWait:TickType_t):BaseType_t
+xSemaphoreGive(xSemaphore:SemaphoreHandle_t):BaseType_t
+vSemaphoreDelete(xSemaphore:SemaphoreHandle_t):void
+pdMS_TO_TICKS(xTimeInMs:const TickType_t):TickType_t</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>740</x>
      <y>210</y>
      <w>230</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>string.h
--
+memset(s:void*, c:int, n:size_t):void*
+memcpy(dest:void*, src:const void*, n:size_t):void*</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>965</x>
      <y>375</y>
      <w>45</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>70.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>965</x>
      <y>445</y>
      <w>45</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>70.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>315</x>
      <y>385</y>
      <w>40</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;60.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>965</x>
      <y>305</y>
      <w>45</w>
      <h>15</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>495</x>
      <y>245</y>
      <w>35</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;80.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>850</x>
      <y>245</y>
      <w>35</w>
      <h>50</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;80.0</additional_attributes>
  </element>
</diagram>
