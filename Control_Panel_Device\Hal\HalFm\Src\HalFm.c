//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file HalFm.c
*
* @brief FRAM/EEPROM hardware abstraction layer implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "HalFm.h"
#include "main.h"
#include "spi.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define HALFM_CHIP_SIZE_BYTES_D         16384U                              /* GT25C128A chip size: 128Kbit = 16KB */
#define HALFM_MAX_ADDRESS_D             (HALFM_CHIP_SIZE_BYTES_D - 1U)      /* Maximum valid address */
#define HALFM_PAGE_SIZE_BYTES_D         64U                                 /* Page size for write operations */
#define HALFM_SPI_TIMEOUT_MS_D          1000U                               /* SPI operation timeout in milliseconds */
#define HALFM_CMD_READ_D                0x03U                               /* Read data from memory */
#define HALFM_CMD_WRITE_D               0x02U                               /* Write data to memory */
#define HALFM_CMD_WRITE_ENABLE_D        0x06U                               /* Enable write operations */
#define HALFM_SPI_HANDLE_D              (&hspi1)                            /* SPI handle */
#define HALFM_CS_GPIO_PORT_D            (FM_CS_GPIO_Port)                   /* CS GPIO port */
#define HALFM_CS_GPIO_PIN_D             (FM_CS_Pin)                         /* CS GPIO pin */
#define HALFM_CS_ACTIVE_LEVEL_D         FALSE_D                             /* CS active level (FALSE_D for active low) */

#define HALFM_CS_SELECT_D()             HAL_GPIO_WritePin(sHalFmConfig.psCsGpioPort, sHalFmConfig.u16CsGpioPin, (sHalFmConfig.bCsActiveLevel) ? (GPIO_PIN_SET) : (GPIO_PIN_RESET))
#define HALFM_CS_DESELECT_D()           HAL_GPIO_WritePin(sHalFmConfig.psCsGpioPort, sHalFmConfig.u16CsGpioPin, (sHalFmConfig.bCsActiveLevel) ? (GPIO_PIN_RESET) : (GPIO_PIN_SET))

/**
* @brief HAL layer FM configuration structure
*/
typedef struct
{
    SPI_HandleTypeDef* psSpiHandle;             /* SPI handle pointer */
    GPIO_TypeDef* psCsGpioPort;                 /* CS GPIO port pointer */
    U16 u16CsGpioPin;                           /* CS GPIO pin number */
    BOOL bCsActiveLevel;                        /* CS active level (TRUE_D for active high, FALSE_D for active low) */
} HALFM_CONFIG_T;

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static HALFM_STATUS_E HalFm_eWriteEnable(void);
static HALFM_STATUS_E HalFm_eIsAddressValid(const U16 u16Address, const U16 u16Length);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------
static HALFM_CONFIG_T sHalFmConfig;             /* Static FM configuration */

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize FM hardware abstraction layer.
* @remark None.
*
* @return None.
*/
void HalFm_eInit(void)
{
    /* Configure SPI interface using predefined macros */
    sHalFmConfig.psSpiHandle = HALFM_SPI_HANDLE_D;
    sHalFmConfig.psCsGpioPort = HALFM_CS_GPIO_PORT_D;
    sHalFmConfig.u16CsGpioPin = HALFM_CS_GPIO_PIN_D;
    sHalFmConfig.bCsActiveLevel = HALFM_CS_ACTIVE_LEVEL_D;
}

/**
* @brief Read data from GT25C128A EEPROM.
* @remark None.
*
* @param u16Address [in]: Start address to read from.
* @param pu8Buffer [out]: Buffer to store read data.
* @param u16Length [in]: Number of bytes to read.
* @return Operation status.
*/
HALFM_STATUS_E HalFm_eReadData(const U16 u16Address, U8* const pu8Buffer, const U16 u16Length)
{
    /* Parameter validity check */
    if ((pu8Buffer == NULL_D) || (u16Length == 0U))
    {
        return HALFM_ERROR_E;
    }
    
    /* Check address range */
    if (HalFm_eIsAddressValid(u16Address, u16Length) != HALFM_OK_E)
    {
        return HALFM_ERROR_E;
    }

    U8 au8Command[3U] = {0U, 0U, 0U};
    
    /* Prepare read command */
    au8Command[0U] = HALFM_CMD_READ_D;
    au8Command[1U] = (U8)((u16Address >> 8U) & 0xFFU);
    au8Command[2U] = (U8)(u16Address & 0xFFU);
    
    /* Select device */
    HALFM_CS_SELECT_D();
    
    /* Send read command and address */
    if (HAL_SPI_Transmit(sHalFmConfig.psSpiHandle, (uint8_t*)au8Command, 3U, HALFM_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFM_CS_DESELECT_D();

        return HALFM_ERROR_E;
    }
    
    /* Read data */
    if (HAL_SPI_Receive(sHalFmConfig.psSpiHandle, (uint8_t*)pu8Buffer, (uint16_t)u16Length, HALFM_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFM_CS_DESELECT_D();

        return HALFM_ERROR_E;
    }
    
    /* Deselect device */
    HALFM_CS_DESELECT_D();
    
    return HALFM_OK_E;
}

/**
* @brief Write data to GT25C128A EEPROM.
* @remark None.
*
* @param u16Address [in]: Start address to write to.
* @param pu8Buffer [in]: Buffer containing data to write.
* @param u16Length [in]: Number of bytes to write.
* @return Operation status.
*/
HALFM_STATUS_E HalFm_eWriteData(const U16 u16Address, const U8* const pu8Buffer, const U16 u16Length)
{
    /* Parameter validity check */
    if ((pu8Buffer == NULL_D) || (u16Length == 0U))
    {
        return HALFM_ERROR_E;
    }
    
    /* Check address range */
    if (HalFm_eIsAddressValid(u16Address, u16Length) != HALFM_OK_E)
    {
        return HALFM_ERROR_E;
    }

    U16 u16CurrentIndex = 0U;
    U16 u16CurrentAddress = u16Address;
    U16 u16RemainingLength = u16Length;
    
    /* Write data page by page */
    while (u16RemainingLength > 0U)
    {
        U16 u16BytesToWrite = HALFM_PAGE_SIZE_BYTES_D - (u16CurrentAddress % HALFM_PAGE_SIZE_BYTES_D);

        /* Limit bytes to write to remaining bytes */
        if (u16BytesToWrite > u16RemainingLength)
        {
            u16BytesToWrite = u16RemainingLength;
        }
        
        /* Enable write */
        if (HalFm_eWriteEnable() != HALFM_OK_E)
        {
            return HALFM_ERROR_E;
        }

        U8 au8Command[3U] = {0U, 0U, 0U};
        
        /* Prepare write command */
        au8Command[0U] = HALFM_CMD_WRITE_D;
        au8Command[1U] = (U8)((u16CurrentAddress >> 8U) & 0xFFU);
        au8Command[2U] = (U8)(u16CurrentAddress & 0xFFU);
        
        /* Select device */
        HALFM_CS_SELECT_D();
        
        /* Send write command and address */
        if (HAL_SPI_Transmit(sHalFmConfig.psSpiHandle, (uint8_t*)au8Command, 3U, HALFM_SPI_TIMEOUT_MS_D) != HAL_OK)
        {
            HALFM_CS_DESELECT_D();

            return HALFM_ERROR_E;
        }
        
        /* Send data */
        if (HAL_SPI_Transmit(sHalFmConfig.psSpiHandle, (uint8_t*)&pu8Buffer[u16CurrentIndex], (uint16_t)u16BytesToWrite, HALFM_SPI_TIMEOUT_MS_D) != HAL_OK)
        {
            HALFM_CS_DESELECT_D();

            return HALFM_ERROR_E;
        }
        
        /* Deselect device */
        HALFM_CS_DESELECT_D();
        
        /* Update counters */
        u16CurrentAddress += u16BytesToWrite;
        u16CurrentIndex += u16BytesToWrite;
        u16RemainingLength -= u16BytesToWrite;
    }
    
    return HALFM_OK_E;
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Enable write operations on GT25C128A.
* @remark None.
*
* @return Operation status.
*/
HALFM_STATUS_E HalFm_eWriteEnable(void)
{
    U8 u8Command = HALFM_CMD_WRITE_ENABLE_D;
    
    /* Select device */
    HALFM_CS_SELECT_D();
    
    /* Send write enable command */
    if (HAL_SPI_Transmit(sHalFmConfig.psSpiHandle, (uint8_t*)&u8Command, 1U, HALFM_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFM_CS_DESELECT_D();

        return HALFM_ERROR_E;
    }
    
    /* Deselect device */
    HALFM_CS_DESELECT_D();
    
    return HALFM_OK_E;
}

/**
* @brief Check if address and length are valid for the device.
* @remark None.
*
* @param u16Address [in]: Start address.
* @param u16Length [in]: Data length.
* @return Validation status.
*/
HALFM_STATUS_E HalFm_eIsAddressValid(const U16 u16Address, const U16 u16Length)
{
    /* Check for address overflow */
    if (((U32)u16Address + u16Length - 1U) > HALFM_MAX_ADDRESS_D)
    {
        return HALFM_ERROR_E;
    }
    
    return HALFM_OK_E;
}

//===========================================================================
// End of file.
//===========================================================================








