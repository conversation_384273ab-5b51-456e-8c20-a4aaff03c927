- [HalLed](#halled)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# HalLed

## 描述

`HalLed`作为LED指示灯的硬件抽象层。该模块主要负责LED指示灯硬件操作，包括GPIO的初始化与控制、LED状态管理、闪烁功能的实现，为上层应用提供统一的LED控制接口。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0071    |   4G指示灯   |
| MNT0072 | 蓝牙指示灯 |
| MNT0073 | 运行指示灯 |
| MNT0074 | 故障警告指示灯 |

### 软件需求

1) 应能够实现4G、WiFi、蓝牙指示灯的点亮和熄灭
2) 应能够实现故障警告灯的点亮和熄灭，以及双色切换
3) 应能够实现运行灯的点亮和熄灭
4) 应能够实现控制盘LED灯的控制功能的硬件抽象

### 假设

1) GPIO引脚已在`main.h`中正确定义并完成时钟配置。
2) 输出引脚配置为推挽输出模式。
3) LED通过GPIO引脚直接控制，无需额外的驱动电路。
4) 系统提供FreeRTOS时钟源用于闪烁时间计算。

***

## 平台资源

接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HAL_GPIO_WritePin()` | 设置GPIO引脚的输出电平（高/低）。 |
| `xTaskGetTickCount()` | 获取FreeRTOS系统时钟计数，用于时间计算。 |
| `portTICK_PERIOD_MS` | FreeRTOS时钟周期常量，用于时间单位转换。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块通过维护每个LED的状态信息（开/关/闪烁）和时间戳，实现对多个LED的独立控制。具体设计特点如下：

1. LED状态管理：每个LED维护独立的状态（OFF/ON/BLINK）。
2. 闪烁功能：通过定时器机制实现LED的周期性开关切换。
3. 时间管理：使用FreeRTOS系统时钟进行时间计算，支持32位溢出处理。
4. 硬件抽象：通过配置结构体抽象不同LED的GPIO端口和引脚配置。

通过这种方式，可以用统一的接口控制多个不同类型的LED指示灯，支持常亮、熄灭和闪烁三种基本状态。

### 静态设计

模块的静态设计如下所示：

![类图](Image/HalLed_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/HalLed_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：

   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `HalLed_eInit()` | 2      |
   | `HalLed_eSetLedState()` | 2 |
   | `HalLed_eUpdateBlinkLeds()` | 4 |
   | `HalLed_eSetLedLevel()` | 1 |
   | `HalLed_u32GetTickMs()` | 1 |
   | `HalLed_u32GetElapsedTime()` | 2 |

2. 其他测试项：目前无免费工具，暂不列出。

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用FreeRTOS新建StartLedTask任务，在其中实现HalLed模块的LED控制测试方案。任务调度时间为10ms。

2. 函数测试详细结果

   2.1 HalLed_eInit()

      2.1.1 分支1：正常初始化

      - 测试用例：调用`HalLed_eInit()`进行模块初始化
      - 预期结果：所有LED配置正确，初始状态为关闭，GPIO输出为非激活电平
      - 测试结果：

   2.2 HalLed_eSetLedState()

      2.2.1 分支1：设置LED关闭状态

      - 函数调用：`HalLed_eSetLedState(HALLED_LED_4G_WHITE_E, HALLED_STATE_OFF_E)`
      - 测试用例：设置4G LED为关闭状态
      - 预期结果：LED状态设置为`HALLED_STATE_OFF_E`，GPIO输出高电平（非激活）
      - 测试结果：

      2.2.2 分支2：设置LED开启状态

      - 函数调用：`HalLed_eSetLedState(HALLED_LED_4G_WHITE_E, HALLED_STATE_ON_E)`
      - 测试用例：设置4G LED为开启状态
      - 预期结果：LED状态设置为`HALLED_STATE_ON_E`，GPIO输出低电平（激活）
      - 测试结果：

      2.2.3 分支3：设置LED闪烁状态

      - 函数调用：`HalLed_eSetLedState(HALLED_LED_4G_WHITE_E, HALLED_STATE_BLINK_E)`
      - 测试用例：设置4G LED为闪烁状态
      - 预期结果：LED状态设置为`HALLED_STATE_BLINK_E`，记录当前时间戳，LED立即开启并开始闪烁
      - 测试结果：

      2.2.4 分支4：所有LED类型测试

      - 函数调用：分别对所有LED索引调用`HalLed_eSetLedState()`
      - 测试用例和预期结果：
        - HALLED_LED_4G_WHITE_E，预期结果：4G LED状态正确设置
        - HALLED_LED_BLUETOOTH_WHITE_E，预期结果：蓝牙LED状态正确设置
        - HALLED_LED_WIFI_WHITE_E，预期结果：WiFi LED状态正确设置
        - HALLED_LED_STATUS_GREEN_E，预期结果：状态LED状态正确设置
        - HALLED_LED_WARNING_RED_E，预期结果：警告红LED状态正确设置
        - HALLED_LED_WARNING_YELLOW_E，预期结果：警告黄LED状态正确设置
      - 测试结果：

   2.3 HalLed_eUpdateBlinkLeds()

      2.3.1 分支1：无闪烁LED时的处理

      - 函数调用：`HalLed_eUpdateBlinkLeds()`
      - 测试用例：所有LED都不处于闪烁状态时调用更新函数
      - 预期结果：函数正常执行，不进行任何GPIO操作
      - 测试结果：

      2.3.2 分支2：单个LED闪烁更新（未到切换时间）

      - 函数调用：`HalLed_eUpdateBlinkLeds()`
      - 测试用例：设置一个LED为闪烁状态，在500ms间隔内调用更新函数
      - 预期结果：LED状态不变，不进行GPIO切换
      - 测试结果：

      2.3.3 分支3：单个LED闪烁更新（到达切换时间）

      - 函数调用：`HalLed_eUpdateBlinkLeds()`
      - 测试用例：设置一个LED为闪烁状态，在500ms间隔后调用更新函数
      - 预期结果：LED状态切换，GPIO电平反转，时间戳更新
      - 测试结果：

      2.3.4 分支4：多个LED同时闪烁

      - 函数调用：`HalLed_eUpdateBlinkLeds()`
      - 测试用例：设置多个LED为闪烁状态，验证独立闪烁控制
      - 预期结果：每个LED独立进行闪烁，互不影响
      - 测试结果：

      2.3.5 分支5：时间溢出处理

      - 函数调用：`HalLed_eUpdateBlinkLeds()`
      - 测试用例：模拟32位时间计数器溢出情况（通过修改系统时钟模拟）
      - 预期结果：时间计算仍然正确，闪烁功能不受影响
      - 测试结果：

      2.3.6 分支6：闪烁周期验证

      - 函数调用：`HalLed_eUpdateBlinkLeds()`
      - 测试用例：使用示波器测量LED闪烁周期
      - 预期结果：闪烁周期为1000ms（500ms亮，500ms灭）
      - 测试结果：


测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

1. 可配置闪烁周期：目前闪烁周期固定为1000ms，未来可改进为可配置的闪烁周期，支持不同的闪烁频率。
2. PWM调光功能：目前仅支持开关控制，未来可增加PWM调光功能，实现LED亮度调节。

### 可重用性声明

1. 该模块依赖于STM32 HAL库和GlobalTypes类型定义。
2. 该模块依赖于FreeRTOS提供的时钟源功能。
3. 该模块的GPIO配置依赖于main.h中的宏定义。
