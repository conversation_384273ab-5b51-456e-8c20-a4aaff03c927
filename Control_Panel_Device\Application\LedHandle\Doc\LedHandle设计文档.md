- [LedHandle](#LedHandle)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# LedHandle

## 描述

`LedHandle`作为LED指示器控制的应用层模块。该模块主要负责根据系统事件控制各种LED指示器的状态，包括4G通信、蓝牙通信、WiFi通信、设备状态和故障警告的LED指示，为用户提供直观的设备状态反馈。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0071    |   4G指示灯   |
| MNT0072 | 蓝牙指示灯 |
| MNT0073 | 运行指示灯 |
| MNT0074 | 故障警告指示灯 |

### 软件需求

1) 应能够正确显示4G、蓝牙、WiFi的状态，可用时亮，不可用时灭，通信异常时闪烁
2) 应能够正确显示故障与警告的状态，有重故障时常亮红色，有轻故障时闪烁红色，有警告时闪烁黄色，无故障与警告时熄灭
3) 应能够正确显示运行与待机的状态，运行时闪烁，待机时熄灭

### 假设

1) HalLed模块已正确初始化并提供LED硬件控制功能。
2) Event模块已正确初始化并提供事件发布订阅功能。

***

## 平台资源

接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HalLed_eInit()` | 初始化LED硬件抽象层。 |
| `HalLed_eSetLedState()` | 设置LED状态（开/关/闪烁）。 |
| `HalLed_eUpdateBlinkLeds()` | 更新闪烁LED的状态。 |
| `Event_eSubscribe()` | 订阅特定类型的事件。 |
| `Event_eProcessSubscriberEvents()` | 处理订阅者的事件队列。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用事件驱动的LED控制方案。该方案通过订阅系统中各种通信和设备状态事件，在事件触发时自动更新相应的LED指示器状态。具体设计思路如下：

1. 在初始化阶段订阅所有相关的系统事件。
2. 为每种事件类型注册对应的回调函数。
3. 在运行阶段周期性处理订阅的事件。
4. 事件触发时通过回调函数直接控制对应的LED状态。
5. 依赖HalLed模块处理LED硬件的闪烁逻辑。

通过这种方式，实现了事件与LED状态的解耦，提高了系统的可维护性和扩展性。

### 静态设计

模块的静态设计如下所示：

![类图](Image/LedHandle_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/LedHandle_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：
   
   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `LedHandle_vInit()` | 1       |
   | `LedHandle_vRun()` | 1 |
   | `LedHandle_v4GEnabledCallback()` | 1 |
   | `LedHandle_v4GDisabledCallback()` | 1 |
   | `LedHandle_v4GErrorCallback()` | 1 |
   | `LedHandle_vBluetoothEnabledCallback()` | 1 |
   | `LedHandle_vBluetoothDisabledCallback()` | 1 |
   | `LedHandle_vBluetoothErrorCallback()` | 1 |
   | `LedHandle_vWifiEnabledCallback()` | 1 |
   | `LedHandle_vWifiDisabledCallback()` | 1 |
   | `LedHandle_vWifiErrorCallback()` | 1 |
   | `LedHandle_vDeviceStandbyCallback()` | 1 |
   | `LedHandle_vDeviceRunningCallback()` | 1 |
   | `LedHandle_vDeviceShutdownCallback()` | 1 |
   | `LedHandle_vDeviceCriticalFaultCallback()` | 1 |
   | `LedHandle_vDeviceMinorFaultCallback()` | 1 |
   | `LedHandle_vDeviceWarningCallback()` | 1 |
   | `LedHandle_vDeviceNoFaultWarningCallback()` | 1 |
   
2. 其他测试项：目前无免费工具，暂不列出。  

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用FreeRTOS新建StartLedHandleTask任务，在其中实现LedHandle模块的LED控制测试方案。任务调度时间为50ms。

   1.4 集成Event模块，确保事件发布订阅机制正常。

2. 函数测试详细结果

   2.1 LedHandle_vInit()

      2.1.1 分支1：正常初始化

      - 测试用例：调用 `LedHandle_vInit()`
      - 预期结果：HalLed模块正确初始化，所有事件订阅成功
      - 测试结果：通过，初始化完成，16个事件订阅成功

   2.2 LedHandle_vRun()

      2.2.1 分支1：正常运行

      - 测试用例：周期性调用 `LedHandle_vRun()`
      - 预期结果：事件正确处理，LED闪烁更新正常
      - 测试结果：通过，模块运行正常
   
3. 集成测试详细结果

   3.1 LedHandle模块功能测试

      3.1.1 测试目标

      验证LedHandle模块是否能正常相应Event模块发布的事件。

      3.1.2 测试环境

      - 测试任务：StartKeyHandleTask (KeyHandle模块) 和 StartLedHandleTask (LedHandle模块)
      - 测试程序如下：
   
      ```c
      void StartLedHandleTask(void const * argument)
      {
        /* USER CODE BEGIN StartLedHandleTask */
        LedHandle_vInit();
        /* Infinite loop */
        for(;;)
        {
      	  LedHandle_vRun();
          osDelay(1);
        }
        /* USER CODE END StartLedHandleTask */
      }
      
      void KeyHandle_vTriggerSingleKeyLongPressEvent(const KEYHANDLE_SINGLE_KEY_E eSingleKey)
      {
          EVENT_EVENT_TYPE_E eEventType = EVENT_TYPE_NONE_E;
          
          /* Map single key to corresponding long press event type */
          switch (eSingleKey)
          {
              // case KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E: eEventType = EVENT_TYPE_KEY_LEFT_FUNC_LONG_PRESS_E; break;
              // case KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E: eEventType = EVENT_TYPE_KEY_RIGHT_FUNC_LONG_PRESS_E; break;
              // case KEYHANDLE_SINGLE_KEY_UP_E: eEventType = EVENT_TYPE_KEY_UP_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E: eEventType = EVENT_TYPE_4G_COMM_ENABLED_E; break;
              case KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E: eEventType = EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E; break;
              case KEYHANDLE_SINGLE_KEY_UP_E: eEventType = EVENT_TYPE_DEVICE_CRITICAL_FAULT_E; break;
      
              case KEYHANDLE_SINGLE_KEY_DOWN_E: eEventType = EVENT_TYPE_KEY_DOWN_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_LEFT_E: eEventType = EVENT_TYPE_KEY_LEFT_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_RIGHT_E: eEventType = EVENT_TYPE_KEY_RIGHT_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_START_E: eEventType = EVENT_TYPE_KEY_START_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_STOP_E: eEventType = EVENT_TYPE_KEY_STOP_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_HELP_E: eEventType = EVENT_TYPE_KEY_HELP_LONG_PRESS_E; break;
              case KEYHANDLE_SINGLE_KEY_CONTROL_SWITCH_E: eEventType = EVENT_TYPE_KEY_CONTROL_SWITCH_LONG_PRESS_E; break;
              default: return; /* Invalid key type */
          }
          
          /* Publish event without additional data */
          Event_ePublish(eEventType, NULL_D, 0U);
      }
      ```
   
      3.1.3 测试实现
   
      测试程序基于两个独立的FreeRTOS任务实现：
   
      使用KeyHandle任务（事件发布者）扫描按键输入，并使用特定按键长按触发模拟以下事件发布到Event模块中
   - 左功能键长按 → EVENT_TYPE_4G_COMM_ENABLED_E
   
   - 右功能键长按 → EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E
   
   - 上方向键长按 → EVENT_TYPE_DEVICE_CRITICAL_FAULT_E
   
      使用LedHandle订阅LED相关事件，根据接收到的事件控制LED状态。
   
      测试用例1：4G通信状态LED控制
      - 操作：长按左功能键 (1秒以上)
   
      - 预期结果：KeyHandle任务发布EVENT_TYPE_4G_COMM_ENABLED_E事件，LedHandle任务接收事件并点亮4G白色LED
   
      - 测试结果：通过，LED正确响应按键操作。断点调试如下：
   
        ![测试图](Image/4G通信状态LED控制.png)
   
      测试用例2：蓝牙通信状态LED控制 
      - 操作：长按右功能键 (1秒以上)
   
      - 预期结果：KeyHandle任务发布EVENT_TYPE_BLUETOOTH_COMM_ENABLED_E事件，LedHandle任务接收事件并点亮蓝牙白色LED
   
      - 测试结果：通过，LED正确响应按键操作。断点调试如下：
   
        ![测试图](Image/蓝牙通信状态LED控制.png)
   
      测试用例3：设备故障状态LED控制
      - 操作：长按上方向键 (1秒以上)  
   
      - 预期结果：KeyHandle任务发布EVENT_TYPE_DEVICE_CRITICAL_FAULT_E事件，LedHandle任务接收事件并点亮红色警告LED，关闭黄色警告LED
   
      - 测试结果：通过，LED状态正确切换。断点调试如下：
   
        ![测试图](Image/设备故障状态LED控制.png)


测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

无

### 可重用性声明

1. 该模块依赖于HalLed模块提供的LED硬件抽象层接口。
2. 该模块依赖于Event模块提供的发布订阅事件管理功能。
