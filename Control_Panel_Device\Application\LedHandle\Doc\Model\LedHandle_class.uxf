<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>8</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>368</x>
      <y>248</y>
      <w>712</w>
      <h>272</h>
    </coordinates>
    <panel_attributes>LedHandle
--
+LedHandle_eInit(void):void
+LedHandle_vRun(void):void
-LedHandle_v4GEnabledCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_v4GDisabledCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_v4GErrorCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vBluetoothEnabledCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vBluetoothDisabledCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vBluetoothErrorCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vWifiEnabledCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vWifiDisabledCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vWifiErrorCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vDeviceStandbyCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vDeviceRunningCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vDeviceShutdownCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vDeviceCriticalFaultCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vDeviceMinorFaultCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vDeviceWarningCallback(psEventData:const EVENT_EVENT_DATA_T* const):void
-LedHandle_vDeviceNoFaultWarningCallback(psEventData:const EVENT_EVENT_DATA_T* const):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>360</x>
      <y>136</y>
      <w>744</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>Event
--
+Event_eSubscribe(eEventType:EVENT_TYPE_E, pfCallback:EVENT_CALLBACK_T, eSubscriber:EVENT_SUBSCRIBER_E):EVENT_STATUS_E
+Event_eProcessSubscriberEvents(eSubscriber:EVENT_SUBSCRIBER_E):EVENT_STATUS_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>712</x>
      <y>192</y>
      <w>56</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>520</x>
      <y>568</y>
      <w>416</w>
      <h>80</h>
    </coordinates>
    <panel_attributes>HalLed
--
+HalLed_eInit(void):void
+HalLed_eUpdateBlinkLeds(void):void
+HalLed_eSetLedState(eLed:HALLED_LED_E, eState:HALLED_STATE_E):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>712</x>
      <y>512</y>
      <w>56</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
</diagram>
