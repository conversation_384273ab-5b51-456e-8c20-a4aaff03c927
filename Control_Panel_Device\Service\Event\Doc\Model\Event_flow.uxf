<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>4</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>276</x>
      <y>0</y>
      <w>316</w>
      <h>504</h>
    </coordinates>
    <panel_attributes>Event Subscribe
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>404</x>
      <y>24</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>364</x>
      <y>44</y>
      <w>88</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>28</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>80</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>64</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>80</y>
      <w>120</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>280.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>112</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Get Subscriber
By subscriber ID</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>92</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>372</x>
      <y>144</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Subscriber Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>128</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>176</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>160</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>488</x>
      <y>428</y>
      <w>76</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>176</y>
      <w>120</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>280.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>364</x>
      <y>208</y>
      <w>88</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Search Subscription List
for Event Type</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>188</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>244</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>228</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>304</x>
      <y>280</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Update Callback</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>328</x>
      <y>244</y>
      <w>80</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Found existing]</panel_attributes>
    <additional_attributes>180.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>280</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Allocate New Node</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>256</y>
      <w>40</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not found]</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>312</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>296</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>312</y>
      <w>84</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>lt=-
[Allocation failed]</panel_attributes>
    <additional_attributes>190.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>364</x>
      <y>344</y>
      <w>88</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Initialize Node
Add to List Head</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>324</y>
      <w>60</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Allocation success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>404</x>
      <y>480</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>328</x>
      <y>248</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>360</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>520</x>
      <y>84</y>
      <w>12</w>
      <h>352</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;860.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>484</x>
      <y>316</y>
      <w>12</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>616</x>
      <y>0</y>
      <w>292</w>
      <h>432</h>
    </coordinates>
    <panel_attributes>Event Unsubscribe
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>684</x>
      <y>24</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>648</x>
      <y>44</y>
      <w>80</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>28</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>680</x>
      <y>80</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>64</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>796</x>
      <y>348</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>692</x>
      <y>80</y>
      <w>152</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>360.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>660</x>
      <y>112</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Get Subscriber</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>92</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>680</x>
      <y>180</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>164</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>692</x>
      <y>180</y>
      <w>152</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>360.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>648</x>
      <y>212</y>
      <w>80</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Search Subscription List</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>192</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>236</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>648</x>
      <y>292</y>
      <w>80</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Remove from List
Free Node, Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>656</x>
      <y>348</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>316</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>684</x>
      <y>400</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>364</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>8</x>
      <y>536</y>
      <w>248</w>
      <h>368</h>
    </coordinates>
    <panel_attributes>Event UnsubscribeAll
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>84</x>
      <y>560</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>48</x>
      <y>580</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>564</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>596</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>152</x>
      <y>824</y>
      <w>76</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>40</x>
      <y>744</y>
      <w>100</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Traverse Subscription List
(while loop:free current, move next)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>60</x>
      <y>784</y>
      <w>56</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>764</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>52</x>
      <y>824</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>804</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>84</x>
      <y>876</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>840</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>320</x>
      <y>536</y>
      <w>248</w>
      <h>392</h>
    </coordinates>
    <panel_attributes>Event Publish
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>412</x>
      <y>560</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>576</y>
      <w>72</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>564</y>
      <w>12</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;30.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>408</x>
      <y>612</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>596</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>476</x>
      <y>852</y>
      <w>72</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>420</x>
      <y>612</y>
      <w>100</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>230.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>368</x>
      <y>644</y>
      <w>100</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Create Event Data
Copy data if provided,else clear buffer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>624</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>388</x>
      <y>688</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Loop All Subscribers</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>668</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>372</x>
      <y>724</y>
      <w>84</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check subscription subscribed</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>704</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>408</x>
      <y>756</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>740</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>376</x>
      <y>788</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Enqueue event to subscriber</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>768</y>
      <w>44</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Subscribed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>804</y>
      <w>12</w>
      <h>56</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;120.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>412</x>
      <y>900</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>760</y>
      <w>120</w>
      <h>68</h>
    </coordinates>
    <panel_attributes>lt=-
[Not subscribed]</panel_attributes>
    <additional_attributes>10.0;150.0;180.0;150.0;180.0;10.0;30.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>508</x>
      <y>616</y>
      <w>12</w>
      <h>244</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;590.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>636</x>
      <y>532</y>
      <w>256</w>
      <h>512</h>
    </coordinates>
    <panel_attributes>Event ProcessSubscriberEvents
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>728</x>
      <y>556</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>696</x>
      <y>576</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>560</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>724</x>
      <y>608</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>592</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>608</y>
      <w>100</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid]</panel_attributes>
    <additional_attributes>230.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>620</y>
      <w>28</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>704</x>
      <y>740</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Process event queue</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>696</x>
      <y>840</y>
      <w>72</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Search subscription list
for matching callback</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>756</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>860</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>704</x>
      <y>880</y>
      <w>56</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Unlock Mutex
Call Callback
Re-lock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>728</x>
      <y>1020</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>40</x>
      <y>960</y>
      <w>196</w>
      <h>236</h>
    </coordinates>
    <panel_attributes>Event LockSubscriberMutex
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>104</x>
      <y>984</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>72</x>
      <y>1004</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>104</x>
      <y>988</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>100</x>
      <y>1036</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>104</x>
      <y>1020</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>112</x>
      <y>1036</y>
      <w>88</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>200.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>80</x>
      <y>1068</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Take Semaphore
With timeout</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>104</x>
      <y>1048</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>100</x>
      <y>1100</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>104</x>
      <y>1084</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>156</x>
      <y>1132</y>
      <w>72</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>112</x>
      <y>1100</y>
      <w>88</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>lt=-
[Take failed]</panel_attributes>
    <additional_attributes>200.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>76</x>
      <y>1132</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>104</x>
      <y>1112</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Take success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>104</x>
      <y>1180</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>344</x>
      <y>960</y>
      <w>200</w>
      <h>236</h>
    </coordinates>
    <panel_attributes>Event UnlockSubscriberMutex
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>0</x>
      <y>1236</y>
      <w>288</w>
      <h>460</h>
    </coordinates>
    <panel_attributes>Event EnqueueEventToSubscriber
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>80</x>
      <y>1256</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>48</x>
      <y>1276</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1260</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>76</x>
      <y>1308</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1292</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>88</x>
      <y>1308</y>
      <w>156</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>370.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>48</x>
      <y>1340</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Subscriber Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1320</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>56</x>
      <y>1404</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check Queue Full
Event count limit</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>56</x>
      <y>1468</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Allocate Event Node</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>56</x>
      <y>1536</y>
      <w>56</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Add to Queue Tail
Increment count</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1556</y>
      <w>12</w>
      <h>12</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>80</x>
      <y>1672</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1640</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>160</x>
      <y>1440</y>
      <w>12</w>
      <h>144</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;340.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>344</x>
      <y>1236</y>
      <w>216</w>
      <h>272</h>
    </coordinates>
    <panel_attributes>Event DequeueEventFromSubscriber
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>408</x>
      <y>1256</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>376</x>
      <y>1276</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1260</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>404</x>
      <y>1308</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1292</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1308</y>
      <w>104</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid/Empty queue]</panel_attributes>
    <additional_attributes>240.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>384</x>
      <y>1340</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Get Node from Head
FIFO operation</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1320</y>
      <w>56</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Queue not empty]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>384</x>
      <y>1372</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Copy Event Data
Update head pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1356</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>384</x>
      <y>1404</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Decrease Count
Free Event Node</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1388</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>376</x>
      <y>1436</y>
      <w>72</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1420</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>408</x>
      <y>1480</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1448</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>508</x>
      <y>1312</y>
      <w>12</w>
      <h>132</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;310.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>592</x>
      <y>1236</y>
      <w>152</w>
      <h>276</h>
    </coordinates>
    <panel_attributes>Event AllocateSubscriptionNode
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>652</x>
      <y>1260</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>616</x>
      <y>1280</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1264</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>648</x>
      <y>1312</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1296</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>660</x>
      <y>1312</y>
      <w>68</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>620</x>
      <y>1344</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Search Pool
(loop:from current index)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1324</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>628</x>
      <y>1376</y>
      <w>56</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Mark Node Used
Update pool index
Set return pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1360</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1400</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>652</x>
      <y>1492</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1436</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1316</y>
      <w>76</w>
      <h>140</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;330.0;170.0;330.0;170.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>48</x>
      <y>1732</y>
      <w>216</w>
      <h>324</h>
    </coordinates>
    <panel_attributes>Event_vFreeSubscriptionNode
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>120</x>
      <y>1756</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>88</x>
      <y>1776</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1760</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>92</x>
      <y>1904</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Validate pointer range
Calculate index</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>96</x>
      <y>1936</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Mark Node Free</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1920</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>120</x>
      <y>2016</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1952</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>24</x>
      <y>0</y>
      <w>212</w>
      <h>316</h>
    </coordinates>
    <panel_attributes>Event Init
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>84</x>
      <y>24</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>48</x>
      <y>44</y>
      <w>80</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Initialize Event Node Pool</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>28</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>48</x>
      <y>80</y>
      <w>80</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Initialize Subscription Pool</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>64</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>48</x>
      <y>116</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Create Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>100</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>80</x>
      <y>148</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>132</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>92</x>
      <y>148</y>
      <w>100</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Mutex create failed]</panel_attributes>
    <additional_attributes>230.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>52</x>
      <y>180</y>
      <w>72</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>Initialize Subscribers Array</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>160</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Mutex created]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>80</x>
      <y>216</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>200</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>144</x>
      <y>248</y>
      <w>76</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>92</x>
      <y>216</y>
      <w>100</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Any mutex create failed]</panel_attributes>
    <additional_attributes>230.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>56</x>
      <y>248</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>228</y>
      <w>40</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[All created]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>84</x>
      <y>296</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>260</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>180</x>
      <y>152</y>
      <w>12</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;240.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>260</y>
      <w>108</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;250.0;50.0;250.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>392</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>376</x>
      <y>428</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>408</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>328</x>
      <y>296</y>
      <w>88</w>
      <h>88</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>200.0;200.0;10.0;200.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>460</x>
      <y>344</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>484</x>
      <y>360</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>100.0;50.0;10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>444</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>444</y>
      <w>128</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;300.0;50.0;300.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>784</x>
      <y>256</y>
      <w>12</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;100.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>652</x>
      <y>148</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Subscriber Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>128</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>264</y>
      <w>32</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Found]</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>680</x>
      <y>252</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>692</x>
      <y>252</y>
      <w>104</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Not found]</panel_attributes>
    <additional_attributes>240.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>760</x>
      <y>296</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>784</x>
      <y>312</y>
      <w>60</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>130.0;50.0;10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>832</x>
      <y>84</y>
      <w>12</w>
      <h>272</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;660.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>684</x>
      <y>364</y>
      <w>160</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;380.0;50.0;380.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>92</x>
      <y>612</y>
      <w>104</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>240.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>80</x>
      <y>612</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>624</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>60</x>
      <y>644</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Get Subscriber</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>660</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>52</x>
      <y>680</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Subscriber Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>696</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>92</x>
      <y>712</y>
      <w>104</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>240.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>80</x>
      <y>712</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>724</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>184</x>
      <y>616</y>
      <w>12</w>
      <h>216</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;520.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>84</x>
      <y>840</y>
      <w>112</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;260.0;50.0;260.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>344</x>
      <y>692</y>
      <w>80</w>
      <h>148</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Next subscriber]</panel_attributes>
    <additional_attributes>110.0;10.0;10.0;10.0;10.0;350.0;180.0;350.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>852</y>
      <w>72</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>864</y>
      <w>108</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;250.0;50.0;250.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>864</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>704</x>
      <y>640</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Get Subscriber</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>696</x>
      <y>676</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Subscriber Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>656</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>704</x>
      <y>776</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Dequeue Event</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>792</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>724</x>
      <y>808</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>820</y>
      <w>64</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Dequeue successed]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>660</x>
      <y>812</y>
      <w>80</w>
      <h>116</h>
    </coordinates>
    <panel_attributes>lt=-
[Dequeue failed]</panel_attributes>
    <additional_attributes>180.0;270.0;10.0;270.0;10.0;10.0;160.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>824</x>
      <y>612</y>
      <w>12</w>
      <h>364</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;890.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>724</x>
      <y>708</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>736</x>
      <y>708</y>
      <w>100</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>230.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>692</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>720</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>704</x>
      <y>932</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>948</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>700</x>
      <y>968</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>792</x>
      <y>968</y>
      <w>76</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>984</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>984</y>
      <w>108</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;250.0;50.0;250.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>728</x>
      <y>904</y>
      <w>12</w>
      <h>36</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;70.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>104</x>
      <y>1144</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>104</x>
      <y>1144</y>
      <w>96</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;220.0;50.0;220.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>188</x>
      <y>1040</y>
      <w>12</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;230.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>404</x>
      <y>976</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>372</x>
      <y>996</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>980</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>1028</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>1012</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>1028</y>
      <w>88</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>200.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>1060</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Give Semaphore
With timeout</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>1040</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>1092</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>1076</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>456</x>
      <y>1124</y>
      <w>72</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>412</x>
      <y>1092</y>
      <w>88</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>lt=-
[Give failed]</panel_attributes>
    <additional_attributes>200.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>376</x>
      <y>1124</y>
      <w>64</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>1104</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Give success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>404</x>
      <y>1172</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>1136</y>
      <w>12</w>
      <h>44</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;90.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>404</x>
      <y>1136</y>
      <w>96</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;50.0;220.0;50.0;220.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>488</x>
      <y>1032</y>
      <w>12</w>
      <h>100</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;230.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1356</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>76</x>
      <y>1372</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>88</x>
      <y>1372</y>
      <w>156</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>370.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1384</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1420</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>76</x>
      <y>1436</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>88</x>
      <y>1436</y>
      <w>84</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Reach limit]</panel_attributes>
    <additional_attributes>190.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1448</y>
      <w>52</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Not reach limit]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>76</x>
      <y>1504</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1484</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>88</x>
      <y>1504</y>
      <w>84</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>lt=-
[Allocate failed]</panel_attributes>
    <additional_attributes>190.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1516</y>
      <w>56</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Allocate success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>56</x>
      <y>1576</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>52</x>
      <y>1624</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_OK_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1592</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1556</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>200</x>
      <y>1624</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>80</x>
      <y>1640</y>
      <w>164</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;390.0;40.0;390.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>136</x>
      <y>1576</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>160</x>
      <y>1592</y>
      <w>84</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>190.0;40.0;10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>232</x>
      <y>1312</y>
      <w>12</w>
      <h>320</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;780.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>476</x>
      <y>1436</y>
      <w>72</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>Return EVENT_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>408</x>
      <y>1448</y>
      <w>112</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;40.0;260.0;40.0;260.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>616</x>
      <y>1420</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>616</x>
      <y>1460</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return node pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>652</x>
      <y>1476</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1792</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>116</x>
      <y>1808</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>128</x>
      <y>1808</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1820</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>84</x>
      <y>1840</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1856</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>128</x>
      <y>1872</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>116</x>
      <y>1872</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1884</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>84</x>
      <y>1968</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1984</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>120</x>
      <y>1812</y>
      <w>100</w>
      <h>196</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;470.0;230.0;470.0;230.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>776</x>
      <y>1236</y>
      <w>160</w>
      <h>292</h>
    </coordinates>
    <panel_attributes>Event AllocateEventNode
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>840</x>
      <y>1260</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>804</x>
      <y>1280</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1264</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>836</x>
      <y>1312</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1296</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>848</x>
      <y>1312</y>
      <w>68</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>150.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>808</x>
      <y>1344</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Search Pool
(loop:from current index)</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1324</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>816</x>
      <y>1376</y>
      <w>56</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>Mark Node Used
Update pool index
Set return pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1360</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1400</y>
      <w>12</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>840</x>
      <y>1492</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1436</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1316</y>
      <w>76</w>
      <h>140</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;330.0;170.0;330.0;170.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>332</x>
      <y>1732</y>
      <w>228</w>
      <h>324</h>
    </coordinates>
    <panel_attributes>Event FreeEventNode
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>416</x>
      <y>1756</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>384</x>
      <y>1776</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Check validate parameters</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1760</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>388</x>
      <y>1904</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Validate pointer range
Calculate index</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>392</x>
      <y>1936</y>
      <w>56</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Mark Node Free</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1920</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>416</x>
      <y>2016</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1952</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>804</x>
      <y>1420</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>804</x>
      <y>1460</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Return node pointer</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>840</x>
      <y>1476</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1792</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>412</x>
      <y>1808</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>424</x>
      <y>1808</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Invalid params]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1820</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Valid params]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>1840</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Lock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1856</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>424</x>
      <y>1872</y>
      <w>92</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>[Lock failed]</panel_attributes>
    <additional_attributes>210.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>412</x>
      <y>1872</y>
      <w>16</w>
      <h>16</h>
    </coordinates>
    <panel_attributes>type=decision</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1884</y>
      <w>48</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;-
[Lock success]</panel_attributes>
    <additional_attributes>10.0;50.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>380</x>
      <y>1968</y>
      <w>80</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Unlock Memory Pool Mutex</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1984</y>
      <w>12</w>
      <h>40</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>416</x>
      <y>1812</y>
      <w>100</w>
      <h>196</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;470.0;230.0;470.0;230.0;10.0</additional_attributes>
  </element>
</diagram>
