//----------------------------------------------------------------------------
/**
* @remark Confidential property of TRIED.
* @remark Copyright (c) 2025 TRIED.All Rights Reserved.
*
* <AUTHOR>
* @remark Requirement IDs
* @remark Reusability status
* @file HalFlash.c
*
* @brief Flash hardware abstraction layer implementation file
*
*/
//----------------------------------------------------------------------------

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "HalFlash.h"
#include "main.h"
#include "spi.h"
#include "cmsis_os.h"

//----------------------------------------------------------------------------
// Private Definitions:
//----------------------------------------------------------------------------
#define HALFLASH_CHIP_SIZE_BYTES_D          4194304U                            /* GD25Q32ETIG total size: 4MB (32Mbit) */
#define HALFLASH_MAX_ADDRESS_D              (HALFLASH_CHIP_SIZE_BYTES_D - 1U)   /* Maximum address */
#define HALFLASH_PAGE_SIZE_BYTES_D          256U                                /* Page size: 256 bytes */
#define HALFLASH_SPI_TIMEOUT_MS_D           1000U                               /* Default operation timeout */
#define HALFLASH_WIP_POLL_TIMEOUT_MS_D      5000U                               /* Maximum timeout for WIP polling */
#define HALFLASH_WEL_POLL_TIMEOUT_MS_D      50U                                 /* Timeout for WEL polling after WREN */
#define HALFLASH_CMD_READ_DATA_D            0x03U                               /* Read Data */
#define HALFLASH_CMD_PAGE_PROGRAM_D         0x02U                               /* Page Program */
#define HALFLASH_CMD_WRITE_ENABLE_D         0x06U                               /* Write Enable */
#define HALFLASH_CMD_READ_STATUS_REGISTER_D 0x05U                               /* Read Status Register */
#define HALFLASH_STATUS_WIP_BIT_D           0x01U                               /* Write In Progress bit (S0) */
#define HALFLASH_STATUS_WEL_BIT_D           0x02U                               /* Write Enable Latch bit (S1) */
#define HALFLASH_SPI_HANDLE_D               (&hspi1)                            /* SPI handle variable name */
#define HALFLASH_CS_GPIO_PORT_D             (FLASH_CS_GPIO_Port)                /* CS GPIO port */
#define HALFLASH_CS_GPIO_PIN_D              (FLASH_CS_Pin)                      /* CS GPIO pin */
#define HALFLASH_CS_ACTIVE_LEVEL_D          FALSE_D                             /* CS active level (FALSE_D for active low) */

#define HALFLASH_CS_SELECT_D()              HAL_GPIO_WritePin(sHalFlashConfig.psCsGpioPort, sHalFlashConfig.u16CsGpioPin, (sHalFlashConfig.bCsActiveLevel) ? (GPIO_PIN_SET) : (GPIO_PIN_RESET))
#define HALFLASH_CS_DESELECT_D()            HAL_GPIO_WritePin(sHalFlashConfig.psCsGpioPort, sHalFlashConfig.u16CsGpioPin, (sHalFlashConfig.bCsActiveLevel) ? (GPIO_PIN_RESET) : (GPIO_PIN_SET))

/**
* @brief SPI configuration structure for Flash communication
*/  
typedef struct
{
    SPI_HandleTypeDef* psSpiHandle;     /* SPI handle pointer */
    GPIO_TypeDef* psCsGpioPort;         /* CS GPIO port */
    U16 u16CsGpioPin;                   /* CS GPIO pin */
    BOOL bCsActiveLevel;                /* CS active level (TRUE_D for active high, FALSE_D for active low) */
} HALFLASH_CONFIG_T;

//----------------------------------------------------------------------------
// Private Function Prototypes:
//----------------------------------------------------------------------------
static HALFLASH_STATUS_E HalFlash_eWriteEnable(void);
static HALFLASH_STATUS_E HalFlash_eIsAddressValid(const U32 u32Address, const U16 u16Length);
static HALFLASH_STATUS_E HalFlash_eReadStatusRegister(U8* const pu8Status);
static HALFLASH_STATUS_E HalFlash_eWaitForReady(void);

//----------------------------------------------------------------------------
// Private Data:
//----------------------------------------------------------------------------
static HALFLASH_CONFIG_T sHalFlashConfig;                /* Static Flash SPI configuration */

//----------------------------------------------------------------------------
// Public Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Initialize Flash hardware abstraction layer.
* @remark None.
*
* @return None.
*/
void HalFlash_eInit(void)
{
    /* Configure SPI and GPIO settings using macros */
    sHalFlashConfig.psSpiHandle = HALFLASH_SPI_HANDLE_D;
    sHalFlashConfig.psCsGpioPort = HALFLASH_CS_GPIO_PORT_D;
    sHalFlashConfig.u16CsGpioPin = HALFLASH_CS_GPIO_PIN_D;
    sHalFlashConfig.bCsActiveLevel = HALFLASH_CS_ACTIVE_LEVEL_D;
}

/**
* @brief Read data from Flash memory.
* @remark None.
*
* @param u32Address [in]: Start address to read from.
* @param pu8Buffer [out]: Buffer to store read data.
* @param u16Length [in]: Number of bytes to read.
* @return Operation status.
*/
HALFLASH_STATUS_E HalFlash_eReadData(const U32 u32Address, U8* const pu8Buffer, const U16 u16Length)
{
    /* Parameter validity check */
    if ((pu8Buffer == NULL_D) || (u16Length == 0U))
    {
        return HALFLASH_ERROR_E;
    }
    
    /* Check address range */
    if (HalFlash_eIsAddressValid(u32Address, u16Length) != HALFLASH_OK_E)
    {
        return HALFLASH_ERROR_E;
    }

    /* According to datasheet section 7.6: Read operations are rejected while Erase, Program or Write cycle is in progress */
    if (HalFlash_eWaitForReady() != HALFLASH_OK_E)
    {
        return HALFLASH_ERROR_E;
    }

    U8 au8Command[4U] = {0U, 0U, 0U, 0U};

    /* Prepare read command with address */
    au8Command[0U] = HALFLASH_CMD_READ_DATA_D;
    au8Command[1U] = (U8)((u32Address >> 16U) & 0xFFU);
    au8Command[2U] = (U8)((u32Address >> 8U) & 0xFFU);
    au8Command[3U] = (U8)(u32Address & 0xFFU);
    
    /* Select device */
    HALFLASH_CS_SELECT_D();
    
    /* Send read command and address */
    if (HAL_SPI_Transmit(sHalFlashConfig.psSpiHandle, (uint8_t*)au8Command, 4U, HALFLASH_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFLASH_CS_DESELECT_D();

        return HALFLASH_ERROR_E;
    }
    
    /* Read data */
    if (HAL_SPI_Receive(sHalFlashConfig.psSpiHandle, (uint8_t*)pu8Buffer, (uint16_t)u16Length, HALFLASH_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFLASH_CS_DESELECT_D();

        return HALFLASH_ERROR_E;
    }
    
    /* Deselect device */
    HALFLASH_CS_DESELECT_D();
    
    return HALFLASH_OK_E;
}

/**
* @brief Write data to Flash memory.
* @remark None.
*
* @param u32Address [in]: Start address to write to.
* @param pu8Buffer [in]: Data buffer to write.
* @param u16Length [in]: Number of bytes to write.
* @return Operation status.
*/
HALFLASH_STATUS_E HalFlash_eWriteData(const U32 u32Address, const U8* const pu8Buffer, const U16 u16Length)
{
    /* Parameter validity check */
    if ((pu8Buffer == NULL_D) || (u16Length == 0U))
    {
        return HALFLASH_ERROR_E;
    }
    
    /* Check address range */
    if (HalFlash_eIsAddressValid(u32Address, u16Length) != HALFLASH_OK_E)
    {
        return HALFLASH_ERROR_E;
    }

    /* Per datasheet: new program commands must not be issued while a program/erase/write is in progress */
    if (HalFlash_eWaitForReady() != HALFLASH_OK_E)
    {
        return HALFLASH_ERROR_E;
    }

    U16 u16CurrentIndex = 0U;
    U32 u32CurrentAddress = u32Address;
    U16 u16RemainingLength = u16Length;
    
    /* Write data page by page */
    while (u16RemainingLength > 0U)
    {
        U16 u16BytesToWrite = HALFLASH_PAGE_SIZE_BYTES_D - (u32CurrentAddress % HALFLASH_PAGE_SIZE_BYTES_D);

        /* Limit bytes to write to remaining bytes */
        if (u16BytesToWrite > u16RemainingLength)
        {
            u16BytesToWrite = u16RemainingLength;
        }
        
        /* Enable write */
        if (HalFlash_eWriteEnable() != HALFLASH_OK_E)
        {
            return HALFLASH_ERROR_E;
        }

        U8 au8Command[4U] = {0U, 0U, 0U, 0U};
        
        /* Prepare write command */
        au8Command[0U] = HALFLASH_CMD_PAGE_PROGRAM_D;
        au8Command[1U] = (U8)((u32CurrentAddress >> 16U) & 0xFFU);
        au8Command[2U] = (U8)((u32CurrentAddress >> 8U) & 0xFFU);
        au8Command[3U] = (U8)(u32CurrentAddress & 0xFFU);
        
        /* Select device */
        HALFLASH_CS_SELECT_D();
        
        /* Send write command and address */
        if (HAL_SPI_Transmit(sHalFlashConfig.psSpiHandle, (uint8_t*)au8Command, 4U, HALFLASH_SPI_TIMEOUT_MS_D) != HAL_OK)
        {
            HALFLASH_CS_DESELECT_D();

            return HALFLASH_ERROR_E;
        }
        
        /* Send data */
        if (HAL_SPI_Transmit(sHalFlashConfig.psSpiHandle, (uint8_t*)&pu8Buffer[u16CurrentIndex], (uint16_t)u16BytesToWrite, HALFLASH_SPI_TIMEOUT_MS_D) != HAL_OK)
        {
            HALFLASH_CS_DESELECT_D();

            return HALFLASH_ERROR_E;
        }
        
        /* Deselect device */
        HALFLASH_CS_DESELECT_D();
        
        /* According to datasheet section 7.13: Wait for Page Program cycle to complete by checking WIP bit. WIP bit is 1 during self-timed Page Program cycle */
        if (HalFlash_eWaitForReady() != HALFLASH_OK_E)
        {
            return HALFLASH_ERROR_E;
        }
        
        /* Update counters */
        u32CurrentAddress += u16BytesToWrite;
        u16CurrentIndex += u16BytesToWrite;
        u16RemainingLength -= u16BytesToWrite;
    }
    
    return HALFLASH_OK_E;
}

//----------------------------------------------------------------------------
// Private Function Implementation:
//----------------------------------------------------------------------------

/**
* @brief Enable Flash write operations.
* @remark None.
*
* @return Operation status.
*/
static HALFLASH_STATUS_E HalFlash_eWriteEnable(void)
{
    U8 u8Command = HALFLASH_CMD_WRITE_ENABLE_D;
    
    /* Select device */
    HALFLASH_CS_SELECT_D();
    
    /* Send write enable command */
    if (HAL_SPI_Transmit(sHalFlashConfig.psSpiHandle, (uint8_t*)&u8Command, 1U, HALFLASH_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFLASH_CS_DESELECT_D();

        return HALFLASH_ERROR_E;
    }
    
    /* Deselect device */
    HALFLASH_CS_DESELECT_D();

    /* Verify WEL bit is set, per datasheet WREN sets WEL prior to PP/WRSR/Erase */
    U8 u8Status = 0U;
    U32 u32Timeout = HALFLASH_WEL_POLL_TIMEOUT_MS_D;
    
    while (u32Timeout > 0U)
    {
        if (HalFlash_eReadStatusRegister(&u8Status) != HALFLASH_OK_E)
        {
            return HALFLASH_ERROR_E;
        }

        if ((u8Status & HALFLASH_STATUS_WEL_BIT_D) != 0U)
        {
            return HALFLASH_OK_E;
        }

        osDelay(1U);
        
        u32Timeout--;
    }

    return HALFLASH_ERROR_E;
}

/**
* @brief Check if address and length parameters are valid.
* @remark None.
*
* @param u32Address [in]: Starting address.
* @param u16Length [in]: Data length.
* @return Validation status.
*/
static HALFLASH_STATUS_E HalFlash_eIsAddressValid(const U32 u32Address, const U16 u16Length)
{
    /* Basic range checks */
    if ((u32Address > HALFLASH_MAX_ADDRESS_D) || (u16Length == 0U))
    {
        return HALFLASH_ERROR_E;
    }

    /* Prevent overflow: (length - 1) <= (MAX - address) */
    if ((u16Length - 1U) > (HALFLASH_MAX_ADDRESS_D - u32Address))
    {
        return HALFLASH_ERROR_E;
    }

    return HALFLASH_OK_E;
}

/**
* @brief Read Flash status register.
* @remark According to datasheet section 7.3 Read Status Register (RDSR) (05H).
*
* @param pu8Status [out]: Buffer to store status register value.
* @return Operation status.
*/
static HALFLASH_STATUS_E HalFlash_eReadStatusRegister(U8* const pu8Status)
{
    /* Parameter validity check */
    if (pu8Status == NULL_D)
    {
        return HALFLASH_ERROR_E;
    }

    U8 u8Command = HALFLASH_CMD_READ_STATUS_REGISTER_D;
    
    /* Select device */
    HALFLASH_CS_SELECT_D();
    
    /* Send read status register command */
    if (HAL_SPI_Transmit(sHalFlashConfig.psSpiHandle, (uint8_t*)&u8Command, 1U, HALFLASH_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFLASH_CS_DESELECT_D();

        return HALFLASH_ERROR_E;
    }
    
    /* Read status register value */
    if (HAL_SPI_Receive(sHalFlashConfig.psSpiHandle, (uint8_t*)pu8Status, 1U, HALFLASH_SPI_TIMEOUT_MS_D) != HAL_OK)
    {
        HALFLASH_CS_DESELECT_D();

        return HALFLASH_ERROR_E;
    }
    
    /* Deselect device */
    HALFLASH_CS_DESELECT_D();
    
    return HALFLASH_OK_E;
}

/**
* @brief Wait for Flash to be ready (WIP bit = 0).
* @remark According to datasheet, WIP bit is 1 during self-timed operations.
*
* @return Operation status.
*/
static HALFLASH_STATUS_E HalFlash_eWaitForReady(void)
{
    U8 u8Status = 0U;
    U32 u32Timeout = HALFLASH_WIP_POLL_TIMEOUT_MS_D;
    
    /* Poll status register until WIP bit is cleared or timeout */
    while (u32Timeout > 0U)
    {
        if (HalFlash_eReadStatusRegister(&u8Status) != HALFLASH_OK_E)
        {
            return HALFLASH_ERROR_E;
        }
        
        /* Check if WIP bit is cleared */
        if ((u8Status & HALFLASH_STATUS_WIP_BIT_D) == 0U)
        {
            return HALFLASH_OK_E;
        }
        
        /* Add delay to avoid excessive polling */
        osDelay(1U);
        
        u32Timeout--;
    }
    
    /* Timeout occurred */
    return HALFLASH_ERROR_E;
}

//===========================================================================
// End of file.
//===========================================================================








