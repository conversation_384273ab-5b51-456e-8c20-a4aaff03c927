//----------------------------------------------------------------------------
/**
* @file HalFlash.h
* @remark HalFlash public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef HALFLASH_H_
#define HALFLASH_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "GlobalTypes.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------
/**
* @brief HAL layer Flash module status enumeration
*/
typedef enum
{
    HALFLASH_OK_E = 0U,     /* Operation successful */
    HALFLASH_ERROR_E        /* General error */
} HALFLASH_STATUS_E;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
void HalFlash_eInit(void);
HALFLASH_STATUS_E HalFlash_eReadData(const U32 u32Address, U8* const pu8Buffer, const U16 u16Length);
HALFLASH_STATUS_E HalFlash_eWriteData(const U32 u32Address, const U8* const pu8Buffer, const U16 u16Length);

#endif /* HALFLASH_H_ */

//===========================================================================
// End of file.
//===========================================================================








