//----------------------------------------------------------------------------
/**
* @file HalFm.h
* @remark HalFm public function declaration.
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------
#ifndef HALFM_H_
#define HALFM_H_

//----------------------------------------------------------------------------
// Includes:
//----------------------------------------------------------------------------
#include "GlobalTypes.h"

//----------------------------------------------------------------------------
// Public Definitions:
//----------------------------------------------------------------------------
/**
* @brief HAL layer FM module status enumeration
*/
typedef enum
{
    HALFM_OK_E = 0U,    /* Operation successful */
    HALFM_ERROR_E       /* General error */
} HALFM_STATUS_E;

//----------------------------------------------------------------------------
// Public Function Prototypes:
//----------------------------------------------------------------------------
void HalFm_eInit(void);
HALFM_STATUS_E HalFm_eReadData(const U16 u16Address, U8* const pu8Buffer, const U16 u16Length);
HALFM_STATUS_E HalFm_eWriteData(const U16 u16Address, const U8* const pu8Buffer, const U16 u16Length);

#endif /* HALFM_H_ */

//===========================================================================
// End of file.
//===========================================================================








