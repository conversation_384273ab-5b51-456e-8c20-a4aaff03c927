- [HalFm](#halfm)
  - [描述](#描述)
  - [需求](#需求)
    - [产品需求](#产品需求)
    - [软件需求](#软件需求)
    - [假设](#假设)
  - [平台资源](#平台资源)
    - [所需软件接口](#所需软件接口)
    - [提供软件接口](#提供软件接口)
    - [参数](#参数)
    - [配置](#配置)
    - [过程数据](#过程数据)
    - [命令](#命令)
    - [事件](#事件)
  - [设计](#设计)
    - [设计方案](#设计方案)
    - [静态设计](#静态设计)
    - [动态设计](#动态设计)
  - [测试](#测试)
    - [静态代码测试](#静态代码测试)
    - [动态代码测试](#动态代码测试)
  - [设计的局限性](#设计的局限性)
    - [已知Bugs](#已知bugs)
    - [未来的改进](#未来的改进)
    - [可重用性声明](#可重用性声明)

&nbsp;

***

# HalFm

## 描述

`HalFm`作为FRAM/EEPROM存储器的硬件抽象层。该模块主要负责GT25C128A存储芯片的硬件操作，包括SPI接口初始化与配置、数据读写操作的实现，为上层应用提供稳定可靠的非易失性存储功能。

***

## 需求

### 产品需求

| 产品需求ID       | 产品需求标题       |
|-----------------|-------------------|
|    MNT0036    |   系统功能菜单   |
| MNT0045 | 系统设置菜单 |
| MNT0052 | 用户登录与退出 |

### 软件需求

1) 应能够实现对片外铁电的读操作，包括单字节、多字节
2) 应能够实现对片外铁电的写操作，包括单字节、多字节
3) 应能够返回写操作的结果
4) 实现对GT25C128A芯片的读写数据功能的抽象

### 假设

1) SPI接口已在`main.h`中正确定义并完成时钟配置。
2) CS片选引脚已正确配置为推挽输出模式。
3) SPI接口配置为主模式，时钟极性和相位正确设置。
4) 芯片的写保护功能已正确配置。

***

## 平台资源

接口是组件定义变频器系统功能的提供和使用的"契约"接口，组件可以只使用接口，也可以只提供接口，或者两者兼有。

### 所需软件接口

| 接口名称 | 目的 |
|---------|------|
| `HAL_SPI_Transmit()` | 通过SPI接口发送数据到存储芯片。 |
| `HAL_SPI_Receive()` | 通过SPI接口从存储芯片接收数据。 |
| `HAL_GPIO_WritePin()` | 控制CS片选引脚的电平状态。 |

### 提供软件接口

见Class图

### 参数

无

### 配置

无

### 过程数据

无

### 命令

无

### 事件

无

***

## 设计

### 设计方案

本模块采用标准的SPI通信协议来控制GT25C128A存储芯片。具体操作流程如下：

1. **初始化阶段**：配置SPI接口参数和CS片选引脚。
2. **读操作**：
   - 拉低CS片选信号
   - 发送读命令（0x03）和16位地址
   - 接收指定长度的数据
   - 拉高CS片选信号
3. **写操作**：
   - 先发送写使能命令（0x06）
   - 拉低CS片选信号
   - 发送写命令（0x02）和16位地址
   - 发送数据（考虑分页边界）
   - 拉高CS片选信号

### 静态设计

模块的静态设计如下所示：

![类图](Image/HalFm_class.png)

### 动态设计

模块的动态设计如下所示：

![流程图](Image/HalFm_flow.png)

***

## 测试

### 静态代码测试

1. 循环复杂度：

   | 函数名                     | 循环复杂度 |
   | --------------------------| ---------- |
   | `HalFm_eInit()` | 1       |
   | `HalFm_eReadData()` | 5 |
   | `HalFm_eWriteData()` | 8 |
   | `HalFm_eWriteEnable()` | 2 |
   | `HalFm_eIsAddressValid()` | 2 |

2. 其他测试项：目前无免费工具，暂不列出。

测试覆盖率是100%(代码行数的百分比)。

### 动态代码测试

1. 测试环境搭建

   1.1 使用FreeRTOS新建StartFmTask任务，在其中实现HalFm模块的GT25C128A存储芯片读写测试方案。任务调度时间为100ms。

2. 函数测试详细结果

   2.1 HalFm_eInit()

      2.1.1 分支1：正常初始化

      - 测试用例：调用 `HalFm_eInit()`
      - 预期结果：配置结构体正确设置，SPI句柄、GPIO端口和引脚、CS电平配置正确
      - 测试结果：

   2.2 HalFm_eReadData()

      2.2.1 分支1：空指针检测

      - 测试用例：传入 `pu8Buffer = NULL_D`
      - 预期结果：返回 `HALFM_ERROR_E`
      - 测试结果：

      2.2.2 分支2：长度为零检测

      - 测试用例：传入 `u16Length = 0`
      - 预期结果：返回 `HALFM_ERROR_E`
      - 测试结果：

      2.2.3 分支3：地址有效性检测

      - 测试用例：传入超出范围的地址或地址+长度超出芯片容量
      - 预期结果：返回 `HALFM_ERROR_E`
      - 测试结果：

      2.2.4 分支4：SPI发送命令失败

      - 测试用例：SPI硬件故障导致命令发送失败
      - 预期结果：返回 `HALFM_ERROR_E`，CS引脚正确恢复
      - 测试结果：

      2.2.5 分支5：SPI接收数据失败

      - 测试用例：SPI硬件故障导致数据接收失败
      - 预期结果：返回 `HALFM_ERROR_E`，CS引脚正确恢复
      - 测试结果：

      2.2.6 分支6：正常读取操作

      - 函数调用：`HalFm_eReadData(0x0000, au8ReadBuffer, 64)`
      - 测试用例：从地址0x0000读取64字节数据
      - 预期结果：返回 `HALFM_OK_E`，读取的数据正确
      - 测试结果：

   2.3 HalFm_eWriteData()

      2.3.1 分支1：空指针检测

      - 测试用例：传入 `pu8Buffer = NULL_D`
   - 预期结果：返回 `HALFM_ERROR_E`
      - 测试结果：
   
      2.3.2 分支2：长度为零检测

      - 测试用例：传入 `u16Length = 0`
   - 预期结果：返回 `HALFM_ERROR_E`
      - 测试结果：
   
      2.3.3 分支3：地址有效性检测

      - 测试用例：传入超出范围的地址或地址+长度超出芯片容量
   - 预期结果：返回 `HALFM_ERROR_E`
      - 测试结果：
   
      2.3.4 分支4：写使能失败

      - 测试用例：SPI通信错误导致写使能命令失败
   - 预期结果：返回 `HALFM_ERROR_E`
      - 测试结果：
   
      2.3.5 分支5：SPI发送写命令失败

      - 测试用例：SPI硬件故障导致写命令发送失败
   - 预期结果：返回 `HALFM_ERROR_E`，CS引脚正确恢复
      - 测试结果：
   
      2.3.6 分支6：SPI发送数据失败

      - 测试用例：SPI硬件故障导致数据发送失败
   - 预期结果：返回 `HALFM_ERROR_E`，CS引脚正确恢复
      - 测试结果：
   
      2.3.7 分支7：正常写入操作（单页）

      - 函数调用：`HalFm_eWriteData(0x0000, au8WriteBuffer, 32)`
   - 测试用例：向地址0x0000写入32字节数据（不跨页）
      - 预期结果：返回 `HALFM_OK_E`，数据正确写入
      - 测试结果：
   
      2.3.8 分支8：正常写入操作（跨页）

      - 函数调用：`HalFm_eWriteData(0x0030, au8WriteBuffer, 64)`
   - 测试用例：从地址0x0030写入64字节数据（跨越页边界）
      - 预期结果：返回 `HALFM_OK_E`，数据正确分页写入
      - 测试结果：
   
   2.4 HalFm_eWriteEnable()

      2.4.1 分支1：SPI发送失败
   
   - 测试用例：SPI硬件故障导致写使能命令发送失败
      - 预期结果：返回 `HALFM_ERROR_E`，CS引脚正确恢复
   - 测试结果：
   
   2.4.2 分支2：正常写使能
   
      - 测试用例：正常SPI通信
      - 预期结果：返回 `HALFM_OK_E`，写使能命令发送成功
   - 测试结果：
   
2.5 HalFm_eIsAddressValid()
   
      2.5.1 分支1：地址溢出检测
   
   - 测试用例：`u16Address = 0x3FF0, u16Length = 32`（总和超过最大地址）
      - 预期结果：返回 `HALFM_ERROR_E`
   - 测试结果：
   
   2.5.2 分支2：地址有效
   
      - 测试用例：`u16Address = 0x1000, u16Length = 256`（在有效范围内）
      - 预期结果：返回 `HALFM_OK_E`
   - 测试结果：

测试覆盖率是100%

***

## 设计的局限性

### 已知Bugs

无

### 未来的改进

无

### 可重用性声明

1. 该模块依赖于STM32 HAL库和GlobalTypes类型定义。
2. 该模块依赖于SPI接口的正确配置和初始化。
3. 该模块特定针对GT25C128A芯片，如需支持其他存储芯片需要相应修改。
