<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>7</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>294</x>
      <y>280</y>
      <w>805</w>
      <h>189</h>
    </coordinates>
    <panel_attributes>KeyHandle
--
+KeyHandle_eInit():KEYHANDLE_STATUS_E
+KeyHandle_vRun():void
-KeyHandle_vProcessStateScan():void
-KeyHandle_vProcessStateDebounce():void
-KeyHandle_vProcessStateDetectSingle():void
-KeyHandle_vProcessStateDetectCombo():void
-KeyHandle_vTriggerSingleKeyShortPressEvent(eSingleKey:const KEYHANDLE_SINGLE_KEY_E):void
-KeyHandle_vTriggerSingleKeyLongPressEvent(eSingleKey:const KEY<PERSON><PERSON>LE_SINGLE_KEY_E):void
-KeyHandle_vTriggerComboKeyShortPressEvent(eComboKey:const KEYHANDLE_COMBO_KEY_E):void
-KeyHandle_vResetState():void
-KeyHandle_bIsComboMatch(psCombo:const KEYHANDLE_COMBO_KEY_MAP_T* const,aeActiveKeys:const KEYHANDLE_SINGLE_KEY_E* const,u8KeyCount:const U8):BOOL
-KeyHandle_bIsKeyPartOfActiveCombo(eSingleKey:const KEYHANDLE_SINGLE_KEY_E):BOOL
-KeyHandle_u32GetTickMs():U32
-KeyHandle_u32GetElapsedTime(u32CurrentTime:const U32,u32StartTime:const U32):U32</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>70</x>
      <y>280</y>
      <w>182</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
KEYHANDLE_STATUS_E
--
+KEYHANDLE_OK_E = 0U
+KEYHANDLE_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>70</x>
      <y>371</y>
      <w>182</w>
      <h>84</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
KEYHANDLE_HANDLE_T
--
+sConfig:KEYHANDLE_CONFIG_T
+sState:KEYHANDLE_STATE_T
+sHalKey:HALKEY_HANDLE_T</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1141</x>
      <y>287</y>
      <w>546</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>HalKey
--
+HalKey_eInit(psHandle:HALKEY_HANDLE_T* const):HALKEY_STATUS_E
+HalKey_eScanMatrix(psHandle:HALKEY_HANDLE_T* const, pu16KeyMatrix:U16* const):HALKEY_STATUS_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>1141</x>
      <y>364</y>
      <w>546</w>
      <h>56</h>
    </coordinates>
    <panel_attributes>Event
--
+Event_ePublish(eEventType:EVENT_EVENT_TYPE_E, pvData:const void* const, u32DataSize:const U32):EVENT_STATUS_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>525</x>
      <y>196</y>
      <w>350</w>
      <h>42</h>
    </coordinates>
    <panel_attributes>FreeRTOS
--
+xTaskGetTickCount(void):TickType_t</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>301</y>
      <w>63</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>70.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>1092</x>
      <y>357</y>
      <w>63</w>
      <h>28</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>70.0;20.0;10.0;20.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>686</x>
      <y>231</y>
      <w>49</w>
      <h>63</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>245</x>
      <y>280</y>
      <w>63</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>245</x>
      <y>378</y>
      <w>63</w>
      <h>21</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;70.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>245</x>
      <y>518</y>
      <w>252</w>
      <h>161</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
KEYHANDLE_SINGLE_KEY_E
--
+KEYHANDLE_SINGLE_KEY_LEFT_FUNC_E = 0U
+KEYHANDLE_SINGLE_KEY_RIGHT_FUNC_E
+KEYHANDLE_SINGLE_KEY_UP_E
+KEYHANDLE_SINGLE_KEY_DOWN_E
+KEYHANDLE_SINGLE_KEY_LEFT_E
+KEYHANDLE_SINGLE_KEY_RIGHT_E
+KEYHANDLE_SINGLE_KEY_START_E
+KEYHANDLE_SINGLE_KEY_STOP_E
+KEYHANDLE_SINGLE_KEY_HELP_E
+KEYHANDLE_SINGLE_KEY_CONTROL_SWITCH_E
+KEYHANDLE_SINGLE_KEY_MAX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>518</x>
      <y>518</y>
      <w>252</w>
      <h>126</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
KEYHANDLE_COMBO_KEY_E
--
+KEYHANDLE_COMBO_KEY_LEFT_RIGHT_E = 0U
+KEYHANDLE_COMBO_KEY_LEFT_UP_E
+KEYHANDLE_COMBO_KEY_LEFT_DOWN_E
+KEYHANDLE_COMBO_KEY_RIGHT_UP_E
+KEYHANDLE_COMBO_KEY_RIGHT_DOWN_E
+KEYHANDLE_COMBO_KEY_LEFT_HELP_E
+KEYHANDLE_COMBO_KEY_MAX_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>791</x>
      <y>518</y>
      <w>364</w>
      <h>77</h>
    </coordinates>
    <panel_attributes>&lt;&lt;struct&gt;&gt;
KEYHANDLE_COMBO_KEY_MAP_T
--
+eComboKey:KEYHANDLE_COMBO_KEY_E
+u8KeyCount:U8
+aeKeys:KEYHANDLE_SINGLE_KEY_E[KEYHANDLE_MAX_COMBO_SIZE_D]</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>364</x>
      <y>462</y>
      <w>21</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>637</x>
      <y>462</y>
      <w>21</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;80.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>938</x>
      <y>462</y>
      <w>21</w>
      <h>70</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;80.0</additional_attributes>
  </element>
</diagram>
