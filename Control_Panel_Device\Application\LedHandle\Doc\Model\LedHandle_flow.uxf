<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>4</zoom_level>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>212</x>
      <y>124</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>184</x>
      <y>144</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Initialize HAL LED</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>212</x>
      <y>128</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>184</x>
      <y>176</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Subscribe 4G Events
Enabled/Disabled/Error</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>212</x>
      <y>160</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>180</x>
      <y>208</y>
      <w>76</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Subscribe Bluetooth Events
Enabled/Disabled/Error</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>212</x>
      <y>192</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>184</x>
      <y>240</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Subscribe WiFi Events
Enabled/Disabled/Error</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>212</x>
      <y>224</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>180</x>
      <y>272</y>
      <w>76</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Subscribe Device Events
Standby/Running/Shutdown</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>212</x>
      <y>256</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>184</x>
      <y>304</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Subscribe Fault Events
Critical/Minor/Warning</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>212</x>
      <y>288</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>212</x>
      <y>344</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>212</x>
      <y>320</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>140</x>
      <y>100</y>
      <w>152</w>
      <h>264</h>
    </coordinates>
    <panel_attributes>LedHandle_vInit
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>392</x>
      <y>124</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>364</x>
      <y>144</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Process Events</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>392</x>
      <y>128</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>364</x>
      <y>176</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Update Blink LEDs</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>392</x>
      <y>160</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>392</x>
      <y>216</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>392</x>
      <y>192</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>320</x>
      <y>100</y>
      <w>152</w>
      <h>136</h>
    </coordinates>
    <panel_attributes>LedHandle_vRun
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>572</x>
      <y>124</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>544</x>
      <y>144</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set 4G LED ON</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>572</x>
      <y>128</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>572</x>
      <y>184</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>572</x>
      <y>160</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>500</x>
      <y>100</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_v4GEnabledCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>752</x>
      <y>124</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>724</x>
      <y>144</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set 4G LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>752</x>
      <y>128</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>752</x>
      <y>184</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>752</x>
      <y>160</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>680</x>
      <y>100</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_v4GDisabledCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>932</x>
      <y>124</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>904</x>
      <y>144</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set 4G LED BLINK</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>932</x>
      <y>128</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>932</x>
      <y>184</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>932</x>
      <y>160</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>860</x>
      <y>100</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_v4GErrorCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>572</x>
      <y>260</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>544</x>
      <y>280</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Bluetooth LED ON</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>572</x>
      <y>264</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>572</x>
      <y>320</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>572</x>
      <y>296</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>500</x>
      <y>236</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vBluetoothEnabledCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>752</x>
      <y>260</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>724</x>
      <y>280</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Bluetooth LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>752</x>
      <y>264</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>752</x>
      <y>320</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>752</x>
      <y>296</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>680</x>
      <y>236</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vBluetoothDisabledCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>932</x>
      <y>260</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>900</x>
      <y>280</y>
      <w>72</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Bluetooth LED BLINK</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>932</x>
      <y>264</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>932</x>
      <y>320</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>932</x>
      <y>296</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>860</x>
      <y>236</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vBluetoothErrorCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>572</x>
      <y>396</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>544</x>
      <y>416</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set WiFi LED ON</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>572</x>
      <y>400</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>572</x>
      <y>456</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>572</x>
      <y>432</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>500</x>
      <y>372</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vWifiEnabledCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>752</x>
      <y>396</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>724</x>
      <y>416</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set WiFi LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>752</x>
      <y>400</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>752</x>
      <y>456</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>752</x>
      <y>432</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>680</x>
      <y>372</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vWifiDisabledCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>932</x>
      <y>396</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>904</x>
      <y>416</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set WiFi LED BLINK</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>932</x>
      <y>400</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>932</x>
      <y>456</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>932</x>
      <y>432</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>860</x>
      <y>372</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vWifiErrorCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>220</x>
      <y>396</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>192</x>
      <y>416</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Status LED ON</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>400</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>220</x>
      <y>456</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>432</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>148</x>
      <y>372</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vDeviceStandbyCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>396</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>372</x>
      <y>416</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Status LED BLINK</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>400</x>
      <y>400</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>456</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>400</x>
      <y>432</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>328</x>
      <y>372</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vDeviceRunningCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>936</x>
      <y>536</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>908</x>
      <y>556</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Status LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>936</x>
      <y>540</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>936</x>
      <y>596</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>936</x>
      <y>572</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>864</x>
      <y>512</y>
      <w>152</w>
      <h>104</h>
    </coordinates>
    <panel_attributes>LedHandle_vDeviceShutdownCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>220</x>
      <y>528</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>192</x>
      <y>548</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Red LED ON</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>532</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>192</x>
      <y>580</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Yellow LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>564</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>220</x>
      <y>620</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>220</x>
      <y>596</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>148</x>
      <y>504</y>
      <w>152</w>
      <h>136</h>
    </coordinates>
    <panel_attributes>LedHandle_vDeviceCriticalFaultCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>528</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>372</x>
      <y>548</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Red LED BLINK</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>400</x>
      <y>532</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>372</x>
      <y>580</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Yellow LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>400</x>
      <y>564</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>400</x>
      <y>620</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>400</x>
      <y>596</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>328</x>
      <y>504</y>
      <w>152</w>
      <h>136</h>
    </coordinates>
    <panel_attributes>LedHandle_vDeviceMinorFaultCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>580</x>
      <y>528</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>552</x>
      <y>548</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Red LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>580</x>
      <y>532</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>552</x>
      <y>580</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Yellow LED BLINK</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>580</x>
      <y>564</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>580</x>
      <y>620</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>580</x>
      <y>596</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>508</x>
      <y>504</y>
      <w>152</w>
      <h>136</h>
    </coordinates>
    <panel_attributes>LedHandle_vDeviceWarningCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>528</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=initial</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>732</x>
      <y>548</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Red LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>532</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLState</id>
    <coordinates>
      <x>732</x>
      <y>580</y>
      <w>64</w>
      <h>20</h>
    </coordinates>
    <panel_attributes>Set Yellow LED OFF</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>564</y>
      <w>12</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;40.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLSpecialState</id>
    <coordinates>
      <x>760</x>
      <y>620</y>
      <w>8</w>
      <h>8</h>
    </coordinates>
    <panel_attributes>type=final</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>760</x>
      <y>596</y>
      <w>12</w>
      <h>32</h>
    </coordinates>
    <panel_attributes>lt=&lt;-</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>688</x>
      <y>504</y>
      <w>152</w>
      <h>136</h>
    </coordinates>
    <panel_attributes>LedHandle_vDeviceNoFaultWarningCallback
valign=top</panel_attributes>
    <additional_attributes/>
  </element>
</diagram>
