//----------------------------------------------------------------------------
/**
* @file Utility.h
* @remark Utility and string functions declaration.(string use lib)
* <AUTHOR>
*
*/
//----------------------------------------------------------------------------

#ifndef UTILITY_H_
#define UTILITY_H_


//-----------------------------------------------------------------------
// Includes:
//-----------------------------------------------------------------------
#include "GlobalTypes.h"
#include <string.h>

//------------------------------------------------------------------------
// Public Definitions:
//------------------------------------------------------------------------


//-----------------------------------------------------------------------
// Public Function Prototypes:
//-----------------------------------------------------------------------
void Utility_vDelayUs(U16 u16Delay);
S16 Utility_s16Round(F32 f32Data);
U32 Utility_u32GetTickMs(void);
#endif /* UTILITY_H_ */


//===========================================================================
// End of file.
//===========================================================================
