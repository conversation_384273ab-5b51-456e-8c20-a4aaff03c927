<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<diagram program="umlet" version="15.1">
  <zoom_level>8</zoom_level>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>384</x>
      <y>360</y>
      <w>712</w>
      <h>120</h>
    </coordinates>
    <panel_attributes>HalFlash
--
+Hal<PERSON>lash_eInit():void
+HalFlash_eReadData(u32Address:const U32, pu8Buffer:U8* const, u16Length:const U16):HALFLASH_STATUS_E
+HalFlash_eWriteData(u32Address:const U32, pu8Buffer:const U8* const, u16Length:const U16):HALFLASH_STATUS_E
-HalFlash_eWriteEnable():HALFLASH_STATUS_E
-HalFlash_eIsAddressValid(u32Address:const U32, u16Length:const U16):HALFLASH_STATUS_E
-Hal<PERSON><PERSON>_eReadStatusRegister(pu8Status:U8* const):HALFLASH_STATUS_E
-HalFlash_eWaitForReady():HALFLASH_STATUS_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>136</x>
      <y>360</y>
      <w>216</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>&lt;&lt;enum&gt;&gt;
HALFLASH_STATUS_E
--
HALFLASH_OK_E = 0
HALFLASH_ERROR_E</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>128</x>
      <y>248</y>
      <w>600</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>HAL_SPI
--
+HAL_SPI_Transmit(hspi:SPI_HandleTypeDef*, pData:uint8_t*, Size:uint16_t, Timeout:uint32_t):HAL_StatusTypeDef
+HAL_SPI_Receive(hspi:SPI_HandleTypeDef*, pData:uint8_t*, Size:uint16_t, Timeout:uint32_t):HAL_StatusTypeDef</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>752</x>
      <y>264</y>
      <w>496</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>HAL_GPIO
--
+HAL_GPIO_WritePin(GPIOx:GPIO_TypeDef*, GPIO_Pin:U16, PinState:GPIO_PinState):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>UMLClass</id>
    <coordinates>
      <x>536</x>
      <y>520</y>
      <w>368</w>
      <h>48</h>
    </coordinates>
    <panel_attributes>osDelay
--
+osDelay(delay:U32):void</panel_attributes>
    <additional_attributes/>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>424</x>
      <y>304</y>
      <w>56</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>992</x>
      <y>304</y>
      <w>56</w>
      <h>72</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;10.0;10.0;70.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>712</x>
      <y>472</y>
      <w>56</w>
      <h>64</h>
    </coordinates>
    <panel_attributes>lt=&lt;.
require</panel_attributes>
    <additional_attributes>10.0;60.0;10.0;10.0</additional_attributes>
  </element>
  <element>
    <id>Relation</id>
    <coordinates>
      <x>344</x>
      <y>360</y>
      <w>56</w>
      <h>24</h>
    </coordinates>
    <panel_attributes>lt=-</panel_attributes>
    <additional_attributes>50.0;10.0;10.0;10.0</additional_attributes>
  </element>
</diagram>
